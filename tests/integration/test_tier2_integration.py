"""Integration tests for Tier 2 commodity-specific analysis."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from yemen_market.models.three_tier.tier2_commodity import (
    CommodityExtractor, CommodityExtractorConfig,
    CommoditySpecificModel, CommodityModelConfig,
    CointegrationTestSuite, CointegrationTestConfig
)


class TestTier2Integration:
    """Test integration of Tier 2 components."""
    
    @pytest.fixture
    def sample_3d_panel(self):
        """Create realistic 3D panel data for testing."""
        # Generate dates
        start_date = datetime(2020, 1, 1)
        dates = [start_date + timedelta(weeks=i) for i in range(104)]  # 2 years
        
        # Markets and commodities
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Ibb']
        commodities = ['wheat', 'rice', 'sugar', 'cooking_oil']
        
        # Generate data with realistic patterns
        data = []
        np.random.seed(42)
        
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    # Base price varies by commodity
                    base_prices = {
                        'wheat': 100,
                        'rice': 150,
                        'sugar': 80,
                        'cooking_oil': 200
                    }
                    
                    # Market premium/discount
                    market_effects = {
                        'Sana\'a': 1.0,
                        'Aden': 1.05,
                        'Taiz': 0.95,
                        'Hodeidah': 1.02,
                        'Ibb': 0.98
                    }
                    
                    # Time trend
                    time_trend = 1 + 0.001 * (date - start_date).days
                    
                    # Generate price with some noise
                    price = (base_prices[commodity] * 
                            market_effects[market] * 
                            time_trend * 
                            (1 + 0.05 * np.random.randn()))
                    
                    # Conflict events (higher in some markets)
                    conflict_base = {'Sana\'a': 20, 'Aden': 30, 'Taiz': 40, 
                                   'Hodeidah': 25, 'Ibb': 15}
                    conflict = np.random.poisson(conflict_base[market])
                    
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': max(price, 50),  # Ensure positive
                        'conflict_events': conflict,
                        'governorate': market,  # For compatibility
                        'usd_price': max(price, 50)
                    })
        
        return pd.DataFrame(data)
    
    def test_full_tier2_pipeline(self, sample_3d_panel):
        """Test complete Tier 2 analysis pipeline."""
        # Step 1: Extract commodities
        extractor_config = CommodityExtractorConfig(
            min_markets=3,
            min_periods=50,
            min_observations=200
        )
        extractor = CommodityExtractor(extractor_config)
        
        # Extract all valid commodities
        commodity_panels = extractor.extract_all_commodities(sample_3d_panel)
        
        # Should extract all 4 commodities with these settings
        assert len(commodity_panels) == 4
        assert set(commodity_panels.keys()) == {'wheat', 'rice', 'sugar', 'cooking_oil'}
        
        # Step 2: Run cointegration tests for wheat
        wheat_data = commodity_panels['wheat']
        
        # Prepare for VECM format
        wheat_wide, _ = extractor.prepare_for_vecm('wheat', wheat_data)
        
        # Run cointegration tests
        coint_suite = CointegrationTestSuite()
        coint_results = coint_suite.run_all_tests(wheat_wide, 'wheat')
        
        # Check results structure
        assert 'commodity' in coint_results
        assert coint_results['commodity'] == 'wheat'
        assert 'tests' in coint_results
        assert 'summary' in coint_results
        
        # Step 3: Fit commodity-specific model
        model_config = CommodityModelConfig(
            model_type='fixed_effects',
            include_trend=True
        )
        
        wheat_model = CommoditySpecificModel('wheat', config=model_config)
        
        # Prepare and fit
        prepared_data = wheat_model.prepare_data(wheat_data)
        
        # Check prepared data structure
        assert isinstance(prepared_data.index, pd.MultiIndex)
        assert 'log_price' in prepared_data.columns
        assert 'trend' in prepared_data.columns
        
        # Step 4: Check extractor statistics
        assert len(extractor.coverage_stats) == 4
        
        for commodity, stats in extractor.coverage_stats.items():
            assert stats['n_markets'] >= 3
            assert stats['n_periods'] >= 50
            assert stats['n_observations'] >= 200
    
    def test_commodity_filtering(self, sample_3d_panel):
        """Test that commodity filtering works correctly."""
        # Create stricter config
        strict_config = CommodityExtractorConfig(
            min_markets=10,  # More than available
            min_periods=100,
            min_observations=1000
        )
        
        extractor = CommodityExtractor(strict_config)
        commodity_panels = extractor.extract_all_commodities(sample_3d_panel)
        
        # Should extract no commodities with these strict settings
        assert len(commodity_panels) == 0
    
    def test_missing_data_handling(self):
        """Test handling of missing data in pipeline."""
        # Create sparse data
        dates = pd.date_range('2020-01-01', periods=20, freq='W')
        
        data = []
        for i, date in enumerate(dates):
            # Only add data for some market-date combinations
            if i % 3 == 0:
                data.append({
                    'date': date,
                    'market': 'Sana\'a',
                    'commodity': 'wheat',
                    'price': 100 + i
                })
            if i % 2 == 0:
                data.append({
                    'date': date,
                    'market': 'Aden',
                    'commodity': 'wheat',
                    'price': 105 + i
                })
        
        sparse_df = pd.DataFrame(data)
        
        # Try to extract with default settings
        extractor = CommodityExtractor()
        commodity_panels = extractor.extract_all_commodities(sparse_df)
        
        # Should not extract wheat due to insufficient data
        assert len(commodity_panels) == 0
    
    def test_cointegration_workflow(self, sample_3d_panel):
        """Test cointegration testing workflow."""
        # Extract wheat data
        wheat_data = sample_3d_panel[sample_3d_panel['commodity'] == 'wheat']
        
        # Pivot to wide format
        wheat_wide = wheat_data.pivot(
            index='date',
            columns='market',
            values='price'
        )
        
        # Configure tests
        config = CointegrationTestConfig(
            test_types=['johansen', 'engle_granger'],
            significance_level=0.05
        )
        
        suite = CointegrationTestSuite(config)
        results = suite.run_all_tests(wheat_wide, 'wheat')
        
        # Check Johansen test
        assert 'johansen' in results['tests']
        johansen = results['tests']['johansen']
        assert 'selected_rank' in johansen
        assert isinstance(johansen['selected_rank'], (int, np.integer))
        
        # Check Engle-Granger test
        assert 'engle_granger' in results['tests']
        eg = results['tests']['engle_granger']
        assert 'n_pairs_tested' in eg
        assert 'n_cointegrated' in eg
        
        # Check summary
        assert results['summary']['tests_performed'] == ['johansen', 'engle_granger']
        assert 'recommendation' in results['summary']
    
    def test_panel_model_workflow(self, sample_3d_panel):
        """Test panel model estimation workflow."""
        # Get rice data
        rice_data = sample_3d_panel[sample_3d_panel['commodity'] == 'rice']
        
        # Create model with various options
        config = CommodityModelConfig(
            model_type='fixed_effects',
            cluster_by='market',
            include_trend=True,
            include_seasonal=True,
            lag_order=1
        )
        
        model = CommoditySpecificModel('rice', config=config)
        
        # Prepare data
        prepared = model.prepare_data(rice_data, exog_vars=['conflict_events'])
        
        # Check all transformations were applied
        assert 'log_price' in prepared.columns
        assert 'log_price_lag1' in prepared.columns
        assert 'trend' in prepared.columns
        assert 'conflict_events' in prepared.columns
        
        # Check seasonal dummies
        month_cols = [col for col in prepared.columns if col.startswith('month_')]
        assert len(month_cols) == 11  # 11 dummies for months 2-12
    
    def test_error_handling(self):
        """Test error handling in Tier 2 components."""
        # Empty DataFrame
        empty_df = pd.DataFrame()
        
        extractor = CommodityExtractor()
        result = extractor.extract_all_commodities(empty_df)
        assert len(result) == 0
        
        # Missing required columns
        bad_df = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'value': range(10)  # Missing required columns
        })
        
        with pytest.raises(ValueError) as exc_info:
            extractor.extract_all_commodities(bad_df)
        assert "missing required columns" in str(exc_info.value).lower()