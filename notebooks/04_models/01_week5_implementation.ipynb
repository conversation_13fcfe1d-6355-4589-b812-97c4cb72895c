# Setup
import sys
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from IPython.display import display, Markdown
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path (more robust)
project_root = Path.cwd().parent.parent
sys.path.insert(0, str(project_root))

# Import our modules
from yemen_market.utils.logging import setup_logging
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM
from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery
from yemen_market.diagnostics.tests.pre_estimation import (
    test_unit_roots_battery,
    test_cointegration,
    test_gregory_hansen_cointegration
)

# Setup logging
setup_logging("week5_notebook")

# Set style
plt.style.use('seaborn-v0_8-darkgrid')
plt.rcParams['figure.figsize'] = (12, 6)
plt.rcParams['font.size'] = 11

# Load integrated panel
panel_path = Path("../data/processed/panels/integrated_panel.parquet")

if not panel_path.exists():
    print("❌ Integrated panel not found!")
    print("Please run: python scripts/run_data_pipeline.py")
else:
    panel_df = pd.read_parquet(panel_path)
    print(f"✅ Loaded panel data: {panel_df.shape}")
    print(f"Date range: {panel_df['date'].min()} to {panel_df['date'].max()}")
    print(f"\nColumns: {list(panel_df.columns)}")

from yemen_market.models.three_tier.integration import ThreeTierAnalysis
from yemen_market.models.three_tier.core import PanelDataHandler, ResultsContainer
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery
from yemen_market.utils.logging import get_logger, setup_logging
import pandas as pd
import numpy as np
from pathlib import Path

# Visualize price dynamics and conflict intensity
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 8), sharex=True)

# Average prices across markets
avg_prices = wheat_houthi.groupby('date')['price_usd'].mean()
price_std = wheat_houthi.groupby('date')['price_usd'].std()

# Plot prices with confidence band
ax1.plot(avg_prices.index, avg_prices.values, 'b-', linewidth=2, label='Average price')
ax1.fill_between(avg_prices.index, 
                 avg_prices - price_std, 
                 avg_prices + price_std, 
                 alpha=0.2, color='blue')
ax1.set_ylabel('Price (USD)', fontsize=12)
ax1.set_title('Wheat Prices in Houthi-Controlled Markets', fontsize=14)
ax1.grid(True, alpha=0.3)
ax1.legend()

# Conflict intensity
conflict = wheat_houthi.groupby('date')['conflict_intensity'].mean()
ax2.plot(conflict.index, conflict.values, 'r-', linewidth=2)
ax2.axhline(y=50, color='k', linestyle='--', linewidth=2, label='Threshold (50 events)')
ax2.fill_between(conflict.index, 0, conflict.values, 
                 where=(conflict.values > 50), color='red', alpha=0.3, label='High conflict')
ax2.fill_between(conflict.index, 0, conflict.values, 
                 where=(conflict.values <= 50), color='green', alpha=0.3, label='Low conflict')
ax2.set_ylabel('Conflict events/month', fontsize=12)
ax2.set_xlabel('Date', fontsize=12)
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.suptitle('Price Dynamics and Conflict Intensity', fontsize=16)
plt.tight_layout()
plt.show()

# Summary statistics by regime
wheat_houthi['high_conflict'] = wheat_houthi['conflict_intensity'] > 50
print("\nSummary by Conflict Regime:")
print(wheat_houthi.groupby('high_conflict')['price_usd'].agg(['mean', 'std', 'count']))

# Prepare data for testing
# Get top 5 markets by data availability
top_markets = wheat_houthi.groupby('market_name').size().nlargest(5).index.tolist()
print(f"Selected markets for analysis: {top_markets}")

# Create price matrix
price_matrix = wheat_houthi[wheat_houthi['market_name'].isin(top_markets)].pivot(
    index='date',
    columns='market_name',
    values='price_usd'
)

# Convert to log prices
log_prices = np.log(price_matrix)
log_prices = log_prices.dropna()

print(f"\nPrice matrix shape: {log_prices.shape}")
print(f"Date range: {log_prices.index[0]} to {log_prices.index[-1]}")

# Unit root tests
print("=" * 60)
print("UNIT ROOT TESTS")
print("=" * 60)

unit_root_results = test_unit_roots_battery(
    {'log_prices': log_prices, 'markets': top_markets}
)

print(f"\nTest: {unit_root_results.test_name}")
print(f"Result: {unit_root_results.interpretation}")
print(f"Passed: {'✅' if unit_root_results.passed else '❌'}")

# Show details
if 'ADF_nonstationary_markets' in unit_root_results.details:
    print(f"\nNon-stationary markets (I(1)): {unit_root_results.details['ADF_nonstationary_markets']}")

# Cointegration tests
print("\n" + "=" * 60)
print("COINTEGRATION TESTS")
print("=" * 60)

# Standard Johansen test
johansen_result = test_cointegration(
    {'log_prices': log_prices, 'markets': top_markets}
)

print(f"\nJohansen Test:")
print(f"Result: {johansen_result.interpretation}")
print(f"Selected rank: {johansen_result.details.get('selected_rank', 0)}")
print(f"Passed: {'✅' if johansen_result.passed else '❌'}")

# Gregory-Hansen test with structural break
gh_result = test_gregory_hansen_cointegration(
    {'log_prices': log_prices, 'markets': top_markets},
    test_type='c'
)

print(f"\nGregory-Hansen Test (with structural break):")
print(f"Result: {gh_result.interpretation}")
if 'break_date' in gh_result.details:
    print(f"Break date: {gh_result.details['break_date']}")
print(f"Passed: {'✅' if gh_result.passed else '❌'}")

# Decision
if johansen_result.details.get('selected_rank', 0) == 0 and gh_result.passed:
    print("\n⚠️ Structural break masks cointegration in standard test!")
    print("Consider separate analysis for pre/post break periods.")

# Initialize Track 2 model
print("=" * 60)
print("TRACK 2: SIMPLE THRESHOLD VECM")
print("=" * 60)

model_t2 = SimpleThresholdVECM(
    threshold=50,                      # Initial value (will be estimated)
    threshold_variable='conflict_intensity',
    n_coint=1,                        # From cointegration test
    n_lags=2,                         # Standard for monthly data
    trim_pct=0.15,                    # Trim 15% on each side
    n_boot=1000                       # Bootstrap replications
)

print("Model initialized with:")
print(f"  Initial threshold: {model_t2.threshold}")
print(f"  Cointegration rank: {model_t2.n_coint}")
print(f"  Number of lags: {model_t2.n_lags}")
print(f"  Bootstrap replications: {model_t2.n_boot}")

# Fit the model with threshold estimation
print("\nEstimating model (this may take a minute due to bootstrap)...")

model_t2.fit(
    data=wheat_houthi,
    estimate_threshold=True,   # Estimate optimal threshold
    price_col='price_usd'
)

# Extract results
results_t2 = model_t2.vecm_results

print("\n" + "=" * 40)
print("THRESHOLD ESTIMATION RESULTS")
print("=" * 40)
print(f"Estimated threshold: {results_t2.threshold_value:.1f} events/month")
print(f"Bootstrap p-value: {results_t2.threshold_p_value:.4f}")
print(f"95% Confidence interval: [{results_t2.threshold_ci_lower:.1f}, {results_t2.threshold_ci_upper:.1f}]")
print(f"\nThreshold effect significant: {'YES ✅' if results_t2.threshold_p_value < 0.05 else 'NO ❌'}")

# Configure three-tier analysis
config = {
    'tier1': {
        'entity_effects': True,
        'time_effects': True,
        'cluster_var': 'governorate'
    },
    'tier2': {
        'threshold_var': 'conflict_intensity',
        'n_regimes': 2,
        'min_obs_per_regime': 20
    },
    'tier3': {
        'n_factors': 3,
        'rolling_window': 12
    }
}

# Initialize and run analysis
analysis = ThreeTierAnalysis(config)
results = analysis.run_full_analysis(panel_data)

# Display tier 1 results
logger.info("Tier 1 - Pooled Panel Results:")
tier1_results = results.get_tier_results(1)
if tier1_results:
    logger.info(f"Number of entities: {tier1_results.summary_stats.get('n_entities', 'N/A')}")
    logger.info(f"Total observations: {tier1_results.summary_stats.get('n_obs', 'N/A')}")

# Display adjustment speeds
if results_t2.low_regime_alpha is not None and results_t2.high_regime_alpha is not None:
    print("\n" + "=" * 40)
    print("ADJUSTMENT SPEEDS BY REGIME")
    print("=" * 40)
    
    # Create comparison dataframe
    adjustment_df = pd.DataFrame({
        'Market': model_t2.data['markets'][:len(results_t2.low_regime_alpha)],
        'Low Conflict α': results_t2.low_regime_alpha[:, 0],
        'High Conflict α': results_t2.high_regime_alpha[:, 0],
        'Difference': results_t2.high_regime_alpha[:, 0] - results_t2.low_regime_alpha[:, 0]
    })
    
    display(adjustment_df)
    
    print(f"\nAverage adjustment speeds:")
    print(f"  Low conflict: {np.mean(results_t2.low_regime_alpha):.4f}")
    print(f"  High conflict: {np.mean(results_t2.high_regime_alpha):.4f}")
    print(f"  Ratio: {abs(np.mean(results_t2.high_regime_alpha)/np.mean(results_t2.low_regime_alpha)):.1f}x slower in high conflict")
    
    if regime_analysis['alpha_diff_significant']:
        print("\n✅ Adjustment speeds are SIGNIFICANTLY different between regimes")
    else:
        print("\n❌ No significant difference in adjustment speeds")

# Display tier 2 results
logger.info("\nTier 2 - Commodity-Specific Results:")
tier2_results = results.get_tier_results(2)
if tier2_results:
    for commodity, comm_results in tier2_results.items():
        if isinstance(comm_results, dict) and 'threshold' in comm_results:
            logger.info(f"\n{commodity}:")
            logger.info(f"  Threshold: {comm_results.get('threshold', 'N/A')}")
            logger.info(f"  Regimes: {comm_results.get('n_regimes', 'N/A')}")

# Fit the Bayesian model
print("\nRunning MCMC sampling (this will take 3-5 minutes)...")
print("Note: Progress bar may not display in notebook")

try:
    model_t1.fit(
        data=wheat_houthi,
        conflict_col='conflict_intensity',
        price_col='price_usd'
    )
    
    # Check convergence
    results_t1 = model_t1.vecm_results
    
    print("\n" + "=" * 40)
    print("MCMC CONVERGENCE DIAGNOSTICS")
    print("=" * 40)
    
    if results_t1.rhat:
        max_rhat = max(results_t1.rhat.values())
        min_ess = min(results_t1.ess.values()) if results_t1.ess else 0
        
        print(f"Max R-hat: {max_rhat:.3f} (target < 1.01)")
        print(f"Min ESS: {min_ess:.0f} (target > 400)")
        
        if results_t1.converged:
            print("\n✅ Model converged successfully!")
        else:
            print("\n⚠️ Convergence issues detected - results may be unreliable")
    
    # Information criteria
    if results_t1.waic:
        print(f"\nWAIC: {results_t1.waic:.1f}")
    if results_t1.loo:
        print(f"LOO: {results_t1.loo:.1f}")
        
except Exception as e:
    print(f"\n❌ Bayesian model failed: {str(e)}")
    print("This is often due to PyMC installation issues or convergence problems.")
    print("Continuing with Track 2 results only.")
    model_t1 = None

# Analyze conflict impact (if model succeeded)
if model_t1 is not None and model_t1.is_fitted:
    conflict_impact = model_t1.analyze_conflict_impact()
    
    print("\n" + "=" * 40)
    print("CONFLICT IMPACT ANALYSIS")
    print("=" * 40)
    print(f"Average |α_diff| between regimes: {conflict_impact['avg_alpha_diff']:.4f}")
    print(f"Markets with significant differences: {conflict_impact['n_significant_differences']}")
    
    if conflict_impact['significant']:
        print("\n✅ Conflict regimes have SIGNIFICANTLY different adjustment speeds")
    else:
        print("\n❌ No significant differences between conflict regimes")
    
    # Show posterior means
    if 'alpha_low' in results_t1.posterior_means:
        print("\nPosterior mean adjustment speeds:")
        print(f"  Low conflict: {np.mean(results_t1.posterior_means['alpha_low']):.4f}")
        print(f"  High conflict: {np.mean(results_t1.posterior_means['alpha_high']):.4f}")

# Compare models if both succeeded
if model_t1 is not None and model_t1.is_fitted:
    print("=" * 60)
    print("MODEL COMPARISON: TRACK 1 vs TRACK 2")
    print("=" * 60)
    
    # Extract parameters
    t1_alpha_low = np.mean(results_t1.posterior_means.get('alpha_low', []))
    t1_alpha_high = np.mean(results_t1.posterior_means.get('alpha_high', []))
    t2_alpha_low = np.mean(results_t2.low_regime_alpha) if results_t2.low_regime_alpha is not None else np.nan
    t2_alpha_high = np.mean(results_t2.high_regime_alpha) if results_t2.high_regime_alpha is not None else np.nan
    
    # Create comparison table
    comparison_df = pd.DataFrame({
        'Model': ['Track 1 (Bayesian)', 'Track 2 (Threshold)'],
        'Low Conflict α': [t1_alpha_low, t2_alpha_low],
        'High Conflict α': [t1_alpha_high, t2_alpha_high],
        'Difference': [t1_alpha_high - t1_alpha_low, t2_alpha_high - t2_alpha_low],
        'Ratio': [t1_alpha_high/t1_alpha_low if t1_alpha_low != 0 else np.nan,
                  t2_alpha_high/t2_alpha_low if t2_alpha_low != 0 else np.nan]
    })
    
    display(comparison_df)
    
    # Calculate correlation
    if not np.isnan(t1_alpha_low) and not np.isnan(t2_alpha_low):
        param_corr = np.corrcoef(
            [t1_alpha_low, t1_alpha_high], 
            [t2_alpha_low, t2_alpha_high]
        )[0, 1]
        print(f"\nParameter correlation between models: {param_corr:.3f}")
        
        if param_corr > 0.8:
            print("✅ Models show STRONG agreement - use simpler Track 2 for policy")
        elif param_corr > 0.5:
            print("⚠️ Models show moderate agreement")
        else:
            print("❌ Models diverge - investigate further")
    
    # Visualize comparison
    fig, ax = plt.subplots(figsize=(10, 6))
    
    models = ['Track 1\n(Bayesian)', 'Track 2\n(Threshold)']
    low_speeds = [t1_alpha_low, t2_alpha_low]
    high_speeds = [t1_alpha_high, t2_alpha_high]
    
    x = np.arange(len(models))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, low_speeds, width, label='Low conflict', color='lightgreen')
    bars2 = ax.bar(x + width/2, high_speeds, width, label='High conflict', color='lightcoral')
    
    ax.set_ylabel('Adjustment speed (α)', fontsize=12)
    ax.set_title('Adjustment Speeds by Model and Regime', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels(models)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')
    
    # Add value labels on bars
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.3f}',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3),  # 3 points vertical offset
                       textcoords="offset points",
                       ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
else:
    print("\n⚠️ Track 1 model not available for comparison")
    print("Using Track 2 results for analysis")

# Run diagnostic battery on Track 2 model
print("=" * 60)
print("DIAGNOSTIC TESTS - TRACK 2")
print("=" * 60)

battery_t2 = DiagnosticTestBattery(model_t2)
results = battery_t2.run_all_tests(
    skip_categories=['robustness', 'validation'],  # Skip slow tests
    include_slow=False
)

# Display summary
print("\nTest Summary:")
for category, tests in results.items():
    if tests:
        print(f"\n{category.upper()}:")
        for test in tests:
            status = "✅" if test.passed else "❌"
            print(f"  {status} {test.test_name}: {test.interpretation}")

# Summarize key findings
print("=" * 60)
print("KEY FINDINGS")
print("=" * 60)

# 1. Threshold
print(f"\n1. CONFLICT THRESHOLD")
print(f"   - Estimated threshold: {results_t2.threshold_value:.0f} events/month")
print(f"   - Statistically significant: {'YES' if results_t2.threshold_p_value < 0.05 else 'NO'}")
print(f"   - Confidence interval: [{results_t2.threshold_ci_lower:.0f}, {results_t2.threshold_ci_upper:.0f}]")

# 2. Market integration
if results_t2.low_regime_alpha is not None and results_t2.high_regime_alpha is not None:
    slow_factor = abs(np.mean(results_t2.high_regime_alpha) / np.mean(results_t2.low_regime_alpha))
    print(f"\n2. MARKET INTEGRATION")
    print(f"   - Integration {1/slow_factor:.0f}x faster in low conflict")
    print(f"   - Half-life to equilibrium:")
    print(f"     * Low conflict: {np.log(0.5)/np.mean(results_t2.low_regime_alpha):.1f} months")
    print(f"     * High conflict: {np.log(0.5)/np.mean(results_t2.high_regime_alpha):.1f} months")

# 3. Regime persistence
print(f"\n3. REGIME DYNAMICS")
print(f"   - Markets stay in low conflict for ~{regime_analysis['expected_low_duration']:.0f} months on average")
print(f"   - Markets stay in high conflict for ~{regime_analysis['expected_high_duration']:.0f} months on average")
print(f"   - Current proportion in high conflict: {results_t2.n_obs_high/results_t2.n_obs*100:.0f}%")

# 4. Policy implications
print(f"\n4. POLICY IMPLICATIONS")
print(f"   - Reducing conflict below {results_t2.threshold_value:.0f} events/month would:")
print(f"     * Restore normal market integration speeds")
print(f"     * Reduce price volatility")
print(f"     * Improve food security")
print(f"   - Exchange rate unification could further enhance integration")
print(f"   - Infrastructure investments most effective in low-conflict periods")

# Save summary
summary_path = Path("../reports/week5_summary.txt")
summary_path.parent.mkdir(parents=True, exist_ok=True)

with open(summary_path, 'w') as f:
    f.write("YEMEN MARKET INTEGRATION - WEEK 5 RESULTS\n")
    f.write("=" * 40 + "\n\n")
    f.write(f"Conflict threshold: {results_t2.threshold_value:.0f} events/month\n")
    f.write(f"Threshold p-value: {results_t2.threshold_p_value:.4f}\n")
    f.write(f"Market integration {1/slow_factor:.0f}x slower in high conflict\n")
    f.write(f"Model agreement: {'Strong' if 'param_corr' in locals() and param_corr > 0.8 else 'Track 2 only'}\n")
    
print(f"\n📄 Summary saved to {summary_path}")

