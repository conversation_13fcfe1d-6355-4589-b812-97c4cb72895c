{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tier 1 Pooled Panel Analysis Example\n", "\n", "This notebook demonstrates the application of the Tier 1 pooled panel analysis for the Yemen Market Integration project. It covers data loading, model estimation, fixed effects extraction, robust standard error calculation (Driscoll-Kraay), results interpretation, and visualization."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports\n", "\n", "Import necessary libraries and project-specific modules."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Python Standard Libraries\n", "import os\n", "import sys\n", "from pathlib import Path\n", "\n", "# Data Handling and Numerical Computation\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Econometric Modeling\n", "import statsmodels.api as sm\n", "from linearmodels.panel import PanelOLS\n", "from linearmodels.panel.data import PanelData # For type hinting if needed\n", "\n", "# Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Project-specific modules\n", "# Ensure the src directory is in the Python path\n", "try:\n", "    project_root = Path(os.getcwd()).resolve().parent.parent # Assumes notebook is in notebooks/04_models/\n", "    src_path = project_root / 'src'\n", "    if str(src_path) not in sys.path:\n", "        sys.path.append(str(src_path))\n", "    from yemen_market.utils.logging import setup_logging, info, warning, error, timer\n", "    from yemen_market.models.three_tier.tier1_pooled.fixed_effects import FixedEffectsExtractor # If needed beyond PanelOLS\n", "    from yemen_market.models.three_tier.tier1_pooled.standard_errors import StandardErrorCorrector\n", "    # Setup logging\n", "    setup_logging(module_name='notebook_tier1_example')\n", "    info('Libraries imported and logging configured.')\n", "except ImportError as e:\n", "    print(f\"Error importing project modules: {e}. Ensure PYTHONPATH is set correctly or adjust project_root.\")\n", "except Exception as e:\n", "    print(f\"An unexpected error occurred during setup: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading and Preparation\n", "\n", "Load the `integrated_panel.parquet` dataset and prepare it for panel analysis. This includes setting the correct multi-index for `PanelOLS`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths (adjust if your notebook is elsewhere or project_root needs change)\n", "df_panel = None # Initialize df_panel\n", "try:\n", "    data_path = project_root / 'data' / 'processed' / 'integrated_panel.parquet'\n", "    # Load data\n", "    with timer('Loading data'):\n", "        df_panel = pd.read_parquet(data_path)\n", "        info(f'Successfully loaded data from {data_path}. Shape: {df_panel.shape}')\n", "        # Display basic info and a sample\n", "        print(df_panel.info())\n", "        print(df_panel.head())\n", "except NameError: # If project_root was not defined due to import error\n", "    error('project_root is not defined. Cannot load data. Please check setup cell.')\n", "except FileNotFoundError:\n", "    error(f'Data file not found at {data_path}. Please ensure the file exists and path is correct.')\n", "except Exception as e:\n", "    error(f'An error occurred during data loading: {e}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1. Define Variables and Set Index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df_panel is not None:\n", "    try:\n", "        # Define dependent and independent variables\n", "        dependent_var = 'ln_price_normalized_iqr' # EXAMPLE: Replace with your actual variable\n", "        exog_vars = ['ln_lag1_price_normalized_iqr', 'conflict_dummy'] # EXAMPLE: Add other relevant exogenous variables\n", "        \n", "        # Define entity and time identifiers\n", "        entity_id_components = ['market_id', 'commodity_id'] # Columns to create the combined entity ID\n", "        entity_id_col = 'market_commodity_id' # Name of the combined entity ID column\n", "        time_id_col = 'month_year' # EXAMPLE: Replace with your actual time column\n", "        \n", "        # Create market_commodity_id if it doesn't exist\n", "        if entity_id_col not in df_panel.columns:\n", "            df_panel[entity_id_col] = df_panel[entity_id_components[0]].astype(str) + '_' + df_panel[entity_id_components[1]].astype(str)\n", "            info(f\"'{entity_id_col}' created.\")\n", "    \n", "        # Ensure time_id_col is datetime if it's not already\n", "        if not pd.api.types.is_datetime64_any_dtype(df_panel[time_id_col]):\n", "            df_panel[time_id_col] = pd.to_datetime(df_panel[time_id_col])\n", "            info(f\"'{time_id_col}' converted to datetime.\")\n", "    \n", "        # Set panel data index\n", "        df_panel_indexed = df_panel.set_index([entity_id_col, time_id_col])\n", "        info(f'Panel data indexed with {entity_id_col} and {time_id_col}.')\n", "    \n", "        # Handle potential missing values (example: drop rows with NaNs in key variables)\n", "        vars_to_check_for_nan = [dependent_var] + exog_vars\n", "        original_rows = len(df_panel_indexed)\n", "        df_panel_indexed = df_panel_indexed.dropna(subset=vars_to_check_for_nan)\n", "        rows_dropped = original_rows - len(df_panel_indexed)\n", "        if rows_dropped > 0:\n", "            info(f'Dropped {rows_dropped} rows due to NaNs in key variables: {vars_to_check_for_nan}.')\n", "        info(f'Final panel data shape for modeling: {df_panel_indexed.shape}')\n", "        \n", "        # Verify no duplicate entries in index for PanelOLS\n", "        if df_panel_indexed.index.has_duplicates:\n", "            warning(\"Panel index contains duplicate entries. This can cause issues with PanelOLS.\")\n", "            # Optional: df_panel_indexed = df_panel_indexed[~df_panel_indexed.index.duplicated(keep='first')]\n", "            # info(\"Attempted to remove duplicates, keeping the first occurrence.\")\n", "            \n", "    except KeyError as e:\n", "        error(f\"A specified column was not found: {e}. Please check your variable names.\")\n", "        df_panel_indexed = None # Prevent further execution if error\n", "    except Exception as e:\n", "        error(f\"An error occurred during variable definition or indexing: {e}\")\n", "        df_panel_indexed = None # Prevent further execution if error\n", "else:\n", "    warning('df_panel is None, skipping variable definition and indexing.')\n", "    df_panel_indexed = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Pooled Panel Model Estimation (Tier 1)\n", "\n", "Run a PanelOLS model with entity (market-commodity) and time fixed effects."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["initial_results = None # Initialize\n", "if df_panel_indexed is not None and not df_panel_indexed.empty:\n", "    try:\n", "        # Define the model formula\n", "        X = sm.add_constant(df_panel_indexed[exog_vars]) # Add constant for intercept\n", "        y = df_panel_indexed[dependent_var]\n", "        \n", "        # Initialize PanelOLS model with two-way fixed effects\n", "        with timer('Estimating PanelOLS model'):\n", "            model = PanelOLS(y, X, entity_effects=True, time_effects=True)\n", "            initial_results = model.fit() # Default SEs initially\n", "        \n", "        info('Initial PanelOLS model estimated.')\n", "        print(\"\\n--- Initial Model Results (Before Robust SE) ---\")\n", "        print(initial_results)\n", "\n", "    except Exception as e:\n", "        error(f'Error during PanelOLS model estimation: {e}')\n", "else:\n", "    warning('Panel data not prepared or is empty, skipping model estimation.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Fixed Effects Extraction\n", "\n", "Extract and examine the estimated fixed effects. `PanelOLS` results directly provide `estimated_effects`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["entity_effects = None\n", "time_effects = None\n", "if initial_results is not None:\n", "    try:\n", "        with timer('Extracting fixed effects from PanelOLS results'):\n", "            # PanelOLS results object has .estimated_effects attribute\n", "            fixed_effects_series = initial_results.estimated_effects # This is a Series\n", "            \n", "            # Separate entity and time effects by checking index names or types\n", "            # The index of fixed_effects_series contains unique entity IDs and time periods\n", "            unique_entities_in_data = df_panel_indexed.index.get_level_values(entity_id_col).unique()\n", "            unique_times_in_data = df_panel_indexed.index.get_level_values(time_id_col).unique()\n", "            \n", "            entity_effects = fixed_effects_series[fixed_effects_series.index.isin(unique_entities_in_data)]\n", "            time_effects = fixed_effects_series[fixed_effects_series.index.isin(unique_times_in_data)]\n", "            \n", "        info('Fixed effects extracted.')\n", "        if not entity_effects.empty:\n", "            print(\"\\n--- Entity Fixed Effects (Sample) ---\")\n", "            print(entity_effects.head())\n", "            print(f\"Total entity effects: {len(entity_effects)}\")\n", "        else:\n", "            warning(\"No entity effects were extracted. Check model specification or data.\")\n", "        \n", "        if not time_effects.empty:\n", "            print(\"\\n--- Time Fixed Effects (Sample) ---\")\n", "            print(time_effects.head())\n", "            print(f\"Total time effects: {len(time_effects)}\")\n", "        else:\n", "            warning(\"No time effects were extracted. Check model specification or data.\")\n", "\n", "    except Exception as e:\n", "        error(f'Error during fixed effects extraction: {e}')\n", "else:\n", "    warning('Initial model results not available, skipping fixed effects extraction.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>-Kraay Standard Errors\n", "\n", "Apply <PERSON><PERSON><PERSON><PERSON> (DK) standard errors to account for potential cross-sectional and temporal dependence."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dk_results = None # Initialize\n", "if initial_results is not None:\n", "    try:\n", "        se_corrector = StandardErrorCorrector(model_results=initial_results)\n", "        with timer('Applying Driscoll-Kraay SEs'):\n", "            # max_lags can be chosen based on theory or data (e.g., T**(1/4) or a fixed number like 12 for monthly data over a few years)\n", "            dk_results = se_corrector.get_driscoll_kraay_se(kernel='bartlett', max_lags=12) # Example max_lags\n", "        \n", "        if dk_results:\n", "            info('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> standard errors applied.')\n", "            print(\"\\n--- Model Results with Dr<PERSON>ll-Kraay SEs ---\")\n", "            print(dk_results)\n", "        else:\n", "            warning('Failed to obtain Driscoll-Kraay SEs. Original results will be used for interpretation.')\n", "            dk_results = initial_results # Fallback to initial results if DK fails\n", "\n", "    except Exception as e:\n", "        error(f'Error applying <PERSON><PERSON><PERSON>-<PERSON>raay SEs: {e}')\n", "        dk_results = initial_results # Fallback in case of error\n", "else:\n", "    warning('Initial model results not available, skipping DK SE calculation.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Results Interpretation\n", "\n", "Discuss the model coefficients, significance (p-values), R-squared, and the implications of the fixed effects and robust standard errors."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if dk_results is not None:\n", "    print(\"\\n### Interpreting Model Coefficients (with DK SEs if available):\")\n", "    for param_name in exog_vars + ['const']: # Check exogenous variables and constant\n", "        if param_name in dk_results.params:\n", "            coef = dk_results.params[param_name]\n", "            pval = dk_results.pvalues[param_name]\n", "            print(f\"\\nParameter: {param_name}\")\n", "            print(f\"  Coefficient: {coef:.4f}\")\n", "            print(f\"  P-value: {pval:.4f}\")\n", "            if pval < 0.05:\n", "                print(f\"  Statistically significant at the 5% level.\")\n", "            else:\n", "                print(f\"  Not statistically significant at the 5% level.\")\n", "            # Add specific interpretation based on the variable\n", "            if param_name == 'ln_lag1_price_normalized_iqr':\n", "                 print(f\"  Interpretation: A 1% increase in the lagged normalized price is associated with a {coef:.2f}% change in the current normalized price, ceteris paribus.\")\n", "            elif param_name == 'conflict_dummy':\n", "                 print(f\"  Interpretation: The presence of conflict (dummy=1) is associated with a {coef:.2f} unit change in {dependent_var}, ceteris paribus.\")\n", "        else:\n", "            print(f\"\\nParameter {param_name} not found in results.\")\n", "    \n", "    print(\"\\n### Model Fit:\")\n", "    try:\n", "        print(f\"R-squared: {dk_results.rsquared:.4f}\")\n", "        print(f\"R-squared Between: {dk_results.rsquared_between:.4f}\")\n", "        print(f\"R-squared Within: {dk_results.rsquared_within:.4f}\")\n", "        print(f\"R-squared Overall: {dk_results.rsquared_overall:.4f}\")\n", "        print(f\"\\nInterpretation: The R-squared within ({dk_results.rsquared_within:.4f}) indicates that approximately {dk_results.rsquared_within*100:.2f}% of the variation in '{dependent_var}' *within* each market-commodity unit over time is explained by the model.\")\n", "    except AttributeError:\n", "        warning(\"Some R-squared measures might not be available in the results object.\")\n", "    print(\"\\n(Further detailed model fit interpretation to be added here based on specific research questions.)\")\n", "\n", "else:\n", "    warning('Model results (dk_results) not available for interpretation.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualizing Fixed Effects\n", "\n", "Create simple visualizations for a subset of the estimated fixed effects (e.g., top/bottom market-commodity effects or time trends)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if entity_effects is not None and not entity_effects.empty:\n", "    try:\n", "        n_effects_to_show = 10\n", "        sorted_entity_effects = entity_effects.sort_values()\n", "        \n", "        top_n = sorted_entity_effects.tail(n_effects_to_show)\n", "        bottom_n = sorted_entity_effects.head(n_effects_to_show)\n", "        \n", "        plt.figure(figsize=(14, 7))\n", "        sns.barplot(x=top_n.index, y=top_n.values, palette='viridis_r') # Use _r for reversed viridis\n", "        plt.xticks(rotation=45, ha='right')\n", "        plt.title(f'Top {n_effects_to_show} Entity ({entity_id_col}) Fixed Effects (Higher {dependent_var})')\n", "        plt.ylabel('Fixed Effect Value')\n", "        plt.xlabel(entity_id_col)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        plt.figure(figsize=(14, 7))\n", "        sns.barplot(x=bottom_n.index, y=bottom_n.values, palette='viridis')\n", "        plt.xticks(rotation=45, ha='right')\n", "        plt.title(f'Bottom {n_effects_to_show} Entity ({entity_id_col}) Fixed Effects (Lower {dependent_var})')\n", "        plt.ylabel('Fixed Effect Value')\n", "        plt.xlabel(entity_id_col)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    except Exception as e:\n", "        error(f\"Error during entity fixed effects visualization: {e}\")\n", "else:\n", "    warning('Entity fixed effects not available for visualization.')\n", "\n", "if time_effects is not None and not time_effects.empty:\n", "    try:\n", "        plt.figure(figsize=(14, 7))\n", "        time_effects_sorted = time_effects.sort_index() # Ensure time_effects index is sorted if it's datetime\n", "        plt.plot(time_effects_sorted.index, time_effects_sorted.values, marker='o', linestyle='-')\n", "        plt.title(f'Time Fixed Effects Over Time ({time_id_col})')\n", "        plt.ylabel('Fixed Effect Value')\n", "        plt.xlabel(f'Time Period ({time_id_col})')\n", "        plt.xticks(rotation=45, ha='right')\n", "        plt.grid(True, linestyle='--', alpha=0.7)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    except Exception as e:\n", "        error(f\"Error during time fixed effects visualization: {e}\")\n", "else:\n", "    warning('Time fixed effects not available for visualization.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusion\n", "\n", "Summary of the Tier 1 analysis process and findings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["info('Tier 1 Pooled Panel Analysis Example Notebook execution complete.')\n", "# Add concluding remarks here, e.g., summarizing key insights or next steps.\n", "print(\"\\nThis notebook provided a walkthrough of Tier 1 pooled panel analysis.\")\n", "print(\"Key steps included data prep, PanelOLS estimation, fixed effects extraction, and DK SE application.\")\n", "print(\"Interpretations and visualizations should be customized based on the specific model output and research context.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9"}}, "nbformat": 4, "nbformat_minor": 5}