{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Three-Tier Methodology Migration Example\n", "\n", "This notebook demonstrates how to migrate from the old dual-track approach to the new three-tier methodology.\n", "\n", "**Key Changes:**\n", "- Old: `track1_complex` + `track2_simple`\n", "- New: Integrated `three_tier` framework with cross-validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Add project root to path\n", "sys.path.insert(0, str(Path.cwd().parent))\n", "\n", "# Configure plotting\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "sns.set_palette('husl')\n", "\n", "# Setup logging\n", "from yemen_market.utils.logging import setup_logging\n", "setup_logging('INFO')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON> Approach (Deprecated)\n", "\n", "Previously, we would run track1 and track2 models separately:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OLD APPROACH - DO NOT USE\n", "# from yemen_market.models.track2_simple import ThresholdVECM\n", "# from yemen_market.models.track1_complex import TVP_VECM\n", "\n", "# This will now raise an ImportError with migration instructions\n", "try:\n", "    from yemen_market.models import track2_simple\n", "except ImportError as e:\n", "    print(f\"Expected error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. New Approach - Three-Tier Methodology\n", "\n", "The new approach provides an integrated framework:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NEW APPROACH - RECOMMENDED\n", "from yemen_market.models.three_tier.integration import ThreeTierAnalysis\n", "from yemen_market.models.three_tier.migration import ModelMigrationHelper\n", "\n", "print(\"✓ Three-tier models imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data (example with synthetic data)\n", "dates = pd.date_range('2019-01-01', periods=200, freq='W')\n", "markets = ['Sana\\'a', 'Aden', 'Taiz', 'Hodeidah']\n", "commodities = ['wheat', 'rice', 'sugar']\n", "\n", "# Create sample panel data\n", "data = []\n", "for date in dates:\n", "    for market in markets:\n", "        for commodity in commodities:\n", "            price = 100 + np.random.randn() * 10\n", "            if date > '2020-01-01' and market == 'Sana\\'a':\n", "                price *= 1.1  # Price shock\n", "            \n", "            data.append({\n", "                'date': date,\n", "                'governorate': market,  # Note: new column name\n", "                'commodity': commodity,\n", "                'usd_price': price      # Note: new column name\n", "            })\n", "\n", "df = pd.DataFrame(data)\n", "print(f\"Panel data shape: {df.shape}\")\n", "print(f\"Markets: {df['governorate'].nunique()}\")\n", "print(f\"Commodities: {df['commodity'].nunique()}\")\n", "print(f\"Time periods: {df['date'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Configure Three-Tier Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration for three-tier analysis\n", "config = {\n", "    'tier1_config': {\n", "        'fixed_effects': ['entity', 'time'],\n", "        'cluster_var': 'entity',\n", "        'dris<PERSON><PERSON>_kraay': True\n", "    },\n", "    'tier2_config': {\n", "        'min_observations': 50,\n", "        'test_thresholds': True,\n", "        'max_lags': 4\n", "    },\n", "    'tier3_config': {\n", "        'n_factors': 3,\n", "        'standardize': True,\n", "        'min_variance_explained': 0.8\n", "    },\n", "    'output_dir': 'results/three_tier_demo'\n", "}\n", "\n", "print(\"Configuration set for all three tiers\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Run Three-Tier Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize and run analysis\n", "analysis = ThreeTierAnalysis(config)\n", "\n", "# Run all three tiers\n", "results = analysis.run_full_analysis(df)\n", "\n", "print(\"\\n✓ Three-tier analysis complete!\")\n", "print(f\"\\nResults keys: {list(results.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Access Results from Each Tier\n", "\n", "### Tier 1: Pooled Panel Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tier 1 results\n", "if results['tier1']:\n", "    tier1 = results['tier1']\n", "    print(\"Tier 1: Pooled Panel Analysis\")\n", "    print(\"=\" * 40)\n", "    \n", "    if hasattr(tier1, 'comparison_metrics'):\n", "        print(f\"R-squared: {tier1.comparison_metrics.r_squared:.4f}\")\n", "        print(f\"Observations: {tier1.metadata.get('n_observations', 'N/A')}\")\n", "    \n", "    # Show coefficients\n", "    if hasattr(tier1, 'get_coefficients_table'):\n", "        coef_table = tier1.get_coefficients_table()\n", "        print(\"\\nCoefficients:\")\n", "        print(coef_table.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tier 2: Commodity-Specific Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tier 2 results\n", "if results['tier2']:\n", "    print(\"\\nTier 2: Commodity-Specific Analysis\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Get commodity comparison\n", "    commodity_df = analysis.get_commodity_comparison()\n", "    if not commodity_df.empty:\n", "        print(commodity_df)\n", "        \n", "        # Visualize commodity differences\n", "        if 'r_squared' in commodity_df.columns:\n", "            fig, ax = plt.subplots(figsize=(8, 5))\n", "            commodity_df.set_index('commodity')['r_squared'].plot(kind='bar', ax=ax)\n", "            ax.set_title('R-squared by Commodity')\n", "            ax.set_ylabel('R-squared')\n", "            plt.xticks(rotation=45)\n", "            plt.tight_layout()\n", "            plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tier 3: Validation Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tier 3 results\n", "if results['tier3']:\n", "    print(\"\\nTier 3: Validation Analysis\")\n", "    print(\"=\" * 40)\n", "    \n", "    # PCA integration results\n", "    if 'pca_integration' in results['tier3']:\n", "        pca_results = results['tier3']['pca_integration']\n", "        if hasattr(pca_results, 'tier_specific'):\n", "            overall = pca_results.tier_specific.get('overall_integration', {})\n", "            print(f\"Integration level: {overall.get('integration_level', 'N/A')}\")\n", "            print(f\"PC1 variance: {overall.get('pc1_variance_explained', 0):.1%}\")\n", "    \n", "    # Factor model results\n", "    if 'static_factors' in results['tier3']:\n", "        factor_results = results['tier3']['static_factors']\n", "        if hasattr(factor_results, 'tier_specific'):\n", "            var_explained = factor_results.tier_specific.get('variance_explained', [])\n", "            if var_explained:\n", "                # Plot variance explained\n", "                fig, ax = plt.subplots(figsize=(8, 5))\n", "                factors = range(1, len(var_explained) + 1)\n", "                ax.bar(factors, var_explained)\n", "                ax.set_xlabel('Factor')\n", "                ax.set_ylabel('Variance Explained')\n", "                ax.set_title('Variance Explained by Each Factor')\n", "                plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Cross-Tier Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check cross-tier consistency\n", "if 'cross_validation' in results:\n", "    print(\"\\nCross-Tier Validation\")\n", "    print(\"=\" * 40)\n", "    \n", "    cv = results['cross_validation']\n", "    \n", "    # Integration consistency\n", "    if 'integration_consistency' in cv:\n", "        consistency = cv['integration_consistency']\n", "        print(f\"All tiers consistent: {consistency.get('all_consistent', False)}\")\n", "        \n", "        if 'measures' in consistency:\n", "            measures = consistency['measures']\n", "            print(\"\\nIntegration measures:\")\n", "            for measure, value in measures.items():\n", "                print(f\"  {measure}: {value:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. <PERSON><PERSON> Helper for Old Results\n", "\n", "If you have results from the old methodology:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Migrate old configuration\n", "old_config = {\n", "    'track1_config': {\n", "        'tvp_vecm': {'n_factors': 3, 'lags': 2}\n", "    },\n", "    'track2_config': {\n", "        'threshold_vecm': {'max_lags': 4}\n", "    }\n", "}\n", "\n", "# Use migration helper\n", "migrator = ModelMigrationHelper()\n", "new_config = migrator.migrate_configuration(old_config)\n", "\n", "print(\"Migrated configuration:\")\n", "for tier, config in new_config.items():\n", "    if isinstance(config, dict):\n", "        print(f\"\\n{tier}:\")\n", "        for key, value in config.items():\n", "            print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Generate Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Access the summary\n", "if 'summary' in results:\n", "    summary = results['summary']\n", "    \n", "    print(\"\\nAnalysis Summary\")\n", "    print(\"=\" * 40)\n", "    print(f\"Markets analyzed: {summary['overview']['n_markets']}\")\n", "    print(f\"Commodities: {summary['overview']['n_commodities']}\")\n", "    print(f\"Observations: {summary['overview']['n_observations']}\")\n", "    \n", "    if summary['key_findings']:\n", "        print(\"\\nKey Findings:\")\n", "        for finding in summary['key_findings']:\n", "            print(f\"- {finding}\")\n", "    \n", "    if summary['recommendations']:\n", "        print(\"\\nRecommendations:\")\n", "        for rec in summary['recommendations']:\n", "            print(f\"- {rec}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Key Differences Summary\n", "\n", "### Old Approach:\n", "- Separate track1/track2 models\n", "- Manual coordination between models\n", "- Limited cross-validation\n", "- Column names: `market`, `price_usd`\n", "\n", "### New Approach:\n", "- Integrated three-tier framework\n", "- Automatic cross-tier validation\n", "- Standardized results format\n", "- Column names: `governorate`, `usd_price`\n", "- Better handling of 3D panel structure\n", "\n", "### Migration Tips:\n", "1. Update column names in your data\n", "2. Use `ThreeTierAnalysis` instead of individual models\n", "3. Access results through tier-specific keys\n", "4. Use migration helper for old configs/results"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}