# Three-Tier Methodology Notebooks

This directory contains <PERSON><PERSON><PERSON> notebooks demonstrating the three-tier econometric methodology for Yemen market integration analysis.

## 📚 Notebook Overview

### Core Implementation Notebooks

1. **`01_three_tier_implementation.ipynb`** ⚠️
   - Original implementation (needs updating to use new framework)
   - Shows complete three-tier workflow

2. **`01_week5_implementation.ipynb`** ⚠️
   - Week 5 implementation (needs updating)
   - Historical reference

### Tier-Specific Examples

3. **`02_tier1_pooled_example.ipynb`** ✅
   - **Purpose**: Demonstrates Tier 1 pooled panel analysis
   - **Features**:
     - Multi-way fixed effects (entity and time)
     - Clustered standard errors
     - Driscoll-Kraay corrections
   - **Key Outputs**: Average effects across all markets and commodities

4. **`04_tier2_commodity_example.ipynb`** ✅
   - **Purpose**: Shows Tier 2 commodity-specific analysis
   - **Features**:
     - Threshold detection for each commodity
     - Regime-specific models
     - Commodity comparison
   - **Key Outputs**: Commodity-specific integration patterns

5. **`05_tier3_validation_example.ipynb`** ✅
   - **Purpose**: Illustrates Tier 3 validation techniques
   - **Features**:
     - Static factor analysis (PCA)
     - Rolling integration metrics
     - Conflict event validation
     - Market clustering
   - **Key Outputs**: Common factors and validation results

### Migration & Integration

6. **`03_three_tier_migration_example.ipynb`** ✅
   - **Purpose**: Guide for migrating from old to new methodology
   - **Features**:
     - Side-by-side comparison of old vs new approaches
     - Migration helper usage
     - Common pitfalls and solutions
   - **Key Outputs**: Migration patterns and best practices

## 🎯 Recommended Learning Path

1. **Start Here**: `03_three_tier_migration_example.ipynb`
   - Understand the differences between old and new approaches
   - Learn the new API structure

2. **Tier by Tier**:
   - `02_tier1_pooled_example.ipynb` - Pooled analysis basics
   - `04_tier2_commodity_example.ipynb` - Commodity-specific patterns
   - `05_tier3_validation_example.ipynb` - Validation techniques

3. **Full Integration**: Review updated `01_three_tier_implementation.ipynb` (when available)

## 📊 Data Requirements

All notebooks expect data with these columns:
- `date`: datetime
- `governorate`: str (market location)
- `commodity`: str
- `usd_price`: float

Optional columns:
- `conflict_intensity`: float (for threshold analysis)
- `fatalities`: int (for conflict validation)

## 🔧 Running the Notebooks

1. **Setup Environment**:
   ```bash
   cd /path/to/yemen-market-integration
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Launch Jupyter**:
   ```bash
   jupyter notebook notebooks/04_models/
   ```

3. **Run in Order**: Each notebook is self-contained but builds on concepts from previous ones

## 📈 Key Concepts Demonstrated

### Tier 1: Pooled Panel
- Handling 3D panel structure (market × commodity × time)
- Entity-time fixed effects
- Robust standard error corrections

### Tier 2: Commodity-Specific
- Panel extraction by commodity
- Threshold effect detection
- Regime-switching models

### Tier 3: Validation
- Factor extraction from price matrix
- Integration strength measurement
- External shock validation

## 🚨 Important Notes

1. **Memory Usage**: Large panels may require chunking or sampling
2. **Computation Time**: Factor models can be slow with many series
3. **Missing Data**: Handled automatically but affects results

## 📝 Creating New Notebooks

When creating new analysis notebooks:

1. Use the three-tier import structure:
   ```python
   from yemen_market.models.three_tier.integration import ThreeTierAnalysis
   ```

2. Follow the configuration pattern:
   ```python
   config = {
       'tier1_config': {...},
       'tier2_config': {...},
       'tier3_config': {...}
   }
   ```

3. Access results through standardized interface:
   ```python
   results['tier1'].comparison_metrics.r_squared
   ```

## 🔗 Related Resources

- [Three-Tier API Documentation](../../docs/api/models/three_tier/README.md)
- [Migration Guide](../../MIGRATION_GUIDE.md)
- [Methodology Paper](../../docs/models/yemen_panel_methodology.md)

---

*Last Updated: January 2025*