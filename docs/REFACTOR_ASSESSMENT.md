# Refactoring Assessment: From Dual-Track to Three-Tier

**Date**: January 2025  
**Assessor**: <PERSON> (Project Manager perspective)

## 🔍 Code Audit Results

### What We Have (Existing Code)

#### 1. **Base Infrastructure** ✅ Salvageable

- `models/base.py`: BaseEconometricModel class
  - Good foundation for model interface
  - Needs adaptation for 3D panels
  - Can extend for our needs

#### 2. **Track 1 Complex** ❌ Not Compatible

- `track1_complex/tvp_vecm.py`: Bayesian TVP-VECM
  - Too complex for our needs
  - PyMC-based, we need linearmodels
  - **Decision**: Archive, don't reuse

- `track1_complex/spatial_network.py`: Spatial weights
  - Some spatial concepts useful
  - But overengineered for current needs
  - **Decision**: Reference only

#### 3. **Track 2 Simple** ⚠️ Partially Reusable

- `track2_simple/threshold_vecm.py`:
  - <PERSON> (1999) implementation valuable
  - Threshold testing logic can adapt
  - Bootstrap procedures useful
  - **Decision**: Extract threshold logic for Tier 2

#### 4. **World Bank Files** ⚠️ Cherry-pick

- `worldbank_threshold_vecm.py`:
  - Good diagnostic structure
  - Standard error calculations
  - **Decision**: Extract diagnostics

#### 5. **Supporting Infrastructure** ✅ Keep

- `model_comparison.py`: Can adapt for three tiers
- `diagnostics/` folder: Valuable test battery
- `utils/logging.py`: Essential, keep as-is

### What We Need (New Development)

#### Critical New Components

1. **3D Panel Handler**
   - Entity creation (market-commodity pairs)
   - Panel reshaping utilities
   - Missing data strategies

2. **Fixed Effects Engine**
   - Multi-way demeaning
   - Within transformation
   - Memory-efficient operations

3. **linearmodels Integration**
   - PanelOLS wrapper
   - Standard error options
   - Result standardization

4. **Factor Analysis Module**
   - PCA implementation
   - Dynamic factor models
   - Conflict correlation

## 📊 Reuse vs Rewrite Analysis

### Reuse Directly (Copy with minimal changes)

- Base model interface patterns
- Logging utilities
- Data validation functions
- Test structure

### Adapt/Extract (Significant modifications)

- Threshold testing from track2_simple
- Diagnostic battery structure
- Bootstrap procedures
- Result containers

### Rewrite Completely (Fresh implementation)

- All Tier 1 pooled panel code
- Panel data transformations
- Factor analysis (Tier 3)
- Integration layer

### Archive (Keep for reference only)

- Bayesian TVP-VECM
- Complex spatial networks
- Old runner scripts

## 🎯 Recommended Approach

### Phase 1 Actions (Immediate)

```python
# 1. Create parallel structure (don't break existing)
src/yemen_market/models/three_tier/
├── __init__.py
├── core/
│   ├── base.py          # Adapted from existing
│   └── panel_handler.py # New
├── tier1/
│   └── pooled_panel.py  # New
├── tier2/
│   └── threshold.py     # Adapted from track2
└── tier3/
    └── factors.py       # New
```

### Migration Strategy

1. **Week 1**: Build alongside existing
2. **Week 2**: Validate results match
3. **Week 3**: Update notebooks/scripts
4. **Week 4**: Archive old code

### Code Salvage Estimates

- **Direct reuse**: ~20% (utilities, base patterns)
- **Adapt/modify**: ~30% (threshold, diagnostics)
- **New development**: ~50% (panel handling, factors)

## 💡 Key Insights

### Do's

- Start with clean namespace (three_tier/)
- Test early and often
- Keep old code until new is proven
- Document all design decisions

### Don'ts

- Don't try to retrofit old Bayesian code
- Don't break existing functionality
- Don't optimize prematurely
- Don't skip testing

## 📝 Decision Summary

**Approach**: Parallel development with selective code salvage

**Rationale**:

- Reduces risk of breaking existing code
- Allows A/B testing of results
- Clear migration path
- Maintains project momentum

**First Step**: Create `panel_handler.py` to solve our 3D → 2D transformation needs

---

This assessment informs our implementation plan and helps set realistic expectations for the refactoring effort.
