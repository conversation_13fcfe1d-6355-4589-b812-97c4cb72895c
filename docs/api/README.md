# API Reference

This directory contains API documentation for the Yemen Market Integration package.

## Core Modules

### Data Processing (`yemen_market.data`)

- **[HDXClient](./hdx_client.md)**: Interface for downloading data from Humanitarian Data Exchange
- **[WFPProcessor](./wfp_processor.md)**: Processes World Food Programme price data
- **[ACAPSProcessor](./acaps_processor.md)**: Processes ACAPS control zone shapefiles
- **[ACLEDProcessor](./acled_processor.md)**: Processes ACLED conflict event data
- **[SpatialJoiner](./spatial_joiner.md)**: Performs spatial joins between markets and zones
- **[PanelBuilder](./panel_builder.md)**: Builds integrated panel datasets

### Feature Engineering (`yemen_market.features`)

- **[FeatureEngineer](./feature_engineer.md)**: Main class for creating analysis features

### Econometric Models (`yemen_market.models`) - Week 5-6

- **[Base Classes](./models/base.md)**: Abstract base classes and common functionality
- **[Bayesian TVP-VECM](./models/track1_complex/tvp_vecm.md)**: Time-varying parameter model
- **[Threshold VECM](./models/track2_simple/threshold_vecm.md)**: Simple threshold model
- **[Spatial Network](./models/track1_complex/spatial_network.md)**: Spatial weight matrices
- **[Model Comparison](./models/model_comparison.md)**: Framework for comparing models
- **[Full Models Documentation](./models/README.md)**: Complete models reference

### Diagnostics (`yemen_market.diagnostics`)

- **[Test Battery](./diagnostics/test_battery.md)**: Comprehensive diagnostic framework
- **[Pre-Estimation Tests](./diagnostics/tests/pre_estimation.md)**: Unit root and cointegration

### Utilities (`yemen_market.utils`)

- **[Enhanced Logging](./logging.md)**: Context-aware logging system with timers and progress tracking

### Configuration (`yemen_market.config`)

- **[Settings](./settings.md)**: Project configuration and constants

## Quick Examples

### Processing WFP Data

```python
from yemen_market.data import WFPProcessor

processor = WFPProcessor(min_market_coverage=0.5)
commodity_prices, exchange_rates = processor.process_price_data()
smart_panel = processor.create_smart_panels(commodity_prices)
```

### Building Panels

```python
from yemen_market.data import PanelBuilder

builder = PanelBuilder()
integrated_panel = builder.build_integrated_panel()
threshold_panel = builder.build_threshold_panel()
```

### Feature Engineering

```python
from yemen_market.features import FeatureEngineer

engineer = FeatureEngineer()
features = engineer.fit_transform(panel_data)
```

### Econometric Modeling (Week 5-6)

```python
# Track 2: Simple Threshold VECM
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM

model = SimpleThresholdVECM(threshold_variable='conflict_intensity')
model.fit(panel_data, estimate_threshold=True)
print(f"Threshold: {model.vecm_results.threshold_value:.1f}")

# Track 1: Bayesian TVP-VECM
from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM

bayesian_model = BayesianTVPVECM(n_samples=2000, n_chains=4)
bayesian_model.fit(panel_data)
```

### Running Diagnostics

```python
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery

battery = DiagnosticTestBattery(model)
results = battery.run_all_tests()
battery.generate_report('diagnostics.md')
```

## Module Documentation

Detailed documentation for each module is available in the linked files above.
