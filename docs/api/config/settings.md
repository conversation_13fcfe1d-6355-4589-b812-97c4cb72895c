# Configuration Settings API Reference

The `settings` module contains all project configuration constants and paths.

## Module: yemen_market.config.settings

```python
from yemen_market.config.settings import (
    PROJECT_ROOT, DATA_DIR, RAW_DATA_DIR, 
    PROCESSED_DATA_DIR, ANALYSIS_CONFIG
)
```

## Directory Paths

### Project Structure

```python
PROJECT_ROOT      # Root directory of the project
DATA_DIR          # Main data directory
SRC_DIR           # Source code directory
TESTS_DIR         # Tests directory
NOTEBOOKS_DIR     # Jupyter notebooks directory
REPORTS_DIR       # Reports and outputs directory
LOGS_DIR          # Log files directory
```

### Data Directories

```python
RAW_DATA_DIR       # data/raw/ - Original, immutable data
INTERIM_DATA_DIR   # data/interim/ - Intermediate transformations
PROCESSED_DATA_DIR # data/processed/ - Final processed data
EXTERNAL_DATA_DIR  # data/external/ - External reference data
```

### Output Directories

```python
FIGURES_DIR        # reports/figures/ - Generated plots
TABLES_DIR         # reports/tables/ - Generated tables
MODELS_DIR         # models/ - Saved model files
```

## Analysis Configuration

### ANALYSIS_CONFIG

Main configuration dictionary for analysis parameters.

```python
ANALYSIS_CONFIG = {
    'start_date': '2019-01-01',
    'end_date': '2024-12-31',
    'commodities': [
        'Wheat', 'Wheat flour', 'Rice (imported)', 'Sugar',
        'Oil (vegetable)', 'Beans (kidney red)', 'Salt',
        'Fuel (diesel)', 'Fuel (petrol-gasoline)'
    ],
    'buffer_km': 50,  # Buffer for spatial matching
    'threshold_events': 50,  # High conflict threshold
    'min_market_coverage': 0.3  # Minimum commodity coverage
}
```

### Accessing Configuration

```python
from yemen_market.config.settings import ANALYSIS_CONFIG

# Get specific values
start_date = ANALYSIS_CONFIG['start_date']
commodities = ANALYSIS_CONFIG['commodities']
buffer_km = ANALYSIS_CONFIG.get('buffer_km', 50)  # With default
```

## Environment Variables

The module supports environment variables for sensitive data:

```python
# ACLED API credentials
ACLED_API_KEY = os.getenv('ACLED_API_KEY')
ACLED_API_EMAIL = os.getenv('ACLED_API_EMAIL')

# HDX configuration
HDX_API_KEY = os.getenv('HDX_API_KEY')
HDX_SITE = os.getenv('HDX_SITE', 'prod')  # 'prod' or 'test'
```

### Setting Environment Variables

```bash
# .env file
ACLED_API_KEY=your_key_here
ACLED_API_EMAIL=<EMAIL>

# Or export directly
export ACLED_API_KEY=your_key_here
```

## Data File Patterns

### Raw Data Files

```python
# WFP data pattern
WFP_PATTERN = "wfp_food_prices_*.csv"

# ACAPS pattern  
ACAPS_PATTERN = "*Yemen Analysis Hub - Areas of control.zip"

# ACLED pattern
ACLED_PATTERN = "acled_yemen_events_*.csv"
```

### Processed Data Files

```python
# Panel data files
INTEGRATED_PANEL = PROCESSED_DATA_DIR / 'panels/integrated_panel.parquet'
SMART_PANEL = PROCESSED_DATA_DIR / 'wfp_smart_panel.parquet'
CONFLICT_METRICS = PROCESSED_DATA_DIR / 'conflict/conflict_metrics.parquet'

# Spatial data files
MARKET_ZONES = PROCESSED_DATA_DIR / 'spatial/market_zones_temporal.parquet'
BOUNDARY_MARKETS = PROCESSED_DATA_DIR / 'spatial/boundary_markets.csv'
```

## Logging Configuration

```python
LOG_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s %(filename)s:%(lineno)d: %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': LOGS_DIR / 'yemen_market.log',
            'formatter': 'detailed'
        }
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO'
    }
}
```

## Example Usage

```python
from yemen_market.config.settings import (
    RAW_DATA_DIR, PROCESSED_DATA_DIR, ANALYSIS_CONFIG
)

# Construct file paths
wfp_raw = RAW_DATA_DIR / 'hdx/wfp-food-prices-for-yemen/wfp_food_prices_202505.csv'
panel_output = PROCESSED_DATA_DIR / 'panels/integrated_panel.parquet'

# Use configuration
start_date = ANALYSIS_CONFIG['start_date']
commodities = ANALYSIS_CONFIG['commodities']

# Check if paths exist
if not wfp_raw.exists():
    print(f"WFP data not found at {wfp_raw}")

# Create directories if needed
panel_output.parent.mkdir(parents=True, exist_ok=True)
```

## Extending Configuration

To add new configuration:

1. **Add to settings.py**:

   ```python
   # New analysis parameter
   ANALYSIS_CONFIG['new_parameter'] = 'value'
   
   # New directory
   NEW_DATA_DIR = DATA_DIR / 'new_data'
   NEW_DATA_DIR.mkdir(exist_ok=True)
   ```

2. **Use in code**:

   ```python
   from yemen_market.config.settings import ANALYSIS_CONFIG
   
   new_param = ANALYSIS_CONFIG.get('new_parameter', 'default')
   ```

3. **Document in this file** for other developers
