# SpatialJoiner API Reference

The `SpatialJoiner` class performs spatial joins between markets and control zones, identifying boundary markets and calculating distances.

## Class: SpatialJoiner

```python
from yemen_market.data import SpatialJoiner

joiner = SpatialJoiner()
```

### Methods

#### load_market_data()

Load market location data with coordinates.

```python
markets_gdf = joiner.load_market_data(market_data_path=None)
```

**Parameters:**

- **market_data_path** (str, optional): Path to market data file

**Returns:**

- **markets_gdf** (GeoDataFrame): Markets with point geometries

#### load_control_zones()

Load control zone polygons.

```python
zones_gdf = joiner.load_control_zones(control_zone_path=None)
```

**Parameters:**

- **control_zone_path** (str, optional): Path to control zone data

**Returns:**

- **zones_gdf** (GeoDataFrame): Control zones with polygon geometries

#### map_markets_to_zones()

Map markets to their control zones using spatial join.

```python
market_zones = joiner.map_markets_to_zones(
    markets_gdf=None,
    zones_gdf=None,
    save_output=True
)
```

**Parameters:**

- **markets_gdf** (GeoDataFrame, optional): Market locations
- **zones_gdf** (GeoDataFrame, optional): Control zone polygons
- **save_output** (bool): Save results to disk

**Returns:**

- **market_zones** (DataFrame): Markets with assigned control zones

#### identify_boundary_markets()

Identify markets near control zone boundaries.

```python
boundary_markets = joiner.identify_boundary_markets(
    market_zones_gdf,
    threshold_km=20
)
```

**Parameters:**

- **market_zones_gdf** (GeoDataFrame): Markets with zones assigned
- **threshold_km** (float): Distance threshold in kilometers

**Returns:**

- **boundary_markets** (DataFrame): Markets near boundaries

#### calculate_market_distances()

Calculate distances between markets and to zone boundaries.

```python
distances = joiner.calculate_market_distances(
    markets_gdf,
    zones_gdf=None
)
```

**Parameters:**

- **markets_gdf** (GeoDataFrame): Market locations
- **zones_gdf** (GeoDataFrame, optional): Control zones

**Returns:**

- **distances** (DataFrame): Distance matrix between markets

#### create_temporal_market_zones()

Create time-varying market-zone mappings.

```python
temporal_zones = joiner.create_temporal_market_zones(
    markets_gdf=None,
    monthly_zones_path=None
)
```

**Parameters:**

- **markets_gdf** (GeoDataFrame, optional): Market locations
- **monthly_zones_path** (str, optional): Path to monthly zone data

**Returns:**

- **temporal_zones** (DataFrame): Market-zone mappings over time

### Spatial Calculations

```python
# Distance calculations
- distance_to_boundary   # Distance to nearest zone boundary (km)
- distance_to_other_zone # Distance to nearest different zone (km)
- nearest_other_zone     # ID of nearest different control zone

# Market classification
- is_boundary_market     # True if within threshold of boundary
- control_zone          # Assigned control zone
- zone_confidence       # Confidence in zone assignment
```

### Example Usage

```python
from yemen_market.data import SpatialJoiner

# Initialize joiner
joiner = SpatialJoiner()

# Perform spatial join
market_zones = joiner.map_markets_to_zones()

# Identify boundary markets
boundary_markets = joiner.identify_boundary_markets(
    market_zones,
    threshold_km=20  # 20km from boundaries
)

# Create temporal mappings
temporal = joiner.create_temporal_market_zones()

# Check results
print(f"Mapped {len(market_zones)} markets")
print(f"Found {len(boundary_markets)} boundary markets")
print(f"Zone distribution:")
print(market_zones['control_zone'].value_counts())
```

### Output Files

- **market_zones_current.parquet**: Current market-zone mappings
- **market_zones_temporal.parquet**: Historical mappings
- **boundary_markets.csv**: List of boundary markets
- **market_zone_distances.parquet**: Distance calculations
- **spatial_join_metadata.json**: Processing metadata

### Coordinate Reference Systems

- Input coordinates: WGS84 (EPSG:4326)
- Distance calculations: Yemen TM (EPSG:2090)
- Automatic reprojection for accurate distance measurements

### Quality Checks

- Validates market coordinates
- Checks for markets outside any zone
- Logs zone assignment statistics
- Identifies potential data issues
