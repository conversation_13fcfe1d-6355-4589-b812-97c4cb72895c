# ACAPSProcessor API Reference

The `ACAPSProcessor` class handles ACAPS control zone data, extracting temporal control information from shapefiles.

## Class: ACAPSProcessor

```python
from yemen_market.data import ACAPSProcessor

processor = ACAPSProcessor()
```

### Methods

#### process_control_zones()

Process ACAPS control zone data from a shapefile or ZIP.

```python
gdf = processor.process_control_zones(file_path, extract_nested=True)
```

**Parameters:**

- **file_path** (str): Path to shapefile or ZIP file
- **extract_nested** (bool): Handle nested ZIP files (default: True)

**Returns:**

- **gdf** (GeoDataFrame): Processed control zones with standardized columns

#### extract_date_from_filename()

Extract date from ACAPS filename format.

```python
date, formatted = processor.extract_date_from_filename(filename)
```

**Parameters:**

- **filename** (str): File name to parse

**Returns:**

- **date** (datetime): Extracted date
- **formatted** (str): Formatted date string (YYYY-MM-DD)

#### process_all_acaps_files()

Process all ACAPS files in a directory.

```python
all_zones = processor.process_all_acaps_files(directory_path)
```

**Parameters:**

- **directory_path** (str): Directory containing ACAPS files

**Returns:**

- **all_zones** (DataFrame): Combined control zones from all files

#### create_monthly_control_zones()

Create monthly control zone summaries.

```python
monthly_zones = processor.create_monthly_control_zones(all_zones_df)
```

**Parameters:**

- **all_zones_df** (DataFrame): Combined zones from all dates

**Returns:**

- **monthly_zones** (DataFrame): Monthly aggregated control data

### Control Group Mappings

```python
CONTROL_GROUP_MAPPING = {
    'Defacto Authorities (DFA)': 'DFA',
    'De-facto authorities': 'DFA',
    'DFA': 'DFA',
    'Internationally Recognized Government (IRG)': 'IRG',
    'Intl Recognized Govt': 'IRG',
    'IRG': 'IRG',
    'Southern Transitional Council (STC)': 'STC',
    'STC': 'STC',
    'Al-Qaeda in the Arabian Peninsula (AQAP)': 'AQAP',
    'AQAP': 'AQAP'
}
```

### Example Usage

```python
from yemen_market.data import ACAPSProcessor

# Initialize processor
processor = ACAPSProcessor()

# Process single file
zones = processor.process_control_zones('path/to/acaps_zones.zip')

# Process all files in directory
all_zones = processor.process_all_acaps_files('data/raw/acaps/')

# Create monthly summaries
monthly = processor.create_monthly_control_zones(all_zones)

# Check results
print(f"Processed {len(zones)} zones")
print(f"Control groups: {zones['controlling_group'].unique()}")
```

### Data Processing Pipeline

1. **Extract shapefiles** from ZIP archives (handles nested ZIPs)
2. **Standardize column names** across different ACAPS formats
3. **Map control groups** to standard abbreviations
4. **Add temporal information** from filenames
5. **Validate geometries** and fix invalid ones
6. **Aggregate to monthly** summaries for analysis

### Output Columns

- **governorate**: Governorate name
- **district**: District name  
- **controlling_group**: Standardized control group (DFA, IRG, STC, AQAP)
- **date**: Date of control data
- **year_month**: Period for monthly aggregation
- **geometry**: Spatial polygon geometry

### Error Handling

- Handles multiple shapefile formats within ZIPs
- Validates geometries and attempts repairs
- Provides detailed logging for debugging
- Gracefully handles missing or corrupted files
