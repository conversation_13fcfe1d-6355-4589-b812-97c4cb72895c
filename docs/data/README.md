# Data Documentation

This folder contains comprehensive documentation for the Yemen Market Integration data pipeline, sources, and final datasets.

## Documentation Structure

### 📊 [Data Sources](./data_sources.md)

Complete documentation of all data sources including:

- World Food Programme (WFP) price data
- ACAPS areas of control
- ACLED conflict events
- Administrative boundaries and pcodes
- Access methods and update schedules

### 🔧 [Detailed Pipeline](./data_pipeline_detailed.md)

Step-by-step documentation of the data processing pipeline:

- Data download procedures
- Individual processing steps for each source
- Spatial integration methods
- Panel construction logic
- Quality assurance checks

### 🚨 [Issues and Solutions](./issues_and_solutions.md)

Comprehensive log of challenges encountered and solutions implemented:

- ACAPS nested ZIP structure
- Commodity name mismatches
- Missing coordinates issue
- Governorate name standardization
- Structural missing prices problem
- API access configuration

### 📁 [Final Dataset Description](./final_dataset_description.md)

Detailed description of output datasets including:

- Variable definitions
- Coverage statistics
- Usage guidelines
- File formats and access methods

## Quick Reference

### Key Improvements Implemented

1. **Pcode-Based Matching**: Standardized governorate names to official Yemen pcodes
2. **Smart Panel Creation**: Only includes commodity-market pairs that exist in reality
3. **Enhanced Coverage**: Improved from 62% to 88.4% price coverage
4. **Conflict Integration**: Added 2,016 market-month conflict observations

### Final Dataset Statistics

- **Markets**: 28 (all major cities)
- **Commodities**: 23 available, 8 core items in smart panel
- **Time Period**: 2019-01 to 2024-12 (72 months)
- **Total Observations**: 14,208 in smart panel
- **Price Coverage**: 88.4%
- **Control Zone Coverage**: 92.9%
- **Conflict Data Coverage**: 60.5%

### Data Access

All processed data is stored in Parquet format in:

```
data/processed/
├── wfp_commodity_prices_enhanced.parquet
├── wfp_smart_panel.parquet
├── panels/
│   ├── integrated_panel.parquet
│   └── threshold_coint_panel.parquet
└── conflict/
    └── conflict_metrics.parquet
```

## See Also

- [Data Pipeline Guide](../guides/data_pipeline.md) - User-friendly pipeline overview
- [Methodology](../../METHODOLOGY.md) - Econometric approach and model specifications
- [API Documentation](../api/) - Programmatic access to data functions
