# Final Dataset Description

## Overview

The Yemen Market Integration project produces several interconnected datasets optimized for econometric analysis of market dynamics under conflict. This document describes the characteristics, structure, and usage of each final dataset.

## Primary Datasets

### 1. Enhanced Smart Panel (`wfp_smart_panel.parquet`)

**Description**: Main balanced panel dataset with commodity prices respecting actual market availability.

**Dimensions**:

- Observations: 14,208
- Time Period: 2019-01 to 2024-12 (72 months)
- Markets: 28
- Commodities: 8 core items (selected for 85%+ market coverage)

**Key Variables**:

```python
# Identifiers
- date: YYYY-MM-15 (monthly on 15th)
- market_id: Governorate_Market format
- commodity: Standardized commodity name
- year_month: Period[M] format

# Location
- governorate: Standardized to pcode names
- district: District name
- market_name: Local market name
- lat/lon: Coordinates (decimal degrees)
- admin1_pcode: Governorate pcode (e.g., YE11)

# Prices
- price_local: Price in YER
- price_usd: USD equivalent
- unit: Measurement unit (KG, 50 KG, etc.)
- currency: YER
- price_type: Retail/Wholesale
```

**Coverage Statistics**:

- Price observations: 12,560 (88.4% coverage)
- Missing patterns: Random (not structural)
- Commodity coverage: 86-89% per item

### 2. Integrated Analysis Panel (`integrated_panel.parquet`)

**Description**: Comprehensive dataset merging prices, control zones, and conflict metrics.

**Dimensions**:

- Observations: 44,122 (full balanced panel)
- Variables: 42
- Time Period: 2019-02 to 2024-12

**Additional Variables Beyond Smart Panel**:

```python
# Control Zones
- control_zone: DFA, IRG, STC, AQAP
- market_governorate: From control data
- market_district: From control data
- is_contested: Binary indicator

# Conflict Metrics
- conflict_intensity: Weighted events + fatalities
- total_fatalities: Monthly fatalities within 50km
- n_battles: Battle events count
- n_explosions: Explosion events count
- n_violence_civilians: Violence against civilians
- conflict_intensity_lag1/2/3: Lagged values
- conflict_ma3: 3-month moving average

# Conflict Regimes
- conflict_regime: Categorical (low/medium/high)
- high_conflict: Binary (top tercile)
- low_conflict: Binary (bottom tercile)

# Temporal Features
- time_trend: Linear trend
- quarter: 1-4
- is_ramadan: Approximate Ramadan indicator
- price_usd_lag1/2/3: Lagged prices
- price_usd_diff: First difference
- price_usd_pct_change: Percentage change
- price_usd_ma3: 3-month moving average
```

**Coverage by Component**:

- Control zone data: 92.9%
- Conflict data: 60.5%
- Price data: 62.0% (includes all commodities)

### 3. Model-Specific Datasets

#### 3.1 Threshold Cointegration Panel (`threshold_coint_panel.parquet`)

**Purpose**: Optimized for threshold VECM and regime-switching models.

**Key Features**:

- Includes all conflict variables for regime identification
- Pre-calculated lags and differences
- Only markets with sufficient observations
- 17 core variables for estimation

#### 3.2 Spatial Analysis Panel (`spatial_panel.parquet`)

**Purpose**: Geographic analysis and spatial econometrics.

**Unique Variables**:

- distance_to_zone_km: Distance to control boundary
- Coordinates for all markets
- Suitable for spatial weight matrix construction

### 4. Supporting Datasets

#### 4.1 Exchange Rates (`wfp_exchange_rates_enhanced.parquet`)

**Dimensions**: 1,609 observations

**Variables**:

```python
- official_rate: Official exchange rate
- parallel_rate: Parallel market rate
- exchange_rate: Primary rate for analysis
- rate_differential: Parallel - Official
- rate_premium: Percentage premium
```

#### 4.2 Conflict Metrics (`conflict_metrics.parquet`)

**Dimensions**: 2,016 market-month observations

**Aggregation**: 50km radius from market center

**Actor-Specific Counts**:

- n_events_houthis
- n_events_government
- n_events_aqap
- n_events_stc
- n_events_coalition

#### 4.3 Control Zone Time Series (`control_zones_monthly.parquet`)

**Dimensions**: 335 district-months

**Tracking**: Monthly control status by district

## Data Quality Indicators

### Spatial Coverage

```
Governorate Distribution:
- Hadramawt: 3 markets
- Taizz: 2 markets  
- Aden: 2 markets
- Hajjah: 2 markets
- Socotra: 2 markets
- Others: 1 market each (17 governorates)

Control Zone Distribution:
- IRG: 14 markets (50%)
- DFA: 12 markets (43%)
- STC: 6 markets (21%)
- AQAP: 1 market (4%)
Note: Some markets change control over time
```

### Temporal Patterns

```
Data Availability by Year:
2019: 85% coverage
2020: 88% coverage
2021: 89% coverage
2022: 90% coverage
2023: 91% coverage
2024: 70% coverage (partial year)

Seasonal Patterns:
- Slight reduction in observations during conflict escalations
- Ramadan months show price volatility
- Year-end often has reporting delays
```

### Commodity Coverage

```
Best Coverage (88-89%):
- Wheat, Wheat Flour
- Sugar, Salt  
- Onions, Potatoes, Tomatoes
- Rice (Imported)

Good Coverage (85-87%):
- Eggs
- Lentils
- Fuel (Diesel, Gas, Petrol)
- Oil (Vegetable)

Limited Coverage (Excluded):
- Meat products (only 2 markets)
- Sorghum/Millet (regional items)
- Oil (Sunflower) (limited availability)
```

## Usage Guidelines

### For Price Analysis

Use `wfp_smart_panel.parquet` - optimized for price dynamics with minimal missing data.

### For Conflict Impact Studies

Use `integrated_panel.parquet` - includes all conflict metrics and control zones.

### For Econometric Models

**Threshold Models**:

```python
df = pd.read_parquet('threshold_coint_panel.parquet')
# Already includes conflict_intensity as threshold variable
# Pre-calculated lags for VECM specification
```

**Spatial Models**:

```python
df = pd.read_parquet('spatial_panel.parquet')
# Has coordinates for weight matrix
# Use distance_to_zone_km for border effects
```

### Data Preparation Tips

1. **Handle Period[M] type**:

```python
df['year_month'] = pd.to_datetime(df['year_month'].astype(str))
```

2. **Create market pairs**:

```python
# For price transmission analysis
markets = df['market_id'].unique()
pairs = [(m1, m2) for m1 in markets for m2 in markets if m1 < m2]
```

3. **Filter stable markets**:

```python
# Markets with consistent data
stable = df.groupby('market_id').size() >= 60
stable_markets = stable[stable].index
```

## File Formats and Access

All datasets are stored in Parquet format for:

- Efficient compression (60-70% smaller than CSV)
- Preserved data types
- Fast read/write operations
- Column-specific access

**Reading in Python**:

```python
import pandas as pd
df = pd.read_parquet('data/processed/panels/integrated_panel.parquet')
```

**Reading in R**:

```r
library(arrow)
df <- read_parquet("data/processed/panels/integrated_panel.parquet")
```

## Metadata Files

Each dataset includes accompanying metadata:

**panel_metadata.json**:

- Creation timestamp
- Variable definitions
- Coverage statistics
- Processing parameters

**processing_summary_enhanced.json**:

- Source data versions
- Transformation steps
- Quality metrics
- Commodity selection criteria

## Update Frequency

- **Monthly**: Check for new WFP price data
- **Bi-weekly**: Update ACAPS control zones
- **Quarterly**: Refresh ACLED conflict data
- **As needed**: Reprocess if methodology changes

## Citation

When using these datasets, please cite:

```
Yemen Market Integration Dataset (2024). 
Processed from WFP, ACAPS, and ACLED sources.
Version: 1.0, Coverage: 2019-2024
GitHub: https://github.com/[your-repo]/yemen-market-integration
```
