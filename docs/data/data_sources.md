# Data Sources Documentation

## Overview

The Yemen Market Integration project integrates data from four primary sources to analyze market dynamics, price transmission, and the impact of territorial control on economic integration.

## 1. World Food Programme (WFP) Price Data

### Source Details

- **Provider**: World Food Programme (WFP) via Humanitarian Data Exchange (HDX)
- **Dataset**: [WFP Food Prices for Yemen](https://data.humdata.org/dataset/wfp-food-prices-for-yemen)
- **Update Frequency**: Monthly
- **Coverage**: March 2009 - Present
- **Format**: CSV with HXL tags

### Data Contents

- **Price Data**: Local and USD prices for 50+ commodities across 28 markets
- **Exchange Rates**: Official and parallel (unofficial) rates by market
- **Market Information**: Coordinates, governorate, district names
- **Temporal Coverage**: Monthly data points on the 15th of each month

### Key Fields

```
- date: Collection date (YYYY-MM-DD)
- admin1/admin2: Governorate/District names
- market: Market name
- latitude/longitude: Market coordinates
- commodity: Item name (e.g., "Wheat", "Rice (imported)")
- price: Local currency price
- usdprice: USD equivalent price
- currency: Currency code (YER)
- unit: Measurement unit (e.g., "KG", "50 KG")
```

### Access Method

- Downloaded via HDXClient using dataset ID: `98fc9c80-5c95-4670-8e5d-d37c62bb4bd6`
- Latest file pattern: `wfp_food_prices_YYYYMM.csv`

## 2. ACAPS Areas of Control Data

### Source Details

- **Provider**: ACAPS Yemen Analysis Hub via HDX
- **Dataset**: [Yemen - Areas of Control](https://data.humdata.org/dataset/yemen-areas-of-control)
- **Update Frequency**: Bi-weekly/Monthly
- **Coverage**: August 2021 - Present
- **Format**: Nested ZIP files containing Shapefiles

### Data Contents

- **Control Zones**: Territorial control by various actors
  - DFA (De Facto Authority/Houthis)
  - IRG (Internationally Recognized Government)
  - STC (Southern Transitional Council)
  - AQAP (Al-Qaeda in the Arabian Peninsula)
- **Geographic Data**: District-level polygons with control status
- **Temporal Snapshots**: Point-in-time control status

### File Structure

```
YYYYMMDD Yemen Analysis Hub - Areas of control.zip
├── DFA/
│   └── DFA_Control_YYYYMMDD.shp (+ .dbf, .prj, .shx)
├── IRG/
│   └── IRG_Control_YYYYMMDD.shp (+ auxiliary files)
└── STC/
    └── STC_Control_YYYYMMDD.shp (+ auxiliary files)
```

### Access Method

- Downloaded via HDXClient using dataset ID: `899c9255-c855-4dad-bbef-6a1cce07cd2d`
- Requires special handling for nested ZIP structure

## 3. ACLED Conflict Event Data

### Source Details

- **Provider**: Armed Conflict Location & Event Data Project (ACLED)
- **Dataset**: Yemen conflict events
- **Coverage**: 2015 - Present (we use 2019-2024)
- **Format**: CSV/JSON via API
- **Access**: Requires API key (stored in .env file)

### Data Contents

- **Event Information**: Date, location, event type, actors involved
- **Casualties**: Fatalities per event
- **Event Types**:
  - Battles
  - Explosions/Remote violence
  - Violence against civilians
  - Protests
  - Strategic developments
- **Actor Information**: Government forces, Houthis, AQAP, ISIS, STC, Coalition

### Key Metrics Calculated

```
- events_within_radius: Count within 50km of market
- conflict_intensity: Weighted sum of events and fatalities
- n_battles/n_explosions: Event type counts
- total_fatalities: Sum of fatalities
- Actor-specific event counts
```

### Access Method

- API endpoint: `https://api.acleddata.com/acled/read`
- Parameters: country=Yemen, dates, limit=0
- Authentication: API key + email in request

## 4. Administrative Boundaries and Pcodes

### Source Details

- **Provider**: OCHA via HDX Common Operational Datasets
- **Dataset**: [Yemen - Administrative Boundaries](https://data.humdata.org/dataset/cod-ab-yem)
- **Last Updated**: December 2024
- **Format**: Shapefiles, GeoPackage, Excel

### Data Contents

- **Administrative Levels**:
  - Admin0: Country (Yemen)
  - Admin1: Governorates (22 units)
  - Admin2: Districts (333 units)
  - Admin3: Sub-districts
- **Pcode System**: Standardized location codes
  - Example: YE1101 (Ibb Governorate, Al Qafr District)
- **Name Variants**: English, Arabic, alternative spellings

### Key Files

```
- yem_admin_pcodes-02122024.xlsx: Pcode reference tables
- yem_adm_govyem_cso_ochayemen_20191002_GPKG.zip: Boundary geometries
- YEM Administrative Divisions Shapefiles.zip: Traditional shapefiles
```

### Pcode Mapping Issues Resolved

```
WFP Name → Standard Pcode Name:
- "Al Dhale'e" → "Ad Dale'"
- "Al Hudaydah" → "Al Hodeidah"
- "Amanat Al Asimah" → "Sana'a City"
- "Hadramaut" → "Hadramawt"
- "Sa'ada" → "Sa'dah"
- "Taizz" → "Ta'iz"
```

## Data Quality Notes

### WFP Data

- **Strengths**: Consistent methodology, broad commodity coverage, includes exchange rates
- **Limitations**: Not all commodities available in all markets, some gaps in conflict areas
- **Coverage**: 28 markets across all major governorates

### ACAPS Data

- **Strengths**: Regular updates, clear territorial boundaries
- **Limitations**: Only available from 2021, simplified control categories
- **Special Handling**: Nested ZIP files require custom extraction

### ACLED Data

- **Strengths**: Detailed event-level data, comprehensive actor coding
- **Limitations**: Potential underreporting in remote areas
- **Processing**: Aggregated to market-month level with spatial buffers

### Administrative Data

- **Strengths**: Official OCHA standards, comprehensive pcode system
- **Limitations**: Name variations between sources require standardization
- **Usage**: Critical for cross-dataset integration

## Data Integration Keys

### Primary Keys

- **Market Identifier**: `governorate_market` (e.g., "Taizz_Taiz_City")
- **Temporal Key**: `year_month` (Period[M] format)
- **Spatial Key**: Latitude/Longitude coordinates

### Integration Challenges Resolved

1. **Name Standardization**: Applied pcode-based governorate name mapping
2. **Temporal Alignment**: Standardized to monthly frequency on 15th
3. **Spatial Matching**: Point-in-polygon joins for market-zone mapping
4. **Missing Data**: Smart panels respect commodity availability

## Update Schedule

- **WFP Prices**: Check monthly for updates
- **ACAPS Control**: Check bi-weekly
- **ACLED Events**: Update quarterly or as needed
- **Boundaries**: Check annually for revisions
