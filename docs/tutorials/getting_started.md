# Getting Started Tutorial

This tutorial will walk you through the basics of using the Yemen Market Integration project.

## Prerequisites

Before starting, ensure you have:

- Installed the project following the [deployment guide](../deployment/README.md)
- Downloaded the data using the collection scripts
- Basic knowledge of Python and pandas

## Tutorial 1: Loading and Exploring Data

### Step 1: Load the Integrated Panel

```python
import pandas as pd
from yemen_market.utils.logging import setup_logging, info, log_data_shape

# Set up logging
setup_logging()

# Load the integrated panel
panel = pd.read_parquet('data/processed/panels/integrated_panel.parquet')
log_data_shape("integrated_panel", panel)

# Basic exploration
info(f"Date range: {panel['date'].min()} to {panel['date'].max()}")
info(f"Markets: {panel['market_id'].nunique()}")
info(f"Commodities: {panel['commodity'].nunique()}")
```

### Step 2: Explore Price Patterns

```python
# Get wheat prices for a specific market
wheat_prices = panel[
    (panel['commodity'] == 'Wheat') & 
    (panel['market_id'] == 'Sana\'a_City_Sana\'a_City')
].copy()

# Calculate basic statistics
print(wheat_prices['price_usd'].describe())

# Plot price trends
import matplotlib.pyplot as plt

plt.figure(figsize=(12, 6))
plt.plot(wheat_prices['date'], wheat_prices['price_usd'])
plt.title('Wheat Prices in Sana\'a City')
plt.xlabel('Date')
plt.ylabel('Price (USD)')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

### Step 3: Analyze Conflict Impact

```python
# Check conflict coverage
conflict_coverage = panel['conflict_events'].notna().mean()
info(f"Conflict data coverage: {conflict_coverage:.1%}")

# High conflict periods
high_conflict = panel[panel['conflict_intensity'] > 2]
info(f"High conflict observations: {len(high_conflict)}")

# Price comparison: high vs low conflict
high_conflict_prices = panel[panel['conflict_intensity'] > 2]['price_usd'].mean()
low_conflict_prices = panel[panel['conflict_intensity'] <= 1]['price_usd'].mean()
info(f"Price difference: {(high_conflict_prices - low_conflict_prices) / low_conflict_prices * 100:.1f}%")
```

## Tutorial 2: Working with Smart Panels

### Understanding Smart Panels

Smart panels only include commodity-market pairs that actually exist in the data:

```python
# Load smart panel
smart_panel = pd.read_parquet('data/processed/wfp_smart_panel.parquet')
log_data_shape("smart_panel", smart_panel)

# Compare coverage
smart_coverage = smart_panel['price_usd'].notna().mean()
info(f"Smart panel coverage: {smart_coverage:.1%}")  # Should be ~88.4%

# Check which markets sell which commodities
market_commodities = smart_panel.groupby('market_id')['commodity'].unique()
print(f"Market commodity availability:")
for market, commodities in market_commodities.head().items():
    print(f"  {market}: {', '.join(commodities)}")
```

### Creating Monthly Aggregates

```python
# Monthly average prices by commodity
monthly_prices = smart_panel.groupby(
    ['year_month', 'commodity']
)['price_usd'].agg(['mean', 'std', 'count']).reset_index()

# Filter for well-covered commodities
well_covered = monthly_prices[monthly_prices['count'] >= 10]

# Pivot for analysis
price_pivot = well_covered.pivot(
    index='year_month',
    columns='commodity',
    values='mean'
)

# Calculate price indices (base = first period)
price_indices = price_pivot / price_pivot.iloc[0] * 100
```

## Tutorial 3: Feature Engineering

### Create Features for Analysis

```python
from yemen_market.features import FeatureEngineer

# Initialize feature engineer
engineer = FeatureEngineer(
    temporal_lags=[1, 2, 3],
    rolling_windows=[3, 6],
    spatial_neighbors=5
)

# Create features for a subset of data
sample_data = panel[panel['commodity'] == 'Wheat'].copy()
features = engineer.fit_transform(sample_data)

# Explore new features
new_cols = [col for col in features.columns if col not in sample_data.columns]
info(f"Created {len(new_cols)} new features")

# Example features
print("Sample features created:")
for col in new_cols[:10]:
    print(f"  - {col}")
```

### Using Specific Feature Types

```python
from yemen_market.features import create_temporal_features, create_threshold_indicators

# Just temporal features
temporal = create_temporal_features(sample_data, lags=[1, 3, 6])

# Just threshold indicators
thresholds = create_threshold_indicators(sample_data)

# Check threshold distribution
if 'high_conflict_threshold' in thresholds.columns:
    high_conflict_pct = thresholds['high_conflict_threshold'].mean() * 100
    info(f"High conflict periods: {high_conflict_pct:.1f}%")
```

## Tutorial 4: Spatial Analysis

### Working with Market Locations

```python
# Load market zones
market_zones = pd.read_parquet('data/processed/spatial/market_zones_temporal.parquet')

# Current zone assignments
current_zones = market_zones[
    market_zones['date'] == market_zones['date'].max()
].copy()

# Zone distribution
print("Markets by control zone:")
print(current_zones['control_zone'].value_counts())

# Boundary markets
boundary_markets = pd.read_csv('data/processed/spatial/boundary_markets.csv')
info(f"Boundary markets: {len(boundary_markets)}")
print("\nBoundary market examples:")
print(boundary_markets[['market_name', 'distance_to_boundary']].head())
```

### Creating Market Pairs for Analysis

```python
from yemen_market.data import PanelBuilder

# Build threshold analysis panel
builder = PanelBuilder()
threshold_panel = builder.build_threshold_panel()

# Explore market pairs
info(f"Market pairs: {threshold_panel['market_pair'].nunique()}")

# Same vs different zone pairs
same_zone = threshold_panel[threshold_panel['same_zone'] == 1]
diff_zone = threshold_panel[threshold_panel['same_zone'] == 0]

info(f"Same zone pairs: {same_zone['market_pair'].nunique()}")
info(f"Different zone pairs: {diff_zone['market_pair'].nunique()}")

# Price differential by zone status
print("\nAverage price differential:")
print(f"Same zone: ${same_zone['price_differential'].abs().mean():.3f}")
print(f"Different zone: ${diff_zone['price_differential'].abs().mean():.3f}")
```

## Tutorial 5: Creating Visualizations

### Price Dynamics Visualization

```python
from yemen_market.visualization.price_dynamics import PriceDynamicsVisualizer
import matplotlib.pyplot as plt

# Initialize visualizer
viz = PriceDynamicsVisualizer()

# Create multi-panel visualization
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 1. Price trends by commodity
ax = axes[0, 0]
for commodity in ['Wheat', 'Rice (Imported)', 'Sugar']:
    commodity_data = panel[panel['commodity'] == commodity]
    monthly_avg = commodity_data.groupby('date')['price_usd'].mean()
    ax.plot(monthly_avg.index, monthly_avg.values, label=commodity)
ax.set_title('Price Trends by Commodity')
ax.set_xlabel('Date')
ax.set_ylabel('Price (USD)')
ax.legend()

# 2. Price volatility
ax = axes[0, 1]
wheat_data = panel[panel['commodity'] == 'Wheat']
monthly_std = wheat_data.groupby('year_month')['price_usd'].std()
ax.bar(range(len(monthly_std)), monthly_std.values)
ax.set_title('Wheat Price Volatility')
ax.set_xlabel('Month')
ax.set_ylabel('Standard Deviation')

# 3. Conflict intensity map
ax = axes[1, 0]
conflict_by_market = panel.groupby('market_id')['conflict_intensity'].mean()
ax.bar(range(len(conflict_by_market)), sorted(conflict_by_market.values))
ax.set_title('Average Conflict Intensity by Market')
ax.set_xlabel('Market (sorted)')
ax.set_ylabel('Conflict Intensity')

# 4. Price-conflict relationship
ax = axes[1, 1]
wheat_monthly = wheat_data.groupby('year_month').agg({
    'price_usd': 'mean',
    'conflict_intensity': 'mean'
}).reset_index()
ax.scatter(wheat_monthly['conflict_intensity'], wheat_monthly['price_usd'], alpha=0.6)
ax.set_title('Wheat Price vs Conflict Intensity')
ax.set_xlabel('Conflict Intensity')
ax.set_ylabel('Price (USD)')

plt.tight_layout()
plt.show()
```

## Next Steps

1. **Explore the notebooks** in `notebooks/` for more detailed analysis
2. **Read the methodology** in `METHODOLOGY.md` for theoretical background
3. **Check the API documentation** in `docs/api/` for detailed function references
4. **Run the full pipeline test** to ensure everything works correctly

## Tips for Analysis

1. **Always use the enhanced logging system** for better debugging
2. **Start with smart panels** for better data coverage
3. **Check for missing data patterns** before analysis
4. **Use progress bars** for long operations
5. **Save intermediate results** in parquet format

## Common Patterns

### Loading and Filtering Data

```python
# Load specific commodity
wheat_data = panel[panel['commodity'] == 'Wheat']

# Filter by date range
recent_data = panel[panel['date'] >= '2023-01-01']

# Filter by zone
houthi_data = panel[panel['control_zone'] == 'DFA']
```

### Aggregating Data

```python
# Monthly averages
monthly = panel.groupby(['year_month', 'commodity'])['price_usd'].mean()

# Market-level statistics
market_stats = panel.groupby('market_id').agg({
    'price_usd': ['mean', 'std'],
    'conflict_events': 'sum'
})
```

### Saving Results

```python
# Save processed data
processed_data.to_parquet('data/processed/my_analysis.parquet')

# Save summary statistics
summary_stats.to_csv('reports/tables/summary_stats.csv')

# Save figures
plt.savefig('reports/figures/price_trends.png', dpi=300, bbox_inches='tight')
```
