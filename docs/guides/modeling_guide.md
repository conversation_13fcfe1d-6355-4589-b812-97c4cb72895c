# Econometric Modeling Guide

This guide walks through the complete process of implementing the three-tier econometric approach for Yemen market integration analysis.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Data Requirements](#data-requirements)
3. [Three-Tier Implementation](#three-tier-implementation)
4. [Interpreting Results](#interpreting-results)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)

## Quick Start

```bash
# Run the full three-tier analysis
python scripts/analysis/run_three_tier_models.py

# Run individual tiers
python scripts/analysis/run_tier1_pooled_panel.py
python scripts/analysis/run_tier2_commodity_specific.py
python scripts/analysis/run_tier3_factor_analysis.py
```

## Data Requirements

### Essential Structure

Your data must have a 3D panel structure:

- **Markets**: Multiple locations (e.g., Sana'a, Aden, Taiz)
- **Commodities**: Multiple goods (e.g., wheat, rice, sugar, fuel)
- **Time**: Monthly observations

### Required Columns

```python
required_columns = [
    'date',              # datetime: Period identifier
    'market',            # str: Market name
    'commodity',         # str: Commodity name
    'price_usd',         # float: Price in USD
    'conflict_intensity' # float: Conflict events (0-100+)
]
```

### Optional Enhancements

```python
optional_columns = [
    'latitude', 'longitude',  # For spatial analysis
    'control_zone',          # For boundary analysis
    'exchange_rate',         # For currency effects
    'rainfall',              # For weather controls
    'transport_cost'         # For infrastructure
]
```

### Data Quality Checks

```python
from yemen_market.utils.logging import info, warning, log_data_shape

def validate_panel_data(df):
    """Validate 3D panel structure."""
    
    # Check required columns
    missing = set(required_columns) - set(df.columns)
    if missing:
        raise ValueError(f"Missing columns: {missing}")
    
    # Check 3D structure
    markets = df['market'].nunique()
    commodities = df['commodity'].nunique()
    periods = df['date'].nunique()
    
    info(f"Panel dimensions: {markets} markets × {commodities} commodities × {periods} periods")
    log_data_shape("panel_data", df)
    
    # Check for duplicates
    duplicates = df.duplicated(['market', 'commodity', 'date']).sum()
    if duplicates > 0:
        warning(f"Found {duplicates} duplicate observations")
    
    # Coverage statistics
    expected = markets * commodities * periods
    actual = len(df)
    coverage = actual / expected * 100
    info(f"Panel coverage: {coverage:.1f}%")
    
    return True
```

## Three-Tier Implementation

### Tier 1: Pooled Panel Regression

**Purpose**: Estimate average effect of conflict across all markets and commodities.

```python
from linearmodels import PanelOLS
from yemen_market.utils.logging import timer

def run_tier1_analysis(df):
    """Run pooled panel regression with multi-way fixed effects."""
    
    with timer("Tier 1: Pooled panel regression"):
        # Create entity identifier
        df['entity'] = df['market'] + '_' + df['commodity']
        
        # Set up panel
        panel = df.set_index(['entity', 'date'])
        
        # Log transform price
        panel['log_price'] = np.log(panel['price_usd'])
        
        # Define model
        y = panel['log_price']
        X = panel[['conflict_intensity', 'log_exchange_rate']]
        
        # Add commodity dummies if needed
        commodity_dummies = pd.get_dummies(df['commodity'], drop_first=True)
        X = pd.concat([X, commodity_dummies], axis=1)
        
        # Estimate with multi-way FE
        model = PanelOLS(
            y, X,
            entity_effects=True,  # Market-commodity FE
            time_effects=True     # Time FE
        )
        
        # Fit with clustered standard errors
        results = model.fit(
            cov_type='clustered',
            cluster_entity=True
        )
        
        return results
```

### Tier 2: Commodity-Specific Analysis

**Purpose**: Examine heterogeneous effects and thresholds by commodity.

```python
from yemen_market.models.track2_simple import SimpleThresholdVECM

def run_tier2_analysis(df, commodity='Wheat'):
    """Run threshold VECM for specific commodity."""
    
    with timer(f"Tier 2: {commodity} analysis"):
        # Extract commodity data
        comm_df = df[df['commodity'] == commodity].copy()
        
        # Create price matrix (markets × time)
        price_matrix = comm_df.pivot(
            index='date',
            columns='market',
            values='price_usd'
        )
        
        # Create conflict matrix
        conflict_matrix = comm_df.pivot(
            index='date',
            columns='market',
            values='conflict_intensity'
        )
        
        # Handle missing values
        price_matrix = price_matrix.fillna(method='ffill', limit=2)
        
        # Initialize threshold model
        model = SimpleThresholdVECM(
            n_lags=2,
            n_coint=1,
            threshold_variable=conflict_matrix.mean(axis=1)
        )
        
        # Fit model
        model.fit(price_matrix, estimate_threshold=True)
        
        return model
```

### Tier 3: Factor Analysis

**Purpose**: Validate patterns and identify common price drivers.

```python
from sklearn.decomposition import PCA
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor

def run_tier3_analysis(df):
    """Run factor analysis for validation."""
    
    with timer("Tier 3: Factor analysis"):
        # Create wide price matrix
        price_wide = df.pivot_table(
            index='date',
            columns=['market', 'commodity'],
            values='log_price'
        )
        
        # Handle missing data
        price_filled = price_wide.fillna(method='ffill').fillna(method='bfill')
        
        # Static factor analysis (PCA)
        pca = PCA(n_components=5)
        factors_static = pca.fit_transform(price_filled)
        
        info(f"Variance explained by first 3 factors: {pca.explained_variance_ratio_[:3].sum():.1%}")
        
        # Dynamic factor model
        dfm = DynamicFactor(
            price_filled,
            k_factors=2,
            factor_order=1
        )
        dfm_results = dfm.fit(disp=False)
        
        # Extract factors
        factors_dynamic = dfm_results.factors.filtered
        
        # Correlate with conflict
        conflict_avg = df.groupby('date')['conflict_intensity'].mean()
        corr_factor1 = factors_dynamic.iloc[:, 0].corr(conflict_avg)
        corr_factor2 = factors_dynamic.iloc[:, 1].corr(conflict_avg)
        
        info(f"Factor 1 conflict correlation: {corr_factor1:.3f}")
        info(f"Factor 2 conflict correlation: {corr_factor2:.3f}")
        
        return {
            'pca': pca,
            'factors_static': factors_static,
            'dfm_results': dfm_results,
            'factors_dynamic': factors_dynamic
        }
```

## Interpreting Results

### Tier 1: Pooled Results

```python
def interpret_tier1(results):
    """Interpret pooled panel results."""
    
    # Conflict coefficient
    beta = results.params['conflict_intensity']
    se = results.std_errors['conflict_intensity']
    pval = results.pvalues['conflict_intensity']
    
    print(f"Conflict Effect: {beta:.4f} ({se:.4f})")
    print(f"Significance: p = {pval:.4f}")
    
    # Economic interpretation
    # If conflict increases by 10 events/month:
    price_increase = (np.exp(beta * 10) - 1) * 100
    print(f"10-unit conflict increase → {price_increase:.1f}% price increase")
    
    # Check fixed effects
    print(f"Entity FE F-stat: {results.f_statistic_entity:.2f}")
    print(f"Time FE F-stat: {results.f_statistic_time:.2f}")
```

### Tier 2: Commodity-Specific Results

```python
def interpret_tier2(model, commodity):
    """Interpret threshold VECM results."""
    
    print(f"\n{commodity} Threshold Analysis:")
    print(f"Threshold: {model.threshold_value:.1f} events/month")
    
    # Low conflict regime
    alpha_low = model.low_regime_results.alpha
    print(f"Low conflict adjustment: {alpha_low:.3f}")
    
    # High conflict regime  
    alpha_high = model.high_regime_results.alpha
    print(f"High conflict adjustment: {alpha_high:.3f}")
    
    # Integration change
    slowdown = (alpha_low - alpha_high) / alpha_low * 100
    print(f"Integration slows by {slowdown:.0f}% in high conflict")
```

### Tier 3: Factor Results

```python
def interpret_tier3(results):
    """Interpret factor analysis."""
    
    pca = results['pca']
    
    # Variance decomposition
    print("\nVariance Explained:")
    for i, var in enumerate(pca.explained_variance_ratio_[:3]):
        print(f"Factor {i+1}: {var:.1%}")
    
    # Factor interpretation
    if abs(results['factors_dynamic'].iloc[:, 0].corr(conflict_avg)) > 0.5:
        print("Factor 1 appears to capture conflict-related variation")
    else:
        print("Factor 1 likely captures national trends (inflation, seasonality)")
```

## Troubleshooting

### Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Reshape Error** | "Index contains duplicate entries" | Our three-tier approach handles this |
| **Memory Error** | Large matrix operations fail | Process commodities sequentially |
| **Convergence** | Model doesn't converge | Check for multicollinearity, scale variables |
| **Missing Data** | Many NaN values | Use interpolation or reduce panel scope |

### Debugging Steps

```python
def debug_panel_issues(df):
    """Diagnose panel data problems."""
    
    # Check for duplicates
    dups = df[df.duplicated(['market', 'commodity', 'date'], keep=False)]
    if len(dups) > 0:
        print(f"Found {len(dups)} duplicate entries")
        print(dups.head())
    
    # Check balance
    pivot = df.pivot_table(
        index='date',
        columns=['market', 'commodity'],
        values='price_usd',
        aggfunc='count'
    )
    print(f"Missing observations: {pivot.isna().sum().sum()}")
    
    # Identify problematic series
    coverage = pivot.notna().mean()
    low_coverage = coverage[coverage < 0.5]
    if len(low_coverage) > 0:
        print(f"\nLow coverage series:")
        print(low_coverage.sort_values().head(10))
```

## Best Practices

### 1. Data Preparation

- Always validate panel structure before modeling
- Handle missing data explicitly
- Log transform prices for better properties
- Standardize conflict measures

### 2. Model Estimation

- Start with Tier 1 for overall effects
- Use Tier 2 for policy-relevant commodities
- Validate with Tier 3 factor analysis
- Compare results across tiers

### 3. Robustness Checks

- Try different fixed effect specifications
- Test sensitivity to outliers
- Use multiple standard error corrections
- Document all choices

### 4. Reporting

- Lead with Tier 1 average effects
- Highlight heterogeneity from Tier 2
- Use Tier 3 to validate mechanisms
- Provide clear policy implications

## Example Workflow

```python
from yemen_market.models import ThreeTierAnalysis

# Complete three-tier analysis
def run_complete_analysis(data_path):
    """Run full three-tier analysis pipeline."""
    
    # Load data
    df = pd.read_parquet(data_path)
    
    # Validate
    validate_panel_data(df)
    
    # Run all tiers
    tier1_results = run_tier1_analysis(df)
    
    tier2_results = {}
    for commodity in ['Wheat', 'Rice', 'Sugar', 'Fuel']:
        tier2_results[commodity] = run_tier2_analysis(df, commodity)
    
    tier3_results = run_tier3_analysis(df)
    
    # Interpret
    interpret_tier1(tier1_results)
    for commodity, model in tier2_results.items():
        interpret_tier2(model, commodity)
    interpret_tier3(tier3_results)
    
    # Save results
    results = {
        'tier1': tier1_results,
        'tier2': tier2_results,
        'tier3': tier3_results
    }
    
    return results
```

## See Also

- [Three-Tier Methodology](../models/yemen_panel_methodology.md)
- [API Reference](../api/models/)
- [Data Pipeline Guide](./data_pipeline.md)
