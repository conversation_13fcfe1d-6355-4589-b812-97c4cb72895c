# 🗺️ Documentation Map

This guide helps you navigate the Yemen Market Integration documentation efficiently.

## 🎯 Quick Navigation by Role

### For Researchers/Academics
1. Start: [`METHODOLOGY.md`](../METHODOLOGY.md) - Three-tier approach overview
2. Details: [`docs/models/yemen_panel_methodology.md`](models/yemen_panel_methodology.md) - Full technical specification
3. Results: [`reports/progress/README.md`](../reports/progress/README.md) - Current findings

### For Developers
1. Start: [`CONTRIBUTING.md`](../CONTRIBUTING.md) - How to contribute
2. Rules: [`CLAUDE.md`](../CLAUDE.md) - Development standards
3. API: [`docs/api/README.md`](api/README.md) - Complete API reference
4. Run: [`scripts/analysis/run_three_tier_models.py`](../scripts/analysis/run_three_tier_models.py)

### For Policy Makers
1. Start: [`README.md`](../README.md) - Project overview
2. Methods: [`METHODOLOGY.md`](../METHODOLOGY.md) - Non-technical summary
3. Progress: [`reports/progress/README.md`](../reports/progress/README.md#key-findings) - Key findings section

### For Claude Code Users
1. Context: [`.claude/ACTIVE_CONTEXT.md`](../.claude/ACTIVE_CONTEXT.md) - Current work
2. Commands: [`.claude/commands/`](../.claude/commands/) - Slash commands
3. Memory: [`.claude/project_memory.md`](../.claude/project_memory.md) - Technical decisions

## 📚 Documentation Structure

```
yemen-market-integration/
│
├── 📋 Root Documentation
│   ├── README.md ..................... Project overview, installation
│   ├── METHODOLOGY.md ................ Three-tier methodology overview
│   ├── CLAUDE.md .................... Development rules and standards
│   └── CONTRIBUTING.md .............. Contribution guidelines
│
├── 📖 docs/
│   ├── DOCUMENTATION_MAP.md ......... THIS FILE - Navigation guide
│   │
│   ├── 📊 models/
│   │   ├── README.md ................ Model overview
│   │   ├── yemen_panel_methodology.md Detailed three-tier specification ⭐
│   │   └── archive/ ................. Historical approaches
│   │
│   ├── 🔧 api/
│   │   ├── README.md ................ API overview
│   │   ├── data/ .................... Data processing modules
│   │   ├── models/ .................. Model implementations
│   │   └── utils/ ................... Utility functions
│   │
│   ├── 📘 guides/
│   │   ├── data_pipeline.md ......... Data processing guide
│   │   ├── modeling_guide.md ........ Three-tier implementation guide
│   │   └── logging_guide.md ......... Enhanced logging system
│   │
│   └── 💾 data/
│       ├── README.md ................ Data documentation overview
│       ├── data_sources.md .......... Source descriptions
│       └── final_dataset_description.md Panel structure details
│
├── 🤖 .claude/
│   ├── README.md .................... Claude directory guide
│   ├── ACTIVE_CONTEXT.md ............ Current development status ⭐
│   ├── project_memory.md ............ Technical decisions
│   ├── methodology_notes.md ......... Key methodology decisions
│   ├── commands/ .................... Custom slash commands
│   └── prompts/ ..................... Reusable prompts
│
└── 📊 reports/
    └── progress/
        └── README.md ................ Progress dashboard ⭐
```

## 🔍 Finding Information

### By Topic

| Topic | Primary Doc | Detailed Doc | Code Example |
|-------|------------|--------------|--------------|
| **Three-Tier Method** | [`METHODOLOGY.md`](../METHODOLOGY.md) | [`yemen_panel_methodology.md`](models/yemen_panel_methodology.md) | [`run_three_tier_models.py`](../scripts/analysis/run_three_tier_models.py) |
| **Data Pipeline** | [`data/README.md`](data/README.md) | [`data_pipeline.md`](guides/data_pipeline.md) | [`build_panel_datasets.py`](../scripts/analysis/build_panel_datasets.py) |
| **Progress/Status** | [`progress/README.md`](../reports/progress/README.md) | [`.claude/ACTIVE_CONTEXT.md`](../.claude/ACTIVE_CONTEXT.md) | - |
| **API Reference** | [`api/README.md`](api/README.md) | Module-specific docs | Source code |
| **Development** | [`CLAUDE.md`](../CLAUDE.md) | [`CONTRIBUTING.md`](../CONTRIBUTING.md) | [`examples/`](../examples/) |

### By Question

**"How do I run the analysis?"**
→ [`scripts/analysis/run_three_tier_models.py`](../scripts/analysis/run_three_tier_models.py)

**"What's the current progress?"**
→ [`reports/progress/README.md`](../reports/progress/README.md) (88% complete)

**"How does the three-tier methodology work?"**
→ [`docs/models/yemen_panel_methodology.md`](models/yemen_panel_methodology.md)

**"What are the development rules?"**
→ [`CLAUDE.md`](../CLAUDE.md)

**"How do I contribute?"**
→ [`CONTRIBUTING.md`](../CONTRIBUTING.md)

**"What's the data structure?"**
→ [`docs/data/final_dataset_description.md`](data/final_dataset_description.md)

## 🌟 Key Documents

### Must Read (Top 5)
1. [`METHODOLOGY.md`](../METHODOLOGY.md) - Understand the approach
2. [`docs/models/yemen_panel_methodology.md`](models/yemen_panel_methodology.md) - Technical details
3. [`CLAUDE.md`](../CLAUDE.md) - Development standards
4. [`reports/progress/README.md`](../reports/progress/README.md) - Current status
5. [`.claude/ACTIVE_CONTEXT.md`](../.claude/ACTIVE_CONTEXT.md) - What's happening now

### For Implementation
1. [`scripts/analysis/run_three_tier_models.py`](../scripts/analysis/run_three_tier_models.py) - Main runner
2. [`docs/guides/modeling_guide.md`](guides/modeling_guide.md) - Step-by-step guide
3. [`docs/api/models/`](api/models/) - Model API documentation

### For Data Work
1. [`docs/data/data_sources.md`](data/data_sources.md) - Understanding sources
2. [`docs/guides/data_pipeline.md`](guides/data_pipeline.md) - Processing guide
3. [`scripts/analysis/build_panel_datasets.py`](../scripts/analysis/build_panel_datasets.py) - Panel creation

## 📝 Documentation Standards

- **Single Source of Truth**: Each topic has ONE authoritative location
- **Cross-References**: Documents link to related content
- **No Duplication**: Information appears once, referenced elsewhere
- **Clear Hierarchy**: General → Specific → Implementation

## 🔄 Keeping Updated

| File | Update Frequency | Who Updates |
|------|-----------------|-------------|
| `.claude/ACTIVE_CONTEXT.md` | After each session | Active developer |
| `reports/progress/README.md` | Weekly | Project lead |
| API docs | With code changes | Feature developer |
| `METHODOLOGY.md` | Major changes only | Technical lead |

---

**Lost?** Start with [`README.md`](../README.md) and follow the links!

**Building something new?** Check [`CONTRIBUTING.md`](../CONTRIBUTING.md) first!