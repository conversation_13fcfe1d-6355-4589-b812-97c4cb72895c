# Yemen Market Integration - Documentation

Comprehensive documentation for the Yemen Market Integration project.

## Documentation Structure

### 📊 [Data Documentation](./data/)

- [Data Sources](./data/data_sources.md) - Overview of all data sources
- [Data Pipeline](./data/data_pipeline_detailed.md) - Detailed pipeline documentation
- [Final Datasets](./data/final_dataset_description.md) - Description of processed datasets
- [Issues & Solutions](./data/issues_and_solutions.md) - Common issues and their solutions

### 🛠️ [Technical Documentation](./technical/)

- [Data Pipeline Architecture](./technical/data_pipeline.md)
- [Enhanced Logging System](./technical/logging_enhancement_summary.md)

### 📚 [User Guides](./guides/)

- [Data Pipeline Guide](./guides/data_pipeline.md) - How to run the data pipeline
- [Logging Guide](./guides/logging_guide.md) - How to use the logging system
- [Modeling Guide](./guides/modeling_guide.md) - Complete econometric modeling workflow

### 📈 [Models Documentation](./models/)

- [Models Overview](./models/README.md) - Three-tier modeling approach
- [Yemen Panel Methodology](./models/yemen_panel_methodology.md) - Complete 3D panel methodology

### 🔧 [API Documentation](./api/)

- [Complete API Reference](./api/README.md) - All modules documented
- [Models API](./api/models/README.md) - Econometric models (Week 5-6)
- [Diagnostics API](./api/diagnostics/test_battery.md) - Model validation framework

## Quick Start

1. **Installation**

   ```bash
   conda env create -f environment.yml
   conda activate yemen-market
   pip install -e .
   ```

2. **Download Data**

   ```bash
   python scripts/data_collection/download_data.py
   python scripts/data_collection/download_acled_data.py
   ```

3. **Process Data**

   ```bash
   python scripts/data_processing/process_wfp_data.py
   python scripts/data_processing/process_acaps_data.py
   python scripts/data_processing/process_acled_data.py
   python scripts/data_processing/run_spatial_joins.py
   ```

4. **Build Analysis Datasets**

   ```bash
   python scripts/analysis/build_panel_datasets.py
   ```

5. **Run Econometric Models** (Week 5-6)

   ```bash
   python scripts/analysis/run_week5_models.py
   python scripts/test_models.py  # Test implementations
   ```

## Key Features

### Enhanced Data Pipeline

- **Pcode Integration**: Standardized governorate names using Yemen administrative pcodes
- **Smart Panels**: Only includes commodity-market pairs that actually exist
- **88.4% Coverage**: Improved from 62% through smart data processing

### Conflict Integration

- ACLED data processing with market-level metrics
- 60.5% of observations include conflict intensity measures
- Event type breakdown and actor analysis

### Spatial Analysis

- Market to control zone mapping
- Boundary market identification
- Distance calculations between zones

### Feature Engineering

- Temporal features (lags, moving averages, differences)
- Interaction features (price × conflict, zone × time)
- Threshold indicators for regime switching
- Spatial features with neighbor analysis

### Econometric Models (Week 5-6)

- **Three-Tier Approach**: Handles 3D panel data (market × commodity × time)
  - **Tier 1**: Pooled panel regression with multi-way fixed effects
  - **Tier 2**: Commodity-specific threshold VECMs for key staples
  - **Tier 3**: Factor analysis for validation and pattern detection
- **Spatial Components**: Network effects and geographic spillovers
- **Comprehensive Diagnostics**: 30+ test battery with World Bank standards
- **Model Comparison**: Systematic framework across all three tiers

## Project Structure

```
yemen-market-integration/
├── data/               # Raw and processed data
├── docs/              # Documentation
├── notebooks/         # Analysis notebooks
├── reports/           # Reports and outputs
├── scripts/           # Executable scripts (organized by function)
├── src/yemen_market/  # Main package code
└── tests/            # Unit and integration tests
```

## Data Quality Metrics

- **Price Coverage**: 88.4% (14,208 observations with prices out of 16,080 total)
- **Markets**: 28 unique markets across 22 governorates
- **Commodities**: 23 commodities tracked
- **Time Period**: 2019-01-15 to 2025-03-15
- **Conflict Coverage**: 60.5% of observations have conflict metrics

## Further Reading

- [Methodology Document](../METHODOLOGY.md)
- [Progress Dashboard](../reports/progress/README.md)
- [EDA Findings](../reports/eda_findings.md)
