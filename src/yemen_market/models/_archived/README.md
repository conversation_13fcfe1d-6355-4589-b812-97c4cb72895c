# Archived Models

This directory contains the old dual-track models that have been replaced by the three-tier methodology.

## Archived Components

- **track1_complex/**: Old Bayesian TVP-VECM and spatial network models
- **track2_simple/**: Old threshold VECM implementation  
- **worldbank_threshold_vecm.py**: Temporary World Bank implementation

## Migration

These models are kept for reference and backward compatibility during the migration period.

### DO NOT USE THESE MODELS FOR NEW ANALYSES

Instead, use the new three-tier methodology:

```python
from yemen_market.models.three_tier.integration import ThreeTierAnalysis
```

See `MIGRATION_GUIDE.md` in the project root for migration instructions.

## Deprecation Timeline

- **Phase 4 (Current)**: Models moved to archive, migration utilities available
- **Phase 5**: Models will be marked with deprecation warnings
- **Phase 6**: Models will be removed completely

Last updated: January 2025
