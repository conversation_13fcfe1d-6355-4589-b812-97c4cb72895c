"""Simple Threshold VECM implementation.

This module implements a pragmatic threshold VECM model with a single
threshold based on conflict intensity. Designed for clear identification
and robust estimation using standard econometric techniques.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple, Union
from sklearn.linear_model import LinearRegression
from dataclasses import dataclass
import statsmodels.api as sm
from scipy import stats

from ...utils.logging import bind, timer, info, warning, error, log_metric, log_data_shape, progress
from ..base import VECMBase, VECMResults, ModelType, ThresholdModelMixin


@dataclass
class ThresholdVECMResults(VECMResults):
    """Results container for threshold VECM models."""
    
    # Threshold test results
    threshold_test_stat: float = 0.0
    threshold_p_value: float = 1.0
    threshold_ci_lower: float = 0.0
    threshold_ci_upper: float = 0.0
    
    # Regime-specific results
    low_regime_alpha: Optional[np.ndarray] = None
    high_regime_alpha: Optional[np.ndarray] = None
    low_regime_alpha_se: Optional[np.ndarray] = None
    high_regime_alpha_se: Optional[np.ndarray] = None
    low_regime_gamma: Optional[np.ndarray] = None
    high_regime_gamma: Optional[np.ndarray] = None
    
    # Regime assignment
    regime_assignment: Optional[np.ndarray] = None
    n_obs_low: int = 0
    n_obs_high: int = 0
    
    # Transition probabilities
    transition_probs: Optional[np.ndarray] = None


class SimpleThresholdVECM(VECMBase, ThresholdModelMixin):
    """Simple single-threshold VECM model.
    
    This model estimates a VECM with two regimes separated by a single
    threshold in conflict intensity. Based on the EDA finding of 50 events/month
    as a critical threshold for market dynamics.
    
    Key features:
    - Single threshold based on conflict intensity
    - Separate adjustment speeds for low/high conflict regimes
    - Standard OLS estimation within each regime
    - Hansen (1999) threshold testing
    - Clear identification and interpretation
    """
    
    def __init__(self, threshold: float = 50.0,
                 threshold_variable: str = 'conflict_intensity',
                 n_coint: int = 1, n_lags: int = 2,
                 trim_pct: float = 0.15,
                 n_boot: int = 1000,
                 random_seed: int = 42,
                 name: str = None):
        """Initialize threshold VECM.
        
        Args:
            threshold: Initial threshold value (can be estimated)
            threshold_variable: Variable to use for threshold
            n_coint: Number of cointegrating relationships
            n_lags: Number of lags in the model
            trim_pct: Trimming percentage for threshold search
            n_boot: Number of bootstrap replications for testing
            random_seed: Random seed for reproducibility
            name: Optional model name
        """
        super().__init__(n_coint=n_coint, n_lags=n_lags, name=name or "ThresholdVECM")
        self.threshold = threshold
        self.threshold_variable = threshold_variable
        self.trim_pct = trim_pct
        self.n_boot = n_boot
        self.random_seed = random_seed
        self.threshold_estimated = False
        
        bind(model=self.name, model_type=ModelType.THRESHOLD_VECM.value)
    
    def fit(self, data: pd.DataFrame, 
            estimate_threshold: bool = True,
            price_col: str = 'price_usd',
            **kwargs) -> 'SimpleThresholdVECM':
        """Fit the threshold VECM model.
        
        Args:
            data: Panel data with prices and threshold variable
            estimate_threshold: Whether to estimate optimal threshold
            price_col: Name of price column
            **kwargs: Additional arguments for prepare_data
            
        Returns:
            Self for method chaining
        """
        with timer("fit_threshold_vecm"):
            info("Starting Threshold VECM estimation")
            
            # Prepare data
            prepared_data = self.prepare_data(data, price_cols=[price_col], **kwargs)
            self.data = prepared_data
            
            # Extract threshold variable
            threshold_data = data.groupby('date')[self.threshold_variable].mean()
            threshold_aligned = threshold_data.reindex(prepared_data['dates']).fillna(0)
            
            # Test for cointegration
            coint_results = self.test_cointegration(prepared_data)
            self.n_coint = coint_results['selected_rank']
            info(f"Selected cointegration rank: {self.n_coint}")
            
            if self.n_coint == 0:
                warning("No cointegration found, proceeding with VAR in differences")
            
            # Estimate or validate threshold
            if estimate_threshold:
                self.threshold = self._estimate_optimal_threshold(
                    prepared_data, threshold_aligned, coint_results
                )
                self.threshold_estimated = True
            else:
                info(f"Using fixed threshold: {self.threshold}")
            
            # Test threshold significance
            threshold_test = self._test_threshold_significance(
                prepared_data, threshold_aligned, coint_results
            )
            
            # Estimate regime-specific models
            regime_results = self._estimate_regime_models(
                prepared_data, threshold_aligned, coint_results
            )
            
            # Create results object
            self._create_results(regime_results, threshold_test, threshold_aligned)
            
            self.is_fitted = True
            log_metric("threshold_vecm_fitted", 1)
            
            return self
    
    def _estimate_optimal_threshold(self, data: Dict[str, Any],
                                  threshold_var: pd.Series,
                                  coint_results: Dict[str, Any]) -> float:
        """Estimate optimal threshold using grid search.
        
        Args:
            data: Prepared data dictionary
            threshold_var: Threshold variable series
            coint_results: Cointegration test results
            
        Returns:
            Optimal threshold value
        """
        with timer("threshold_estimation"):
            info("Estimating optimal threshold")
            
            # Get threshold variable values
            thr_values = threshold_var.values[1:]  # Align with dlogs
            
            # Determine search grid
            thr_sorted = np.sort(np.unique(thr_values))
            n_obs = len(thr_values)
            trim_n = int(n_obs * self.trim_pct)
            
            # Grid points (ensure minimum observations in each regime)
            grid_points = thr_sorted[trim_n:-trim_n]
            
            if len(grid_points) == 0:
                warning("Insufficient variation for threshold search, using median")
                return float(np.median(thr_values))
            
            info(f"Searching over {len(grid_points)} threshold values")
            
            # Calculate sum of squared residuals for each threshold
            ssr_values = []
            
            with progress("Threshold search", total=len(grid_points)) as update:
                for thr in grid_points:
                    ssr = self._calculate_ssr_for_threshold(
                        data, threshold_var, thr, coint_results
                    )
                    ssr_values.append(ssr)
                    update(1)
            
            # Find minimum SSR
            min_idx = np.argmin(ssr_values)
            optimal_threshold = float(grid_points[min_idx])
            
            # Apply practical constraints: threshold must be economically meaningful
            if optimal_threshold < 20:
                warning(f"Estimated threshold ({optimal_threshold:.1f}) below reasonable range")
                info("Using theory-based threshold of 50 events/month")
                optimal_threshold = 50.0
            elif optimal_threshold > 200:
                warning(f"Estimated threshold ({optimal_threshold:.1f}) above reasonable range")
                info("Using theory-based threshold of 50 events/month")
                optimal_threshold = 50.0
            
            log_metric("optimal_threshold", optimal_threshold)
            info(f"Optimal threshold: {optimal_threshold:.2f}")
            
            return optimal_threshold
    
    def _calculate_ssr_for_threshold(self, data: Dict[str, Any],
                                   threshold_var: pd.Series,
                                   threshold: float,
                                   coint_results: Dict[str, Any]) -> float:
        """Calculate sum of squared residuals for a given threshold.
        
        Args:
            data: Prepared data dictionary
            threshold_var: Threshold variable series
            threshold: Threshold value to test
            coint_results: Cointegration test results
            
        Returns:
            Sum of squared residuals
        """
        # Get data matrices
        dlogs = data['dlogs'].values
        log_prices = data['log_prices'].values
        
        # Get cointegrating vectors
        if self.n_coint > 0:
            beta = coint_results['eigenvectors'][:, :self.n_coint]
            ect = self.calculate_error_correction_terms(log_prices[:-1], beta)
        else:
            ect = None
        
        # Split by regime
        thr_values = threshold_var.values[1:]
        low_regime = thr_values <= threshold
        high_regime = ~low_regime
        
        # Check minimum observations
        if np.sum(low_regime) < 10 or np.sum(high_regime) < 10:
            return np.inf
        
        # Calculate SSR for each regime
        ssr_total = 0
        
        for regime_mask in [low_regime, high_regime]:
            if ect is not None:
                # VECM specification
                X_regime = ect[regime_mask]
                y_regime = dlogs[1:][regime_mask]
                
                # Add lags if needed - use pandas for better alignment
                if self.n_lags > 1:
                    # Create DataFrame for easier lag operations
                    df_dlogs = pd.DataFrame(dlogs[1:], columns=[f'var_{i}' for i in range(dlogs.shape[1])])
                    df_dlogs['regime'] = regime_mask
                    
                    # Add lagged differences
                    lag_columns = []
                    for lag in range(1, self.n_lags):
                        for col in df_dlogs.columns:
                            if col != 'regime':
                                lag_col = f'{col}_lag{lag}'
                                df_dlogs[lag_col] = df_dlogs[col].shift(lag)
                                lag_columns.append(lag_col)
                    
                    # Filter by regime and drop NaN rows
                    df_regime = df_dlogs[df_dlogs['regime']].dropna()
                    
                    if len(df_regime) > 0:
                        # Extract lagged values
                        lagged_X = df_regime[lag_columns].values
                        
                        # Get the valid indices and apply to the FULL arrays before regime filtering
                        valid_indices = df_regime.index.values
                        
                        # Apply valid_indices to the full arrays first, then filter by regime
                        y_regime = dlogs[1:][valid_indices]
                        X_regime = ect[valid_indices]
                        
                        # Concatenate ECT with lagged values
                        X_regime = np.hstack([X_regime, lagged_X])
            else:
                # VAR in differences
                # Don't filter by regime yet - let DataFrame operations handle alignment
                
                # Add additional lags for VAR using pandas
                if self.n_lags > 1:
                    # Create DataFrame for easier lag operations
                    df_dlogs = pd.DataFrame(dlogs[1:], columns=[f'var_{i}' for i in range(dlogs.shape[1])])
                    df_dlogs['regime'] = regime_mask
                    
                    # Add all lags
                    lag_columns = []
                    for lag in range(1, self.n_lags + 1):
                        for col in df_dlogs.columns:
                            if col != 'regime':
                                lag_col = f'{col}_lag{lag}'
                                df_dlogs[lag_col] = df_dlogs[col].shift(lag)
                                lag_columns.append(lag_col)
                    
                    # Filter by regime and drop NaN rows
                    df_regime = df_dlogs[df_dlogs['regime']].dropna()
                    
                    if len(df_regime) > 0:
                        # Get the valid indices and apply to the FULL arrays before regime filtering
                        valid_indices = df_regime.index.values
                        
                        # Extract lagged values for all lags
                        X_regime = df_regime[lag_columns].values
                        
                        # Apply valid_indices to the full arrays first
                        y_regime = dlogs[1:][valid_indices]
                    else:
                        # Fallback if no valid observations
                        continue
                else:
                    # Simple case: just one lag, apply regime mask directly
                    y_regime = dlogs[1:][regime_mask]
                    X_regime = dlogs[:-1][regime_mask]
            
            # OLS regression
            try:
                reg = LinearRegression(fit_intercept=True)
                reg.fit(X_regime, y_regime)
                residuals = y_regime - reg.predict(X_regime)
                ssr_total += np.sum(residuals**2)
            except Exception as e:
                warning(f"SSR calculation failed for regime: {e}")
                return np.inf
        
        return ssr_total
    
    def _test_threshold_significance(self, data: Dict[str, Any],
                                   threshold_var: pd.Series,
                                   coint_results: Dict[str, Any]) -> Dict[str, Any]:
        """Test significance of threshold effect using Hansen (1999) approach.
        
        Args:
            data: Prepared data dictionary
            threshold_var: Threshold variable series
            coint_results: Cointegration test results
            
        Returns:
            Dictionary with test results
        """
        with timer("threshold_test"):
            info("Testing threshold significance")
            
            # Calculate test statistic
            # H0: Linear VECM, H1: Threshold VECM
            
            # Fit linear model
            linear_ssr = self._fit_linear_vecm(data, coint_results)
            
            # Fit threshold model
            threshold_ssr = self._calculate_ssr_for_threshold(
                data, threshold_var, self.threshold, coint_results
            )
            
            # LM test statistic
            n_obs = data['n_obs'] - 1
            lm_stat = n_obs * (linear_ssr - threshold_ssr) / threshold_ssr
            
            log_metric("threshold_lm_stat", lm_stat)
            
            # Bootstrap p-value
            if self.n_boot > 0:
                info(f"Running {self.n_boot} bootstrap replications")
                p_value = self._bootstrap_threshold_test(
                    data, threshold_var, coint_results, lm_stat
                )
            else:
                # Asymptotic approximation
                p_value = 1 - stats.chi2.cdf(lm_stat, df=1)
            
            # Confidence interval for threshold
            ci_lower, ci_upper = self._threshold_confidence_interval(
                data, threshold_var, coint_results
            )
            
            results = {
                'test_statistic': float(lm_stat),
                'p_value': float(p_value),
                'threshold': float(self.threshold),
                'ci_lower': float(ci_lower),
                'ci_upper': float(ci_upper),
                'reject_linearity': p_value < 0.05
            }
            
            if results['reject_linearity']:
                info(f"Threshold effect is significant (p={p_value:.4f})")
            else:
                warning(f"Threshold effect not significant (p={p_value:.4f})")
            
            return results
    
    def _fit_linear_vecm(self, data: Dict[str, Any],
                        coint_results: Dict[str, Any]) -> float:
        """Fit linear VECM and return SSR.
        
        Args:
            data: Prepared data dictionary
            coint_results: Cointegration test results
            
        Returns:
            Sum of squared residuals
        """
        dlogs = data['dlogs'].values
        log_prices = data['log_prices'].values
        
        if self.n_coint > 0:
            beta = coint_results['eigenvectors'][:, :self.n_coint]
            ect = self.calculate_error_correction_terms(log_prices[:-1], beta)
            X = ect
            y = dlogs[1:]
        else:
            X = dlogs[:-1]
            y = dlogs[1:]
        
        reg = LinearRegression(fit_intercept=True)
        reg.fit(X, y)
        residuals = y - reg.predict(X)
        
        return float(np.sum(residuals**2))
    
    def _bootstrap_threshold_test(self, data: Dict[str, Any],
                                threshold_var: pd.Series,
                                coint_results: Dict[str, Any],
                                observed_stat: float) -> float:
        """Bootstrap p-value for threshold test using residual-based bootstrap.
        
        This implements the Hansen (1999) residual-based bootstrap to preserve
        the null hypothesis of linearity while testing for threshold effects.
        
        Args:
            data: Prepared data dictionary
            threshold_var: Threshold variable series
            coint_results: Cointegration test results
            observed_stat: Observed test statistic
            
        Returns:
            Bootstrap p-value
        """
        n_exceed = 0
        
        # First, fit the linear model and obtain residuals
        dlogs = data['dlogs'].values
        log_prices = data['log_prices'].values
        n_obs, n_vars = dlogs.shape
        
        # Fit linear VECM under H0
        if self.n_coint > 0:
            beta = coint_results['eigenvectors'][:, :self.n_coint]
            ect = self.calculate_error_correction_terms(log_prices[:-1], beta)
            X_linear = ect
        else:
            X_linear = dlogs[:-1]
        
        y_linear = dlogs[1:]
        
        # OLS estimation of linear model
        from sklearn.linear_model import LinearRegression
        linear_model = LinearRegression(fit_intercept=True)
        linear_model.fit(X_linear, y_linear)
        
        # Get fitted values and residuals
        fitted_values = linear_model.predict(X_linear)
        residuals = y_linear - fitted_values
        
        # Center residuals
        residuals_centered = residuals - np.mean(residuals, axis=0)
        
        # Parallel bootstrap implementation for M3 Pro
        try:
            from joblib import Parallel, delayed
            n_jobs = max(1, min(10, self.n_boot // 100))  # Use up to 10 cores, at least 1
            info(f"Using parallel bootstrap with {n_jobs} jobs")
            
            def bootstrap_iteration(b, seed):
                np.random.seed(seed)
                # Step 1: Resample residuals (with replacement)
                boot_indices = np.random.choice(len(residuals_centered), 
                                              size=len(residuals_centered), 
                                              replace=True)
                residuals_boot = residuals_centered[boot_indices]
                
                # Step 2: Reconstruct data under H0 (linear model)
                y_boot = fitted_values + residuals_boot
                
                # Create bootstrap data dictionary
                data_boot = data.copy()
                dlogs_boot = np.vstack([dlogs[0], y_boot])
                data_boot['dlogs'] = pd.DataFrame(dlogs_boot, 
                                                index=data['dlogs'].index,
                                                columns=data['dlogs'].columns)
                
                # Step 3: Compute SSR for linear model on bootstrap data
                if self.n_coint > 0:
                    ssr_linear_boot = np.sum((y_boot - linear_model.predict(X_linear))**2)
                else:
                    ssr_linear_boot = np.sum(residuals_boot**2)
                
                # Step 4: Compute SSR for threshold model on bootstrap data
                ssr_threshold_boot = self._calculate_ssr_for_threshold(
                    data_boot, threshold_var, self.threshold, coint_results
                )
                
                # Step 5: Compute bootstrap test statistic
                n_obs_boot = len(y_boot)
                lm_stat_boot = n_obs_boot * (ssr_linear_boot - ssr_threshold_boot) / ssr_threshold_boot
                
                return lm_stat_boot >= observed_stat
            
            # Run parallel bootstrap
            with progress("Bootstrap test", total=self.n_boot) as update:
                results = Parallel(n_jobs=n_jobs, backend='threading')(
                    delayed(bootstrap_iteration)(b, self.random_seed + b) 
                    for b in range(self.n_boot)
                )
                update(self.n_boot)
                
            n_exceed = sum(results)
            
        except ImportError:
            warning("joblib not available, using sequential bootstrap")
            # Fallback to sequential implementation
            with progress("Bootstrap test", total=self.n_boot) as update:
                for b in range(self.n_boot):
                    np.random.seed(self.random_seed + b)
                    # Step 1: Resample residuals (with replacement)
                    boot_indices = np.random.choice(len(residuals_centered), 
                                                  size=len(residuals_centered), 
                                                  replace=True)
                    residuals_boot = residuals_centered[boot_indices]
                    
                    # Step 2: Reconstruct data under H0 (linear model)
                    y_boot = fitted_values + residuals_boot
                    
                    # Create bootstrap data dictionary
                    data_boot = data.copy()
                    dlogs_boot = np.vstack([dlogs[0], y_boot])
                    data_boot['dlogs'] = pd.DataFrame(dlogs_boot, 
                                                    index=data['dlogs'].index,
                                                    columns=data['dlogs'].columns)
                    
                    # Step 3: Compute SSR for linear model on bootstrap data
                    if self.n_coint > 0:
                        ssr_linear_boot = np.sum((y_boot - linear_model.predict(X_linear))**2)
                    else:
                        ssr_linear_boot = np.sum(residuals_boot**2)
                    
                    # Step 4: Compute SSR for threshold model on bootstrap data
                    ssr_threshold_boot = self._calculate_ssr_for_threshold(
                        data_boot, threshold_var, self.threshold, coint_results
                    )
                    
                    # Step 5: Compute bootstrap test statistic
                    n_obs_boot = len(y_boot)
                    lm_stat_boot = n_obs_boot * (ssr_linear_boot - ssr_threshold_boot) / ssr_threshold_boot
                    
                    if lm_stat_boot >= observed_stat:
                        n_exceed += 1
                    
                    update(1)
        
        p_value = n_exceed / self.n_boot
        
        info(f"Bootstrap p-value: {p_value:.4f} ({n_exceed}/{self.n_boot} exceeding observed)")
        
        return p_value
    
    def _threshold_confidence_interval(self, data: Dict[str, Any],
                                     threshold_var: pd.Series,
                                     coint_results: Dict[str, Any]) -> Tuple[float, float]:
        """Compute confidence interval for threshold.
        
        Args:
            data: Prepared data dictionary
            threshold_var: Threshold variable series
            coint_results: Cointegration test results
            
        Returns:
            Tuple of (lower, upper) confidence bounds
        """
        # Simplified: use likelihood ratio approach
        optimal_ssr = self._calculate_ssr_for_threshold(
            data, threshold_var, self.threshold, coint_results
        )
        
        # Critical value for 95% CI
        crit_val = stats.chi2.ppf(0.95, df=1)
        n_obs = data['n_obs'] - 1
        
        # Find values where LR < critical value
        thr_values = np.sort(np.unique(threshold_var.values[1:]))
        ci_values = []
        
        for thr in thr_values:
            ssr = self._calculate_ssr_for_threshold(
                data, threshold_var, thr, coint_results
            )
            lr = n_obs * (ssr - optimal_ssr) / optimal_ssr
            
            if lr < crit_val:
                ci_values.append(thr)
        
        if ci_values:
            return float(min(ci_values)), float(max(ci_values))
        else:
            return float(self.threshold * 0.9), float(self.threshold * 1.1)
    
    def _estimate_regime_models(self, data: Dict[str, Any],
                              threshold_var: pd.Series,
                              coint_results: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate separate models for each regime.
        
        Args:
            data: Prepared data dictionary
            threshold_var: Threshold variable series
            coint_results: Cointegration test results
            
        Returns:
            Dictionary with regime-specific results
        """
        with timer("regime_estimation"):
            info("Estimating regime-specific models")
            
            # Get data matrices
            dlogs = data['dlogs'].values
            log_prices = data['log_prices'].values
            n_vars = dlogs.shape[1]
            
            # Get cointegrating vectors
            if self.n_coint > 0:
                beta = coint_results['eigenvectors'][:, :self.n_coint]
                ect = self.calculate_error_correction_terms(log_prices[:-1], beta)
            else:
                beta = None
                ect = None
            
            # Split by regime
            thr_values = threshold_var.values[1:]
            low_regime = thr_values <= self.threshold
            high_regime = ~low_regime
            
            results = {
                'beta': beta,
                'n_obs_low': int(np.sum(low_regime)),
                'n_obs_high': int(np.sum(high_regime)),
                'regime_assignment': low_regime.astype(int)
            }
            
            info(f"Low regime: {results['n_obs_low']} obs, "
                 f"High regime: {results['n_obs_high']} obs")
            
            # Estimate each regime
            for regime_name, regime_mask in [('low', low_regime), ('high', high_regime)]:
                if np.sum(regime_mask) < 20:
                    warning(f"{regime_name} regime has too few observations")
                    continue
                
                regime_results = self._estimate_single_regime(
                    dlogs[1:][regime_mask],
                    ect[regime_mask] if ect is not None else None,
                    dlogs[:-1][regime_mask] if self.n_lags > 1 else None,
                    n_vars
                )
                
                results[f'{regime_name}_alpha'] = regime_results['alpha']
                results[f'{regime_name}_alpha_se'] = regime_results['alpha_se']
                results[f'{regime_name}_gamma'] = regime_results.get('gamma')
                results[f'{regime_name}_sigma'] = regime_results['sigma']
            
            # Calculate transition probabilities
            results['transition_probs'] = self._calculate_transition_probs(low_regime)
            
            return results
    
    def _estimate_single_regime(self, y: np.ndarray,
                              ect: Optional[np.ndarray],
                              lags: Optional[np.ndarray],
                              n_vars: int) -> Dict[str, np.ndarray]:
        """Estimate model for a single regime.
        
        Args:
            y: Dependent variable (dlogs) for this regime
            ect: Error correction terms (if cointegrated)
            lags: Lagged differences (if n_lags > 1)
            n_vars: Number of variables
            
        Returns:
            Dictionary with coefficient estimates
        """
        results = {}
        
        # Build regressor matrix
        if ect is not None:
            X = ect
            if lags is not None and self.n_lags > 1:
                # Add lagged differences
                X = np.hstack([X, lags])
        else:
            # Pure VAR in differences
            if lags is not None:
                X = lags
            else:
                X = np.empty((len(y), 0))  # no regressors; intercept added later
        
        # Estimate equation by equation
        alpha = np.zeros((n_vars, self.n_coint)) if ect is not None else None
        alpha_se = np.zeros((n_vars, self.n_coint)) if ect is not None else None
        gamma = np.zeros((n_vars, n_vars * (self.n_lags - 1))) if self.n_lags > 1 else None
        sigma = np.zeros(n_vars)
        
        for i in range(n_vars):
            # OLS regression
            model = sm.OLS(y[:, i], sm.add_constant(X))
            res = model.fit()
            
            if ect is not None:
                alpha[i, :] = res.params[1:self.n_coint+1]
                alpha_se[i, :] = res.bse[1:self.n_coint+1]
                
                if self.n_lags > 1 and gamma is not None:
                    gamma[i, :] = res.params[self.n_coint+1:]
            
            sigma[i] = res.scale**0.5
        
        results['alpha'] = alpha
        results['alpha_se'] = alpha_se
        results['gamma'] = gamma
        results['sigma'] = sigma
        
        return results
    
    def _calculate_transition_probs(self, regime_assignment: np.ndarray) -> np.ndarray:
        """Calculate regime transition probabilities.
        
        Args:
            regime_assignment: Binary array of regime assignments
            
        Returns:
            2x2 transition probability matrix
        """
        # Calculate transitions
        trans_00 = np.sum((regime_assignment[:-1] == 0) & (regime_assignment[1:] == 0))
        trans_01 = np.sum((regime_assignment[:-1] == 0) & (regime_assignment[1:] == 1))
        trans_10 = np.sum((regime_assignment[:-1] == 1) & (regime_assignment[1:] == 0))
        trans_11 = np.sum((regime_assignment[:-1] == 1) & (regime_assignment[1:] == 1))
        
        # Normalize to probabilities
        trans_matrix = np.array([
            [trans_00, trans_01],
            [trans_10, trans_11]
        ], dtype=float)
        
        row_sums = trans_matrix.sum(axis=1, keepdims=True)
        trans_matrix = trans_matrix / (row_sums + 1e-10)
        
        return trans_matrix
    
    def _create_results(self, regime_results: Dict[str, Any],
                       threshold_test: Dict[str, Any],
                       threshold_var: pd.Series) -> None:
        """Create results object from estimation outputs.
        
        Args:
            regime_results: Dictionary with regime-specific estimates
            threshold_test: Threshold test results
            threshold_var: Threshold variable series
        """
        # Average alpha across regimes for summary
        if 'low_alpha' in regime_results and 'high_alpha' in regime_results:
            n_low = regime_results['n_obs_low']
            n_high = regime_results['n_obs_high']
            n_total = n_low + n_high
            
            avg_alpha = (
                regime_results['low_alpha'] * n_low + 
                regime_results['high_alpha'] * n_high
            ) / n_total
        else:
            avg_alpha = regime_results.get('low_alpha', np.array([]))
        
        self.vecm_results = ThresholdVECMResults(
            # Base parameters
            alpha=avg_alpha,
            beta=regime_results.get('beta', np.array([])),
            
            # Threshold-specific
            threshold_value=self.threshold,
            threshold_variable=self.threshold_variable,
            threshold_test_stat=threshold_test['test_statistic'],
            threshold_p_value=threshold_test['p_value'],
            threshold_ci_lower=threshold_test['ci_lower'],
            threshold_ci_upper=threshold_test['ci_upper'],
            
            # Regime-specific
            low_regime_alpha=regime_results.get('low_alpha'),
            high_regime_alpha=regime_results.get('high_alpha'),
            low_regime_alpha_se=regime_results.get('low_alpha_se'),
            high_regime_alpha_se=regime_results.get('high_alpha_se'),
            low_regime_gamma=regime_results.get('low_gamma'),
            high_regime_gamma=regime_results.get('high_gamma'),
            regime_assignment=regime_results['regime_assignment'],
            n_obs_low=regime_results['n_obs_low'],
            n_obs_high=regime_results['n_obs_high'],
            transition_probs=regime_results['transition_probs'],
            
            # Model info
            n_obs=self.data['n_obs'],
            n_coint=self.n_coint,
            n_lags=self.n_lags,
            converged=True
        )
        
        # Calculate information criteria
        self._calculate_information_criteria()
    
    def _calculate_information_criteria(self) -> None:
        """Calculate AIC, BIC for model comparison."""
        n_obs = self.vecm_results.n_obs
        n_vars = self.data['n_vars']
        
        # Calculate number of parameters more accurately
        # For each regime: alpha (n_vars x n_coint) + gamma (if lags > 1) + covariance matrix
        n_params_per_regime = n_vars * self.n_coint  # Alpha parameters
        if self.n_lags > 1:
            n_params_per_regime += n_vars * n_vars * (self.n_lags - 1)  # Gamma parameters
        n_params_per_regime += n_vars * (n_vars + 1) // 2  # Covariance matrix parameters
        
        # Total: 2 regimes + threshold parameter + cointegrating vectors
        n_params = 2 * n_params_per_regime + 1 + (n_vars * self.n_coint)
        
        # Calculate log likelihood based on residuals
        try:
            residuals = self._calculate_residuals()
            
            # Reshape residuals to 2-D array (n_obs, n_vars)
            residuals_2d = residuals.values.reshape(-1, n_vars)
            
            # Calculate regime-specific log likelihoods
            log_lik = 0
            
            for regime in [0, 1]:
                mask = self.vecm_results.regime_assignment == regime
                if np.any(mask):
                    regime_residuals = residuals_2d[mask]
                    n_regime = np.sum(mask)
                    
                    # Ensure we have at least 2 observations for covariance calculation
                    if n_regime < 2:
                        sigma = np.eye(n_vars)
                        det_sigma = 1.0
                    else:
                        # Multivariate normal log likelihood
                        sigma = np.cov(regime_residuals.T)
                        det_sigma = np.linalg.det(sigma)
                    
                    if det_sigma > 0:
                        log_lik += -0.5 * n_regime * (n_vars * np.log(2 * np.pi) + np.log(det_sigma))
                        # Add quadratic form with singular matrix guard
                        try:
                            sigma_inv = np.linalg.inv(sigma)
                        except np.linalg.LinAlgError:
                            # Use pseudo-inverse if singular
                            sigma_inv = np.linalg.pinv(sigma)
                        for i in range(n_regime):
                            res = regime_residuals[i].reshape(-1, 1)
                            log_lik += -0.5 * (res.T @ sigma_inv @ res)[0, 0]
        except Exception as e:
            warning(f"Could not calculate exact log likelihood: {e}")
            # Fallback to approximate calculation
            log_lik = -n_obs * n_vars * np.log(2 * np.pi) / 2
        
        self.vecm_results.aic = -2 * log_lik + 2 * n_params
        self.vecm_results.bic = -2 * log_lik + np.log(n_obs) * n_params
        self.vecm_results.log_likelihood = float(log_lik)
        self.vecm_results.n_params = int(n_params)
    
    def predict(self, steps: int = 1,
                exog: Optional[pd.DataFrame] = None,
                regime: Optional[str] = None) -> pd.DataFrame:
        """Generate predictions from the fitted model.
        
        Args:
            steps: Number of steps ahead to forecast
            exog: Optional exogenous variables (including future conflict)
            regime: Force specific regime ('low', 'high', or None for auto)
            
        Returns:
            DataFrame of predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        with timer("predict"):
            info(f"Generating {steps}-step ahead predictions")
            
            # Get last values
            last_prices = self.data['log_prices'].iloc[-1].values
            
            # Determine starting regime
            if regime is None:
                # Use last observed regime
                last_regime = self.vecm_results.regime_assignment[-1]
                current_regime = 'high' if last_regime == 1 else 'low'
            else:
                current_regime = regime
            
            predictions = []
            current_prices = last_prices.copy()
            
            for h in range(steps):
                # Get regime-specific parameters
                if current_regime == 'low':
                    alpha = self.vecm_results.low_regime_alpha
                else:
                    alpha = self.vecm_results.high_regime_alpha
                
                # Calculate error correction term
                if self.vecm_results.beta is not None:
                    ect = current_prices @ self.vecm_results.beta
                    expected_change = alpha @ ect
                else:
                    expected_change = np.zeros_like(current_prices)
                
                # Update prices
                current_prices += expected_change
                predictions.append(current_prices)
                
                # Potentially switch regime based on transition probabilities
                if regime is None and h < steps - 1:
                    trans_probs = self.vecm_results.transition_probs
                    regime_idx = 0 if current_regime == 'low' else 1
                    if np.random.rand() > trans_probs[regime_idx, regime_idx]:
                        current_regime = 'high' if current_regime == 'low' else 'low'
            
            # Convert to DataFrame
            pred_df = pd.DataFrame(
                np.exp(np.array(predictions)),  # Convert back from log
                columns=self.data['markets'],
                index=pd.date_range(
                    start=self.data['dates'][-1] + pd.Timedelta(days=30),
                    periods=steps,
                    freq='M'
                )
            )
            
            return pred_df
    
    def _calculate_residuals(self) -> pd.Series:
        """Calculate model residuals for each regime."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        dlogs = self.data['dlogs'].values[1:]
        dates = self.data['dates'][1:]
        markets = self.data['markets']
        
        # Get predicted values for each regime
        residuals = np.zeros_like(dlogs)
        
        # Low regime
        low_mask = self.vecm_results.regime_assignment == 0
        if np.any(low_mask) and self.vecm_results.low_regime_alpha is not None:
            # Simplified: just use adjustment speed times ECT
            if self.vecm_results.beta is not None:
                log_prices = self.data['log_prices'].values[:-1]
                ect = self.calculate_error_correction_terms(
                    log_prices[low_mask], 
                    self.vecm_results.beta
                )
                predicted = ect @ self.vecm_results.low_regime_alpha.T
                residuals[low_mask] = dlogs[low_mask] - predicted
        
        # High regime
        high_mask = self.vecm_results.regime_assignment == 1
        if np.any(high_mask) and self.vecm_results.high_regime_alpha is not None:
            if self.vecm_results.beta is not None:
                log_prices = self.data['log_prices'].values[:-1]
                ect = self.calculate_error_correction_terms(
                    log_prices[high_mask], 
                    self.vecm_results.beta
                )
                predicted = ect @ self.vecm_results.high_regime_alpha.T
                residuals[high_mask] = dlogs[high_mask] - predicted
        
        # Convert to Series with MultiIndex
        return pd.Series(
            residuals.flatten(),
            index=pd.MultiIndex.from_product([
                dates, markets
            ], names=['date', 'market'])
        )
    
    def analyze_regime_dynamics(self) -> Dict[str, Any]:
        """Analyze differences between regimes.
        
        Returns:
            Dictionary with regime comparison statistics
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        results = {
            'n_obs_low': self.vecm_results.n_obs_low,
            'n_obs_high': self.vecm_results.n_obs_high,
            'threshold': self.vecm_results.threshold_value,
            'threshold_variable': self.vecm_results.threshold_variable
        }
        
        # Compare adjustment speeds
        if (self.vecm_results.low_regime_alpha is not None and 
            self.vecm_results.high_regime_alpha is not None):
            
            alpha_diff = self.vecm_results.high_regime_alpha - self.vecm_results.low_regime_alpha
            results['alpha_difference'] = alpha_diff
            results['mean_alpha_diff'] = float(np.mean(np.abs(alpha_diff)))
            
            # Test if adjustment speeds are significantly different
            # Use average of regime-specific standard errors
            if (hasattr(self.vecm_results, 'low_regime_alpha_se') and 
                hasattr(self.vecm_results, 'high_regime_alpha_se') and
                self.vecm_results.low_regime_alpha_se is not None and
                self.vecm_results.high_regime_alpha_se is not None):
                # Pooled standard error for difference
                se_low = self.vecm_results.low_regime_alpha_se
                se_high = self.vecm_results.high_regime_alpha_se
                se_diff = np.sqrt(se_low**2 + se_high**2)
                t_stats = alpha_diff / (se_diff + 1e-10)
            else:
                # Conservative estimate using delta method approximation
                # Assume SE is proportional to |alpha|
                se_approx = np.sqrt(
                    (self.vecm_results.low_regime_alpha * 0.2)**2 + 
                    (self.vecm_results.high_regime_alpha * 0.2)**2
                )
                t_stats = alpha_diff / (se_approx + 1e-10)
            
            # Two-sided test
            p_values = 2 * (1 - stats.norm.cdf(np.abs(t_stats)))
            results['alpha_diff_significant'] = np.any(p_values < 0.05)
            results['alpha_diff_p_values'] = p_values
            
            info(f"Mean absolute difference in adjustment speeds: {results['mean_alpha_diff']:.4f}")
            
            if results['alpha_diff_significant']:
                info("Adjustment speeds differ significantly between regimes")
            else:
                warning("No significant difference in adjustment speeds between regimes")
        
        # Analyze transition dynamics
        trans_probs = self.vecm_results.transition_probs
        results['prob_stay_low'] = float(trans_probs[0, 0])
        results['prob_stay_high'] = float(trans_probs[1, 1])
        results['expected_low_duration'] = 1 / (1 - trans_probs[0, 0] + 1e-10)
        results['expected_high_duration'] = 1 / (1 - trans_probs[1, 1] + 1e-10)
        
        info(f"Expected duration - Low regime: {results['expected_low_duration']:.1f} months, "
             f"High regime: {results['expected_high_duration']:.1f} months")
        
        return results
    
    def plot_threshold_dynamics(self, save_path: Optional[str] = None) -> None:
        """Plot threshold variable and regime assignment over time.
        
        Args:
            save_path: Optional path to save figure
        """
        import matplotlib.pyplot as plt
        import matplotlib.patches as mpatches
        
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        
        dates = self.data['dates'][1:]
        
        # Top panel: Threshold variable
        threshold_data = self.data.get('threshold_data')
        if threshold_data is not None:
            ax1.plot(dates, threshold_data[1:], 'b-', label=self.threshold_variable)
            ax1.axhline(self.threshold, color='r', linestyle='--', 
                       label=f'Threshold = {self.threshold:.1f}')
            ax1.set_ylabel(self.threshold_variable.replace('_', ' ').title())
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        
        # Bottom panel: Regime indicator
        regime = self.vecm_results.regime_assignment
        
        # Create filled areas for high regime
        for i in range(len(regime)):
            if regime[i] == 1:
                ax2.axvspan(dates[i], dates[min(i+1, len(dates)-1)], 
                           alpha=0.3, color='red')
        
        # Add dummy lines for legend
        low_patch = mpatches.Patch(color='white', label='Low conflict regime')
        high_patch = mpatches.Patch(color='red', alpha=0.3, label='High conflict regime')
        ax2.legend(handles=[low_patch, high_patch])
        
        ax2.set_ylabel('Regime')
        ax2.set_ylim(-0.1, 1.1)
        ax2.set_yticks([0, 1])
        ax2.set_yticklabels(['Low', 'High'])
        ax2.grid(True, alpha=0.3)
        ax2.set_xlabel('Date')
        
        plt.suptitle(f'Threshold Dynamics - {self.name}')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            info(f"Saved threshold dynamics plot to {save_path}")
        else:
            plt.show()