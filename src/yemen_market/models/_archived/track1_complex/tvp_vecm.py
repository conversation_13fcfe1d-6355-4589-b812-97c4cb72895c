"""Bayesian Time-Varying Parameter VECM implementation.

This module implements a sophisticated Bayesian TVP-VECM model where
parameters can evolve over time in response to conflict intensity.
Uses PyMC for Bayesian inference with careful priors and efficient sampling.
"""

import numpy as np
import pandas as pd
import pymc as pm
import pytensor.tensor as pt
from typing import Dict, Any, Optional, List, Tuple, Union
import arviz as az
from dataclasses import dataclass

from ...utils.logging import bind, timer, info, warning, error, log_metric, log_data_shape, progress
from ..base import VECMBase, VECMResults, ModelType


@dataclass
class BayesianVECMResults(VECMResults):
    """Extended results for Bayesian VECM models."""
    
    # Posterior samples
    trace: Optional[az.InferenceData] = None
    posterior_means: Optional[Dict[str, np.ndarray]] = None
    posterior_stds: Optional[Dict[str, np.ndarray]] = None
    
    # Convergence diagnostics
    rhat: Optional[Dict[str, float]] = None
    ess: Optional[Dict[str, float]] = None
    
    # Model comparison
    waic: Optional[float] = None
    loo: Optional[float] = None
    
    # Time-varying parameters
    alpha_path: Optional[np.ndarray] = None  # T x N x r
    beta_path: Optional[np.ndarray] = None   # T x N x r
    volatility_path: Optional[np.ndarray] = None  # T x N


class BayesianTVPVECM(VECMBase):
    """Simplified Bayesian VECM with discrete regime changes.
    
    Instead of continuous time-varying parameters, this model allows for
    discrete regime changes at major conflict events (e.g., >50 events/month).
    This improves identification and computational efficiency.
    
    Key features:
    - Discrete regimes based on conflict threshold
    - Regime-specific adjustment speeds
    - Hierarchical priors for efficient estimation
    - Robust to outliers through Student-t errors
    """
    
    def __init__(self, n_coint: int = 1, n_lags: int = 2,
                 n_samples: int = 2000, n_chains: int = 4,
                 target_accept: float = 0.8,
                 random_seed: int = 42,
                 name: str = None):
        """Initialize Bayesian TVP-VECM.
        
        Args:
            n_coint: Number of cointegrating relationships
            n_lags: Number of lags in the model
            n_samples: Number of MCMC samples per chain
            n_chains: Number of MCMC chains
            target_accept: Target acceptance rate for NUTS sampler
            random_seed: Random seed for reproducibility
            name: Optional model name
        """
        super().__init__(n_coint=n_coint, n_lags=n_lags, name=name or "BayesianTVP-VECM")
        self.n_samples = n_samples
        self.n_chains = n_chains
        self.target_accept = target_accept
        self.random_seed = random_seed
        self.model = None
        self.trace = None
        
        bind(model=self.name, model_type=ModelType.BAYESIAN_VECM.value)
        
    def build_model(self, data: Dict[str, Any], 
                   conflict_intensity: pd.Series,
                   conflict_threshold: float = 50.0) -> pm.Model:
        """Build the PyMC model with discrete regime changes.
        
        Args:
            data: Prepared data from prepare_data method
            conflict_intensity: Time series of conflict intensity
            conflict_threshold: Threshold for regime changes (default: 50)
            
        Returns:
            PyMC model object
        """
        with timer("build_bayesian_model"):
            info("Building Simplified Bayesian VECM with discrete regimes")
            
            # Extract data
            log_prices = data['log_prices'].values
            dlogs = data['dlogs'].values
            n_obs, n_vars = dlogs.shape
            markets = data['markets']
            
            # Create regime indicators based on conflict threshold
            conflict_aligned = conflict_intensity.reindex(data['dlogs'].index).fillna(0).values
            high_conflict = (conflict_aligned > conflict_threshold).astype(int)
            
            # Count observations in each regime
            n_low_regime = np.sum(high_conflict == 0)
            n_high_regime = np.sum(high_conflict == 1)
            info(f"Low conflict regime: {n_low_regime} obs, High conflict regime: {n_high_regime} obs")
            
            with pm.Model() as model:
                # === Hierarchical Priors ===
                info("Setting hierarchical priors for regime-specific parameters")
                
                # Global hyperpriors for adjustment speeds
                alpha_mu_global = pm.Normal('alpha_mu_global', mu=-0.5, sigma=0.25)
                alpha_sigma_global = pm.HalfNormal('alpha_sigma_global', sigma=0.1)
                
                # Regime-specific adjustment speeds (hierarchical)
                alpha_low = pm.Normal('alpha_low', 
                                     mu=alpha_mu_global,
                                     sigma=alpha_sigma_global,
                                     shape=(n_vars, self.n_coint))
                
                alpha_high = pm.Normal('alpha_high',
                                      mu=alpha_mu_global,
                                      sigma=alpha_sigma_global,
                                      shape=(n_vars, self.n_coint))
                
                # Cointegrating vectors (constant across regimes)
                beta = pm.Normal('beta', mu=0, sigma=1, shape=(n_vars, self.n_coint))
                
                # Regime-specific volatilities
                sigma_low = pm.HalfNormal('sigma_low', sigma=0.1, shape=n_vars)
                sigma_high = pm.HalfNormal('sigma_high', sigma=0.2, shape=n_vars)
                
                # === VECM Dynamics ===
                info("Constructing regime-switching VECM likelihood")
                
                # Calculate error correction terms
                ect = pm.math.dot(log_prices[:-1], beta)  # (T-1) x r
                
                # Expected changes based on regime
                expected_dlogs = []
                volatilities = []
                
                for t in range(1, n_obs):
                    if high_conflict[t] == 0:
                        # Low conflict regime
                        alpha_current = alpha_low
                        sigma_current = sigma_low
                    else:
                        # High conflict regime  
                        alpha_current = alpha_high
                        sigma_current = sigma_high
                    
                    # Error correction effect
                    ec_effect = pm.math.dot(alpha_current, ect[t-1].T)
                    expected_dlogs.append(ec_effect)
                    volatilities.append(sigma_current)
                
                expected_dlogs = pt.stack(expected_dlogs)
                volatilities = pt.stack(volatilities)
                
                # === Likelihood ===
                # Using Student-t for robustness to outliers
                nu = pm.Gamma('nu', alpha=5, beta=1)  # Degrees of freedom
                
                # Observations
                obs = pm.StudentT('obs',
                                 nu=nu,
                                 mu=expected_dlogs,
                                 sigma=volatilities,
                                 observed=dlogs[1:])
                
                # === Derived Quantities ===
                # Difference in adjustment speeds between regimes
                alpha_diff = pm.Deterministic('alpha_diff', alpha_high - alpha_low)
                
                # Average adjustment speed difference
                avg_alpha_diff = pm.Deterministic('avg_alpha_diff', 
                                                 pm.math.mean(pm.math.abs(alpha_diff)))
                
                log_metric("model_parameters", 
                          2 * n_vars * self.n_coint +      # Regime-specific alphas
                          n_vars * self.n_coint +           # Beta
                          2 * n_vars +                      # Regime-specific sigmas
                          5)                                # Hyperparameters
                
            info(f"Model built with {n_vars} markets and 2 regimes")
            return model
    
    def fit(self, data: pd.DataFrame, 
            conflict_col: str = 'conflict_intensity',
            price_col: str = 'price_usd',
            **kwargs) -> 'BayesianTVPVECM':
        """Fit the Bayesian TVP-VECM model.
        
        Args:
            data: Panel data with prices and conflict intensity
            conflict_col: Name of conflict intensity column
            price_col: Name of price column
            **kwargs: Additional arguments for prepare_data
            
        Returns:
            Self for method chaining
        """
        with timer("fit_bayesian_tvp_vecm"):
            info("Starting Bayesian TVP-VECM estimation")
            
            # Prepare data
            prepared_data = self.prepare_data(data, price_cols=[price_col], **kwargs)
            self.data = prepared_data
            
            # Extract conflict intensity
            conflict_intensity = data.groupby('date')[conflict_col].mean()
            
            # Test for cointegration
            coint_results = self.test_cointegration(prepared_data)
            self.n_coint = coint_results['selected_rank']
            info(f"Selected cointegration rank: {self.n_coint}")
            
            if self.n_coint == 0:
                warning("No cointegration found, model may be misspecified")
            
            # Build model
            self.model = self.build_model(prepared_data, conflict_intensity)
            
            # Sample from posterior
            with self.model:
                info(f"Starting MCMC sampling with {self.n_chains} chains")
                
                with progress("MCMC sampling", total=self.n_chains) as update:
                    self.trace = pm.sample(
                        self.n_samples,
                        chains=self.n_chains,
                        target_accept=self.target_accept,
                        random_seed=self.random_seed,
                        progressbar=False,  # We use our own progress bar
                        callback=lambda *args: update(1/self.n_samples)
                    )
                
                # Compute posterior predictive
                info("Computing posterior predictive samples")
                # Attach posterior-predictive group to the main trace
                self.trace = pm.sample_posterior_predictive(
                    self.trace,
                    extend_inferencedata=True
                )
            
            # Process results
            self._process_posterior()
            self.is_fitted = True
            
            # Run convergence diagnostics
            self._check_convergence()
            
            log_metric("bayesian_fit_complete", 1)
            return self
    
    def _process_posterior(self) -> None:
        """Process posterior samples to extract key quantities."""
        with timer("process_posterior"):
            info("Processing posterior samples")
            
            # Extract posterior means and standard deviations
            posterior_means = {}
            posterior_stds = {}
            
            var_names = ['alpha_low', 'alpha_high', 'beta', 'sigma_low', 'sigma_high',
                        'nu', 'alpha_diff', 'avg_alpha_diff']
            
            for var in var_names:
                if var in self.trace.posterior:
                    posterior_means[var] = self.trace.posterior[var].mean(dim=['chain', 'draw']).values
                    posterior_stds[var] = self.trace.posterior[var].std(dim=['chain', 'draw']).values
            
            # Create results object
            self.vecm_results = BayesianVECMResults(
                alpha=posterior_means.get('alpha_low', np.array([])),  # Use low regime as baseline
                beta=posterior_means.get('beta', np.array([])),
                posterior_means=posterior_means,
                posterior_stds=posterior_stds,
                alpha_path=None,  # No continuous paths in simplified model
                trace=self.trace,
                n_obs=self.data['n_obs'],
                n_coint=self.n_coint,
                n_lags=self.n_lags,
                converged=True  # Will be updated by convergence check
            )
            
            # Compute information criteria
            self._compute_information_criteria()
    
    def _check_convergence(self) -> None:
        """Check MCMC convergence using R-hat and ESS."""
        info("Checking MCMC convergence")
        
        # Compute R-hat
        rhat = az.rhat(self.trace.posterior)
        max_rhat = max(float(v.max().values) for v in rhat.data_vars.values() if v.size > 0)
        
        # Compute effective sample size
        ess = az.ess(self.trace)
        min_ess = min(float(v.min().values) for v in ess.data_vars.values() if v.size > 0)
        
        # Log metrics
        log_metric("max_rhat", max_rhat)
        log_metric("min_ess", min_ess)
        
        # Check convergence criteria
        converged = max_rhat < 1.01 and min_ess > 400
        
        if not converged:
            warning(f"Convergence issues detected: max R-hat={max_rhat:.3f}, min ESS={min_ess:.0f}")
        else:
            info(f"Model converged: max R-hat={max_rhat:.3f}, min ESS={min_ess:.0f}")
        
        self.vecm_results.converged = converged
        self.vecm_results.rhat = {k: float(v.mean()) for k, v in rhat.data_vars.items()}
        self.vecm_results.ess = {k: float(v.mean()) for k, v in ess.data_vars.items()}

    def _compute_information_criteria(self) -> None:
        """Compute WAIC and LOO for model comparison."""
        with timer("compute_ic"):
            info("Computing information criteria")
            
            try:
                waic = az.waic(self.trace)
                self.vecm_results.waic = float(waic.waic)
                
                loo = az.loo(self.trace)
                self.vecm_results.loo = float(loo.loo)
                
                log_metric("waic", self.vecm_results.waic)
                log_metric("loo", self.vecm_results.loo)
            except Exception as e:
                warning(f"Could not compute information criteria: {e}")
    
    def predict(self, steps: int = 1, 
                exog: Optional[pd.DataFrame] = None,
                include_uncertainty: bool = True) -> Union[pd.DataFrame, Tuple[pd.DataFrame, pd.DataFrame]]:
        """Generate predictions from the fitted model.
        
        Args:
            steps: Number of steps ahead to forecast
            exog: Optional exogenous variables (including future conflict)
            include_uncertainty: Whether to return prediction intervals
            
        Returns:
            DataFrame of predictions, optionally with uncertainty bounds
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        with timer("predict"):
            info(f"Generating {steps}-step ahead predictions")
            
            # Extract last values
            last_prices = self.data['log_prices'].iloc[-1].values
            last_alpha = self.vecm_results.alpha_path[-1] if self.vecm_results.alpha_path is not None else self.vecm_results.alpha
            
            predictions = []
            lower_bounds = []
            upper_bounds = []
            
            current_prices = last_prices.copy()
            
            for h in range(steps):
                # Calculate error correction term
                ect = current_prices @ self.vecm_results.beta
                
                # Expected change
                expected_change = last_alpha @ ect
                
                # Add uncertainty if requested
                if include_uncertainty:
                    # Sample from posterior predictive
                    posterior_samples = self.trace.posterior_predictive['obs'].values[:, :, -1, :]
                    change_samples = posterior_samples.reshape(-1, self.data['n_vars'])
                    
                    lower = np.percentile(change_samples, 2.5, axis=0)
                    upper = np.percentile(change_samples, 97.5, axis=0)
                    
                    lower_bounds.append(current_prices + lower)
                    upper_bounds.append(current_prices + upper)
                
                # Update prices
                current_prices += expected_change
                predictions.append(current_prices)
            
            # Convert to DataFrame
            pred_df = pd.DataFrame(
                np.exp(np.array(predictions)),  # Convert back from log
                columns=self.data['markets'],
                index=pd.date_range(
                    start=self.data['dates'][-1] + pd.Timedelta(days=30),
                    periods=steps,
                    freq='M'
                )
            )
            
            if include_uncertainty:
                lower_df = pd.DataFrame(
                    np.exp(np.array(lower_bounds)),
                    columns=self.data['markets'],
                    index=pred_df.index
                )
                upper_df = pd.DataFrame(
                    np.exp(np.array(upper_bounds)),
                    columns=self.data['markets'],
                    index=pred_df.index
                )
                return pred_df, (lower_df, upper_df)
            
            return pred_df
    
    def _calculate_residuals(self) -> pd.Series:
        """Calculate model residuals."""
        # Use posterior mean predictions
        posterior_pred = self.trace.posterior_predictive['obs'].mean(dim=['chain', 'draw'])
        observed = self.data['dlogs'].iloc[1:].values
        residuals = observed - posterior_pred.values
        
        return pd.Series(
            residuals.flatten(),
            index=pd.MultiIndex.from_product([
                self.data['dates'][1:],
                self.data['markets']
            ], names=['date', 'market'])
        )
    
    def plot_time_varying_parameters(self, market_idx: int = 0, 
                                    save_path: Optional[str] = None) -> None:
        """Plot evolution of time-varying parameters.
        
        Args:
            market_idx: Index of market to plot
            save_path: Optional path to save figure
        """
        import matplotlib.pyplot as plt
        
        if self.vecm_results.alpha_path is None:
            warning("No time-varying parameters to plot")
            return
        
        fig, axes = plt.subplots(2, 1, figsize=(12, 8))
        
        # Plot adjustment speed evolution
        market_name = self.data['markets'][market_idx]
        alpha_evolution = self.vecm_results.alpha_path[:, market_idx, 0]
        
        axes[0].plot(self.data['dates'][1:], alpha_evolution)
        axes[0].set_title(f'Adjustment Speed Evolution - {market_name}')
        axes[0].set_ylabel('Alpha')
        axes[0].grid(True, alpha=0.3)
        
        # Plot with uncertainty bounds if available
        if f'alpha_{market_idx}_0' in self.trace.posterior:
            alpha_samples = self.trace.posterior[f'alpha_{market_idx}_0'].values
            lower = np.percentile(alpha_samples, 2.5, axis=(0, 1))
            upper = np.percentile(alpha_samples, 97.5, axis=(0, 1))
            
            axes[0].fill_between(self.data['dates'][1:], lower, upper, 
                            alpha=0.3, label='95% Credible Interval')
            axes[0].legend()

        # Plot volatility evolution if available
        if hasattr(self.trace.posterior, 'log_vol'):
            vol_samples = np.exp(self.trace.posterior['log_vol'].mean(dim=['chain', 'draw']))
            axes[1].plot(self.data['dates'][1:], vol_samples[:, market_idx])
            axes[1].set_title(f'Volatility Evolution - {market_name}')
            axes[1].set_ylabel('Volatility')
            axes[1].set_xlabel('Date')
            axes[1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            info(f"Saved time-varying parameter plot to {save_path}")
        else:
            plt.show()
        
    def analyze_conflict_impact(self) -> Dict[str, Any]:
        """Analyze differences between conflict regimes.
        
        Returns:
            Dictionary with regime comparison analysis
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        results = {
            'avg_alpha_diff': float(
                self.vecm_results.posterior_means.get('avg_alpha_diff', 0)
            ),
            'avg_alpha_diff_std': float(
                self.vecm_results.posterior_stds.get('avg_alpha_diff', 0)
            ),
            'significant': False
        }
        
        # Check if regime difference is significant
        if 'alpha_diff' in self.trace.posterior:
            # Get samples of alpha differences
            alpha_diff_samples = self.trace.posterior['alpha_diff'].values
            
            # Test if any adjustment speeds differ significantly
            # Compute proportion of samples where |diff| > 0
            prob_different = np.mean(np.abs(alpha_diff_samples) > 0.01, axis=(0, 1))
            
            # Market-level results
            results['market_differences'] = {}
            for i, market in enumerate(self.data['markets']):
                for j in range(self.n_coint):
                    key = f"{market}_coint{j}"
                    market_samples = alpha_diff_samples[:, :, i, j].flatten()
                    
                    ci_lower = np.percentile(market_samples, 2.5)
                    ci_upper = np.percentile(market_samples, 97.5)
                    
                    results['market_differences'][key] = {
                        'mean_diff': float(np.mean(market_samples)),
                        'ci_lower': float(ci_lower),
                        'ci_upper': float(ci_upper),
                        'significant': ci_lower > 0 or ci_upper < 0
                    }
            
            # Overall assessment
            n_significant = sum(
                1 for m in results['market_differences'].values() 
                if m['significant']
            )
            results['n_significant_differences'] = n_significant
            results['significant'] = n_significant > 0
            
            info(f"Average |alpha_diff|: {results['avg_alpha_diff']:.4f}")
            info(f"Markets with significant regime differences: {n_significant}/{len(self.data['markets'])}")
            
            if results['significant']:
                info("Conflict regimes have significantly different adjustment speeds")
            else:
                info("No significant differences between conflict regimes")
        
        return results