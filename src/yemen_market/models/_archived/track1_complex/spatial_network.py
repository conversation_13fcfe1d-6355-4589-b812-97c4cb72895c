"""Spatial network components for econometric models.

This module implements spatial weight matrices and network-augmented
models that account for geographic spillovers and market connectivity.
"""

import numpy as np
import pandas as pd
import geopandas as gpd
from typing import Dict, Any, Optional, List, Tuple, Union
from scipy.spatial.distance import cdist
from scipy.sparse import csr_matrix, diags
import networkx as nx
from shapely.geometry import Point

from ...utils.logging import bind, timer, info, warning, error, log_metric, log_data_shape, progress
from ..base import SpatialModelMixin


class SpatialWeightMatrix:
    """Create and manage spatial weight matrices for market integration analysis.
    
    Supports multiple weight specifications:
    - Distance-based (inverse distance, exponential decay)
    - Contiguity-based (shared control zones)
    - Network-based (trade routes, conflict corridors)
    - Hybrid (combining multiple criteria)
    """
    
    def __init__(self, markets_df: pd.DataFrame,
                 weight_type: str = 'distance',
                 normalize: bool = True):
        """Initialize spatial weight matrix.
        
        Args:
            markets_df: DataFrame with market info (must have lat/lon or geometry)
            weight_type: Type of weights ('distance', 'contiguity', 'network', 'hybrid')
            normalize: Whether to row-normalize the weight matrix
        """
        self.markets_df = markets_df.copy()
        self.weight_type = weight_type
        self.normalize = normalize
        self.n_markets = len(markets_df)
        self.market_names = markets_df['market_name'].tolist()
        self.W = None
        
        bind(spatial_weights=weight_type)
        
    def create_weight_matrix(self, **kwargs) -> np.ndarray:
        """Create spatial weight matrix based on specified type.
        
        Args:
            **kwargs: Type-specific parameters
            
        Returns:
            Spatial weight matrix (n x n)
        """
        with timer("create_weight_matrix"):
            info(f"Creating {self.weight_type} weight matrix for {self.n_markets} markets")
            
            if self.weight_type == 'distance':
                self.W = self._create_distance_weights(**kwargs)
            elif self.weight_type == 'contiguity':
                self.W = self._create_contiguity_weights(**kwargs)
            elif self.weight_type == 'network':
                self.W = self._create_network_weights(**kwargs)
            elif self.weight_type == 'hybrid':
                self.W = self._create_hybrid_weights(**kwargs)
            else:
                raise ValueError(f"Unknown weight type: {self.weight_type}")
            
            # Ensure no self-connections
            np.fill_diagonal(self.W, 0)
            
            # Normalize if requested
            if self.normalize:
                self.W = self._row_normalize(self.W)
            
            # Log statistics
            self._log_weight_stats()
            
            return self.W
    
    def _create_distance_weights(self, cutoff_km: float = 200,
                               decay: str = 'inverse',
                               alpha: float = 1.0) -> np.ndarray:
        """Create distance-based weight matrix.
        
        Args:
            cutoff_km: Maximum distance for non-zero weights
            decay: Decay function ('inverse', 'exponential', 'gaussian')
            alpha: Decay parameter
            
        Returns:
            Distance weight matrix
        """
        # Extract coordinates
        if 'geometry' in self.markets_df.columns:
            coords = np.array([[geom.x, geom.y] for geom in self.markets_df.geometry])
        else:
            coords = self.markets_df[['longitude', 'latitude']].values
        
        # Calculate pairwise distances (in degrees)
        dist_matrix = cdist(coords, coords)
        
        # Convert to km (approximate)
        dist_matrix_km = dist_matrix * 111.32  # 1 degree ≈ 111.32 km
        
        # Apply cutoff
        W = np.zeros_like(dist_matrix_km)
        mask = (dist_matrix_km > 0) & (dist_matrix_km <= cutoff_km)
        
        # Apply decay function
        if decay == 'inverse':
            W[mask] = 1 / (dist_matrix_km[mask] ** alpha)
        elif decay == 'exponential':
            W[mask] = np.exp(-alpha * dist_matrix_km[mask] / cutoff_km)
        elif decay == 'gaussian':
            W[mask] = np.exp(-0.5 * (dist_matrix_km[mask] / (cutoff_km/3)) ** 2)
        
        info(f"Distance weights: {np.sum(mask)} connections within {cutoff_km}km")
        
        return W
    
    def _create_contiguity_weights(self, zone_col: str = 'control_zone',
                                  boundary_bonus: float = 2.0) -> np.ndarray:
        """Create contiguity-based weight matrix.
        
        Args:
            zone_col: Column containing zone assignments
            boundary_bonus: Extra weight for boundary markets
            
        Returns:
            Contiguity weight matrix
        """
        W = np.zeros((self.n_markets, self.n_markets))
        
        if zone_col not in self.markets_df.columns:
            warning(f"Zone column '{zone_col}' not found, using distance weights")
            return self._create_distance_weights()
        
        # Same zone connections
        for i in range(self.n_markets):
            for j in range(i+1, self.n_markets):
                zone_i = self.markets_df.iloc[i][zone_col]
                zone_j = self.markets_df.iloc[j][zone_col]
                
                if pd.notna(zone_i) and pd.notna(zone_j):
                    if zone_i == zone_j:
                        W[i, j] = W[j, i] = 1.0
                    
                    # Boundary market bonus
                    if ('is_boundary' in self.markets_df.columns and
                        (self.markets_df.iloc[i]['is_boundary'] or 
                         self.markets_df.iloc[j]['is_boundary'])):
                        W[i, j] *= boundary_bonus
                        W[j, i] *= boundary_bonus
        
        info(f"Contiguity weights: {np.sum(W > 0) // 2} connections")
        
        return W
    
    def _create_network_weights(self, trade_routes: Optional[List[Tuple[str, str]]] = None,
                              conflict_corridors: Optional[pd.DataFrame] = None) -> np.ndarray:
        """Create network-based weight matrix.
        
        Args:
            trade_routes: List of (market1, market2) trade connections
            conflict_corridors: DataFrame with conflict intensity between markets
            
        Returns:
            Network weight matrix
        """
        # Start with distance-based network
        W = self._create_distance_weights(cutoff_km=300, decay='exponential')
        
        # Enhance with trade routes if provided
        if trade_routes:
            for market1, market2 in trade_routes:
                if market1 in self.market_names and market2 in self.market_names:
                    i = self.market_names.index(market1)
                    j = self.market_names.index(market2)
                    W[i, j] = W[j, i] = max(W[i, j], 2.0)  # Strong connection
        
        # Modify based on conflict corridors
        if conflict_corridors is not None:
            # Reduce weights where conflict is high
            # (markets separated by conflict are less integrated)
            pass  # Placeholder for conflict-based adjustments
        
        info("Network weights created with trade and conflict adjustments")
        
        return W
    
    def _create_hybrid_weights(self, distance_weight: float = 0.5,
                             contiguity_weight: float = 0.3,
                             network_weight: float = 0.2) -> np.ndarray:
        """Create hybrid weight matrix combining multiple criteria.
        
        Args:
            distance_weight: Weight for distance component
            contiguity_weight: Weight for contiguity component
            network_weight: Weight for network component
            
        Returns:
            Hybrid weight matrix
        """
        # Normalize component weights
        total = distance_weight + contiguity_weight + network_weight
        distance_weight /= total
        contiguity_weight /= total
        network_weight /= total
        
        # Create component matrices
        W_dist = self._create_distance_weights()
        W_cont = self._create_contiguity_weights()
        W_net = self._create_network_weights()
        
        # Normalize each to [0, 1]
        for W in [W_dist, W_cont, W_net]:
            if W.max() > 0:
                W /= W.max()
        
        # Combine
        W = (distance_weight * W_dist + 
             contiguity_weight * W_cont + 
             network_weight * W_net)
        
        info("Hybrid weights created from multiple components")
        
        return W
    
    def _row_normalize(self, W: np.ndarray) -> np.ndarray:
        """Row-normalize weight matrix so rows sum to 1."""
        row_sums = W.sum(axis=1)
        # Avoid division by zero
        row_sums[row_sums == 0] = 1
        return W / row_sums[:, np.newaxis]
    
    def _log_weight_stats(self) -> None:
        """Log statistics about the weight matrix."""
        if self.W is None:
            return
        
        n_connections = np.sum(self.W > 0)
        avg_neighbors = n_connections / self.n_markets
        max_weight = np.max(self.W)
        
        log_metric("n_spatial_connections", n_connections)
        log_metric("avg_neighbors_per_market", avg_neighbors)
        log_metric("max_spatial_weight", max_weight)
        
        # Check for isolated markets
        isolated = np.sum(self.W.sum(axis=1) == 0)
        if isolated > 0:
            warning(f"{isolated} markets have no spatial connections")
    
    def get_neighbors(self, market: str, threshold: float = 0.1) -> List[Tuple[str, float]]:
        """Get neighbors of a market with weights above threshold.
        
        Args:
            market: Market name
            threshold: Minimum weight threshold
            
        Returns:
            List of (neighbor_name, weight) tuples
        """
        if self.W is None:
            raise ValueError("Weight matrix not created yet")
        
        if market not in self.market_names:
            raise ValueError(f"Market '{market}' not found")
        
        idx = self.market_names.index(market)
        weights = self.W[idx]
        
        neighbors = []
        for i, w in enumerate(weights):
            if w > threshold and i != idx:
                neighbors.append((self.market_names[i], float(w)))
        
        return sorted(neighbors, key=lambda x: x[1], reverse=True)
    
    def calculate_spatial_lag(self, values: pd.Series) -> pd.Series:
        """Calculate spatial lag of a variable.
        
        Args:
            values: Series with values for each market
            
        Returns:
            Series with spatial lag values
        """
        if self.W is None:
            raise ValueError("Weight matrix not created yet")
        
        # Align values with market order
        aligned_values = values.reindex(self.market_names).fillna(0).values
        
        # Calculate spatial lag: W * y
        spatial_lag = self.W @ aligned_values
        
        return pd.Series(spatial_lag, index=self.market_names)
    
    def to_sparse(self) -> csr_matrix:
        """Convert weight matrix to sparse format for efficiency."""
        if self.W is None:
            raise ValueError("Weight matrix not created yet")
        
        return csr_matrix(self.W)
    
    def plot_network(self, save_path: Optional[str] = None,
                    edge_threshold: float = 0.1) -> None:
        """Plot spatial network visualization.
        
        Args:
            save_path: Optional path to save figure
            edge_threshold: Minimum weight to show edge
        """
        import matplotlib.pyplot as plt
        
        if self.W is None:
            raise ValueError("Weight matrix not created yet")
        
        # Create network graph
        G = nx.Graph()
        
        # Add nodes with positions
        pos = {}
        for idx, row in self.markets_df.iterrows():
            market = row['market_name']
            G.add_node(market)
            
            if 'geometry' in row:
                pos[market] = (row.geometry.x, row.geometry.y)
            else:
                pos[market] = (row['longitude'], row['latitude'])
        
        # Add edges
        for i in range(self.n_markets):
            for j in range(i+1, self.n_markets):
                if self.W[i, j] > edge_threshold:
                    G.add_edge(
                        self.market_names[i],
                        self.market_names[j],
                        weight=self.W[i, j]
                    )
        
        # Plot
        plt.figure(figsize=(12, 10))
        
        # Draw network
        nx.draw_networkx_nodes(G, pos, node_size=300, node_color='lightblue')
        nx.draw_networkx_labels(G, pos, font_size=8)
        
        # Draw edges with width proportional to weight
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        
        nx.draw_networkx_edges(
            G, pos,
            width=[w * 3 for w in weights],
            alpha=0.5
        )
        
        plt.title(f"Spatial Network ({self.weight_type} weights)")
        plt.axis('off')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            info(f"Saved network plot to {save_path}")
        else:
            plt.show()


def create_weight_matrix(data: Union[pd.DataFrame, Dict[str, Any]],
                        weight_type: str = 'distance',
                        **kwargs) -> np.ndarray:
    """Convenience function to create spatial weight matrix.
    
    Args:
        data: DataFrame with market data or dict from model
        weight_type: Type of weights to create
        **kwargs: Additional arguments for weight creation
        
    Returns:
        Spatial weight matrix
    """
    # Extract market data if needed
    if isinstance(data, dict):
        if 'markets_df' in data:
            markets_df = data['markets_df']
        else:
            # Try to reconstruct from panel data
            markets = data.get('markets', [])
            if not markets:
                raise ValueError("No market information found in data")
            
            markets_df = pd.DataFrame({'market_name': markets})
    else:
        markets_df = data
    
    # Create weight matrix
    sw = SpatialWeightMatrix(markets_df, weight_type=weight_type)
    W = sw.create_weight_matrix(**kwargs)
    
    return W


class SpatialVECM(SpatialModelMixin):
    """Spatial Vector Error Correction Model.
    
    Extends standard VECM with spatial lags of dependent and independent
    variables to capture geographic spillovers in price transmission.
    """
    
    def __init__(self, base_model, spatial_weights: np.ndarray):
        """Initialize spatial VECM.
        
        Args:
            base_model: Base VECM model to extend
            spatial_weights: Spatial weight matrix
        """
        self.base_model = base_model
        self.W = spatial_weights
        self.n_markets = self.W.shape[0]
        
    def add_spatial_lags(self, data: pd.DataFrame,
                        lag_variables: List[str]) -> pd.DataFrame:
        """Add spatial lags of specified variables.
        
        Args:
            data: Panel data
            lag_variables: Variables to create spatial lags for
            
        Returns:
            Data with spatial lag columns added
        """
        data_with_lags = data.copy()
        
        for var in lag_variables:
            if var not in data.columns:
                warning(f"Variable '{var}' not found in data")
                continue
            
            # Create spatial lag for each time period
            spatial_lag_values = []
            
            for date in data['date'].unique():
                date_data = data[data['date'] == date].set_index('market_name')[var]
                
                # Calculate spatial lag
                sw = SpatialWeightMatrix(pd.DataFrame(index=date_data.index))
                sw.W = self.W
                sw.market_names = date_data.index.tolist()
                
                spatial_lag = sw.calculate_spatial_lag(date_data)
                
                for market in date_data.index:
                    spatial_lag_values.append({
                        'date': date,
                        'market_name': market,
                        f'spatial_lag_{var}': spatial_lag[market]
                    })
            
            # Merge back
            lag_df = pd.DataFrame(spatial_lag_values)
            data_with_lags = data_with_lags.merge(
                lag_df,
                on=['date', 'market_name'],
                how='left'
            )
        
        return data_with_lags
    
    def test_spatial_autocorrelation(self, residuals: pd.Series) -> Dict[str, float]:
        """Test for spatial autocorrelation in residuals using Moran's I.
        
        Args:
            residuals: Model residuals
            
        Returns:
            Dict with Moran's I statistic and p-value
        """
        from esda.moran import Moran
        
        # Need to implement full Moran's I test
        # Placeholder for now
        return {
            'morans_i': 0.0,
            'p_value': 1.0,
            'interpretation': 'Spatial autocorrelation test pending'
        }