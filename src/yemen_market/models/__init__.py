"""Econometric models for Yemen market integration analysis.

This package implements the three-tier methodology for analyzing market
integration in conflict-affected settings. The old dual-track approach
has been archived in favor of the unified three-tier framework.

Three-Tier Methodology:
- Tier 1: Pooled panel analysis with fixed effects
- Tier 2: Commodity-specific models with thresholds
- Tier 3: Factor analysis and external validation

For the main analysis, use:
>>> from yemen_market.models.three_tier.integration import ThreeTierAnalysis
>>> analysis = ThreeTierAnalysis(config)
>>> results = analysis.run_full_analysis(data)

For migration from old models:
>>> from yemen_market.models.three_tier.migration import ModelMigrationHelper
"""

from .base import BaseEconometricModel, ModelResults
from .model_comparison import ModelComparison, run_model_comparison

# Import main three-tier components for convenience
from .three_tier.integration import ThreeTierAnalysis
from .three_tier.migration import ModelMigrationHelper

__all__ = [
    # Legacy base classes (kept for compatibility)
    'BaseEconometricModel',
    'ModelResults', 
    'ModelComparison',
    'run_model_comparison',
    # New three-tier methodology
    'ThreeTierAnalysis',
    'ModelMigrationHelper'
]

# Deprecation notice for old imports
def __getattr__(name):
    """Provide helpful error messages for deprecated imports."""
    deprecated_modules = {
        'track1_complex': 'three_tier.tier3_validation',
        'track2_simple': 'three_tier.tier2_commodity',
        'worldbank_threshold_vecm': 'three_tier.tier2_commodity.threshold_vecm'
    }
    
    if name in deprecated_modules:
        raise ImportError(
            f"Module '{name}' has been deprecated and moved to archive.\n"
            f"Please use '{deprecated_modules[name]}' instead.\n"
            f"See MIGRATION_GUIDE.md for details."
        )
    
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")