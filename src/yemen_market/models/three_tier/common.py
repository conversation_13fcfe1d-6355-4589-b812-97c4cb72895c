"""Common utilities and classes for three-tier analysis.

This module provides shared classes and utilities used across all tiers
of the three-tier econometric methodology.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
import json

from yemen_market.utils.logging import info, warning, error, bind

# Set module context
bind(module=__name__)


@dataclass
class ModelMetadata:
    """Metadata for model results."""
    model_type: str
    tier: int
    commodity: Optional[str] = None
    estimation_date: datetime = field(default_factory=datetime.now)
    sample_period: Optional[str] = None
    n_observations: Optional[int] = None
    convergence: bool = True
    notes: str = ""


class ResultsContainer:
    """Container for storing and accessing model results across tiers."""
    
    def __init__(self, metadata: Optional[ModelMetadata] = None):
        """Initialize results container.
        
        Parameters
        ----------
        metadata : ModelMetadata, optional
            Model metadata information
        """
        self.metadata = metadata or ModelMetadata(model_type="unknown", tier=0)
        self.results: Dict[str, Any] = {}
        self.diagnostics: Dict[str, Any] = {}
        self.predictions: Optional[pd.DataFrame] = None
        self.residuals: Optional[pd.Series] = None
        self.fitted_values: Optional[pd.Series] = None
        self.parameters: Dict[str, Any] = {}
        self.standard_errors: Dict[str, Any] = {}
        self.statistics: Dict[str, float] = {}
        
        bind(class_name=self.__class__.__name__)
    
    def add_result(self, key: str, value: Any) -> None:
        """Add a result to the container.
        
        Parameters
        ----------
        key : str
            Result identifier
        value : Any
            Result value
        """
        self.results[key] = value
        info(f"Added result: {key}")
    
    def get_result(self, key: str, default: Any = None) -> Any:
        """Get a result from the container.
        
        Parameters
        ----------
        key : str
            Result identifier
        default : Any, optional
            Default value if key not found
            
        Returns
        -------
        Any
            Result value or default
        """
        return self.results.get(key, default)
    
    def add_diagnostic(self, key: str, value: Any) -> None:
        """Add a diagnostic result.
        
        Parameters
        ----------
        key : str
            Diagnostic identifier
        value : Any
            Diagnostic value
        """
        self.diagnostics[key] = value
        info(f"Added diagnostic: {key}")
    
    def get_diagnostic(self, key: str, default: Any = None) -> Any:
        """Get a diagnostic result.
        
        Parameters
        ----------
        key : str
            Diagnostic identifier
        default : Any, optional
            Default value if key not found
            
        Returns
        -------
        Any
            Diagnostic value or default
        """
        return self.diagnostics.get(key, default)
    
    def set_predictions(self, predictions: pd.DataFrame) -> None:
        """Set model predictions.
        
        Parameters
        ----------
        predictions : pd.DataFrame
            Model predictions
        """
        self.predictions = predictions
        info(f"Set predictions with shape {predictions.shape}")
    
    def set_residuals(self, residuals: pd.Series) -> None:
        """Set model residuals.
        
        Parameters
        ----------
        residuals : pd.Series
            Model residuals
        """
        self.residuals = residuals
        info(f"Set residuals with {len(residuals)} observations")
    
    def set_fitted_values(self, fitted_values: pd.Series) -> None:
        """Set fitted values.
        
        Parameters
        ----------
        fitted_values : pd.Series
            Model fitted values
        """
        self.fitted_values = fitted_values
        info(f"Set fitted values with {len(fitted_values)} observations")
    
    def add_parameter(self, name: str, value: float, std_error: Optional[float] = None) -> None:
        """Add a parameter estimate.
        
        Parameters
        ----------
        name : str
            Parameter name
        value : float
            Parameter estimate
        std_error : float, optional
            Standard error of the estimate
        """
        self.parameters[name] = value
        if std_error is not None:
            self.standard_errors[name] = std_error
        info(f"Added parameter: {name} = {value}")
    
    def get_parameter(self, name: str, default: Optional[float] = None) -> Optional[float]:
        """Get a parameter estimate.
        
        Parameters
        ----------
        name : str
            Parameter name
        default : float, optional
            Default value if parameter not found
            
        Returns
        -------
        float or None
            Parameter estimate or default
        """
        return self.parameters.get(name, default)
    
    def add_statistic(self, name: str, value: float) -> None:
        """Add a model statistic.
        
        Parameters
        ----------
        name : str
            Statistic name
        value : float
            Statistic value
        """
        self.statistics[name] = value
        info(f"Added statistic: {name} = {value}")
    
    def get_statistic(self, name: str, default: Optional[float] = None) -> Optional[float]:
        """Get a model statistic.
        
        Parameters
        ----------
        name : str
            Statistic name
        default : float, optional
            Default value if statistic not found
            
        Returns
        -------
        float or None
            Statistic value or default
        """
        return self.statistics.get(name, default)
    
    def summary(self) -> Dict[str, Any]:
        """Get a summary of the results container.
        
        Returns
        -------
        dict
            Summary information
        """
        return {
            'metadata': {
                'model_type': self.metadata.model_type,
                'tier': self.metadata.tier,
                'commodity': self.metadata.commodity,
                'estimation_date': self.metadata.estimation_date.isoformat(),
                'n_observations': self.metadata.n_observations,
                'convergence': self.metadata.convergence
            },
            'n_results': len(self.results),
            'n_diagnostics': len(self.diagnostics),
            'n_parameters': len(self.parameters),
            'n_statistics': len(self.statistics),
            'has_predictions': self.predictions is not None,
            'has_residuals': self.residuals is not None,
            'has_fitted_values': self.fitted_values is not None
        }
    
    def export_results(self, filepath: str) -> None:
        """Export results to file.
        
        Parameters
        ----------
        filepath : str
            Output file path
        """
        export_data = {
            'metadata': {
                'model_type': self.metadata.model_type,
                'tier': self.metadata.tier,
                'commodity': self.metadata.commodity,
                'estimation_date': self.metadata.estimation_date.isoformat(),
                'sample_period': self.metadata.sample_period,
                'n_observations': self.metadata.n_observations,
                'convergence': self.metadata.convergence,
                'notes': self.metadata.notes
            },
            'results': self.results,
            'diagnostics': self.diagnostics,
            'parameters': self.parameters,
            'standard_errors': self.standard_errors,
            'statistics': self.statistics,
            'summary': self.summary()
        }
        
        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, pd.Series):
                return obj.to_dict()
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict('records')
            return obj
        
        # Recursively convert numpy types
        def recursive_convert(obj):
            if isinstance(obj, dict):
                return {k: recursive_convert(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [recursive_convert(item) for item in obj]
            else:
                return convert_numpy(obj)
        
        export_data = recursive_convert(export_data)
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        info(f"Results exported to {filepath}")
    
    def __repr__(self) -> str:
        """String representation of the results container."""
        return f"ResultsContainer(tier={self.metadata.tier}, model_type='{self.metadata.model_type}', n_results={len(self.results)})"
