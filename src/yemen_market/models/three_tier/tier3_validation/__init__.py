"""Tier 3: Factor analysis and conflict validation.

This module implements the third tier of analysis using factor models
to validate market integration patterns and correlate with conflict data.

Components:
- Static and dynamic factor models for dimension reduction
- PCA-based integration strength analysis
- Conflict event impact validation
- Cross-validation tools for integration patterns
"""

from .factor_models import StaticFactorModel, DynamicFactorModel
from .pca_analysis import PCAMarketIntegration
from .conflict_validation import ConflictIntegrationValidator
from yemen_market.utils.logging import bind

# Set module context
bind(module=__name__)

__all__ = [
    'StaticFactorModel',
    'DynamicFactorModel', 
    'PCAMarketIntegration',
    'ConflictIntegrationValidator'
]