"""Conflict event validation for market integration analysis.

This module validates market integration findings against conflict events,
testing whether integration patterns change during periods of violence or
political instability.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from scipy import stats
from statsmodels.tsa.stattools import grangercausalitytests
from statsmodels.stats.diagnostic import het_breuschpagan
import warnings

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel, TierType
from yemen_market.models.three_tier.core.results_container import ResultsContainer
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler

# Set module context
bind(module=__name__)


class ConflictIntegrationValidator(BaseThreeTierModel):
    """Validate market integration patterns against conflict events.
    
    This model tests whether conflict events affect market integration by:
    1. Comparing integration metrics before/during/after conflict
    2. Testing for structural breaks aligned with major conflicts
    3. Analyzing spatial patterns of conflict impact on markets
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize conflict validator.
        
        Parameters
        ----------
        config : dict, optional
            Configuration with keys:
            - conflict_threshold: Min fatalities to consider event (default: 10)
            - window_before: Days before event to analyze (default: 30)
            - window_after: Days after event to analyze (default: 30)
            - min_markets_affected: Min markets for spatial analysis (default: 3)
        """
        super().__init__(TierType.TIER3_VALIDATION, config)
        
        # Configuration
        self.conflict_threshold = config.get('conflict_threshold', 10)
        self.window_before = config.get('window_before', 30)
        self.window_after = config.get('window_after', 30)
        self.min_markets_affected = config.get('min_markets_affected', 3)
        
        # Components
        self.panel_handler = PanelDataHandler()
        self.conflict_data = None
        self.market_integration_scores = None
        self.has_conflict_data = False
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data includes necessary columns.
        
        Parameters
        ----------
        data : pd.DataFrame
            Combined price and conflict data
            
        Returns
        -------
        bool
            True if valid
        """
        # Check for price columns
        price_cols = ['governorate', 'commodity', 'date', 'usd_price']
        missing_price = set(price_cols) - set(data.columns)
        if missing_price:
            error(f"Missing price columns: {missing_price}")
            return False
            
        # Check for conflict columns (if present)
        if 'fatalities' in data.columns or 'event_type' in data.columns:
            info("Conflict data detected in input")
            self.has_conflict_data = True
        else:
            warning("No conflict data found. Will need to be provided separately.")
            self.has_conflict_data = False
            
        return True
    
    def fit(self, data: pd.DataFrame, conflict_data: Optional[pd.DataFrame] = None,
            integration_scores: Optional[pd.DataFrame] = None, **kwargs) -> 'ConflictIntegrationValidator':
        """Fit conflict validation model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Price panel data
        conflict_data : pd.DataFrame, optional
            Conflict events with columns: date, governorate, fatalities, event_type
        integration_scores : pd.DataFrame, optional
            Pre-computed integration scores over time
        **kwargs
            Additional parameters
            
        Returns
        -------
        self
            Fitted model
        """
        with timer("fit_conflict_validator"):
            self._log_estimation_start(data)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
                
            # Store data
            self.data = data
            self.conflict_data = conflict_data
            self.market_integration_scores = integration_scores
            
            # If conflict data not provided, extract from main data
            if conflict_data is None and 'fatalities' in data.columns:
                self.conflict_data = self._extract_conflict_data(data)
            
            # If integration scores not provided, compute them
            if integration_scores is None:
                info("Computing market integration scores")
                self.market_integration_scores = self._compute_integration_scores(data)
            
            # Perform validation analyses
            results = ResultsContainer(
                tier="tier3_validation",
                model_type="conflict_integration_validator"
            )
            
            # 1. Event study analysis
            event_results = self._event_study_analysis()
            results.add_tier_specific(event_study=event_results)
            
            # 2. Structural break analysis
            break_results = self._structural_break_analysis()
            results.add_tier_specific(structural_breaks=break_results)
            
            # 3. Spatial impact analysis
            spatial_results = self._spatial_impact_analysis()
            results.add_tier_specific(spatial_impact=spatial_results)
            
            # 4. Granger causality tests
            if self.conflict_data is not None:
                granger_results = self._granger_causality_tests()
                results.add_tier_specific(granger_causality=granger_results)
            
            # Store results
            self.results = results
            self.is_fitted = True
            
            # Add summary statistics
            self._add_summary_statistics(results)
            
            elapsed_time = timer.get_elapsed("fit_conflict_validator")
            self._log_estimation_complete(elapsed_time)
            
            return self
    
    def _extract_conflict_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Extract conflict data from combined dataset.
        
        Parameters
        ----------
        data : pd.DataFrame
            Combined data
            
        Returns
        -------
        pd.DataFrame
            Conflict events
        """
        conflict_cols = ['date', 'governorate', 'fatalities']
        if 'event_type' in data.columns:
            conflict_cols.append('event_type')
            
        # Get unique conflict events
        conflict_df = data[data['fatalities'] > 0][conflict_cols].drop_duplicates()
        
        # Aggregate by date and governorate
        conflict_df = conflict_df.groupby(['date', 'governorate']).agg({
            'fatalities': 'sum',
            'event_type': lambda x: x.mode()[0] if 'event_type' in conflict_cols else 'Unknown'
        }).reset_index()
        
        info(f"Extracted {len(conflict_df)} conflict events")
        return conflict_df
    
    def _compute_integration_scores(self, data: pd.DataFrame) -> pd.DataFrame:
        """Compute rolling market integration scores.
        
        Parameters
        ----------
        data : pd.DataFrame
            Price data
            
        Returns
        -------
        pd.DataFrame
            Time series of integration scores
        """
        from .pca_analysis import PCAMarketIntegration
        
        # Use PCA-based integration analysis
        pca_analyzer = PCAMarketIntegration({
            'window_size': 52,  # Weekly windows
            'min_periods': 26
        })
        
        # Get rolling integration metrics
        integration_scores = pca_analyzer.rolling_pca_analysis(data)
        
        return integration_scores
    
    def _event_study_analysis(self) -> Dict[str, Any]:
        """Analyze integration changes around conflict events.
        
        Returns
        -------
        dict
            Event study results
        """
        with timer("event_study_analysis"):
            info("Performing event study analysis")
            
            if self.conflict_data is None or self.market_integration_scores is None:
                warning("Insufficient data for event study")
                return {}
            
            # Filter significant conflicts
            major_conflicts = self.conflict_data[
                self.conflict_data['fatalities'] >= self.conflict_threshold
            ].copy()
            
            info(f"Analyzing {len(major_conflicts)} major conflict events")
            
            event_results = []
            
            with progress("Analyzing conflict events", total=len(major_conflicts)) as update:
                for idx, event in major_conflicts.iterrows():
                    event_date = event['date']
                    
                    # Define windows
                    window_start = event_date - pd.Timedelta(days=self.window_before)
                    window_end = event_date + pd.Timedelta(days=self.window_after)
                    
                    # Get integration scores in windows
                    before_scores = self.market_integration_scores[
                        (self.market_integration_scores.index >= window_start) &
                        (self.market_integration_scores.index < event_date)
                    ]
                    
                    after_scores = self.market_integration_scores[
                        (self.market_integration_scores.index > event_date) &
                        (self.market_integration_scores.index <= window_end)
                    ]
                    
                    if len(before_scores) > 0 and len(after_scores) > 0:
                        # Compare integration levels
                        before_mean = before_scores['pc1_variance'].mean()
                        after_mean = after_scores['pc1_variance'].mean()
                        
                        # Test for significant change
                        t_stat, p_value = stats.ttest_ind(
                            before_scores['pc1_variance'],
                            after_scores['pc1_variance']
                        )
                        
                        event_results.append({
                            'event_date': event_date,
                            'governorate': event['governorate'],
                            'fatalities': event['fatalities'],
                            'integration_before': before_mean,
                            'integration_after': after_mean,
                            'integration_change': after_mean - before_mean,
                            'change_pct': ((after_mean - before_mean) / before_mean * 100) if before_mean > 0 else 0,
                            't_statistic': t_stat,
                            'p_value': p_value,
                            'significant': p_value < 0.05
                        })
                    
                    update(1)
            
            # Aggregate results
            event_df = pd.DataFrame(event_results)
            
            if len(event_df) > 0:
                summary = {
                    'n_events_analyzed': len(event_df),
                    'n_significant_changes': event_df['significant'].sum(),
                    'avg_integration_change': event_df['integration_change'].mean(),
                    'pct_events_reducing_integration': (event_df['integration_change'] < 0).mean() * 100,
                    'largest_impact': event_df.loc[event_df['change_pct'].abs().idxmax()].to_dict() if len(event_df) > 0 else None,
                    'event_details': event_df
                }
                
                log_metric("conflict_impact_rate", summary['pct_events_reducing_integration'])
                
            else:
                summary = {'n_events_analyzed': 0}
            
            return summary
    
    def _structural_break_analysis(self) -> Dict[str, Any]:
        """Test for structural breaks in integration aligned with conflicts.
        
        Returns
        -------
        dict
            Structural break test results
        """
        with timer("structural_break_analysis"):
            info("Testing for conflict-aligned structural breaks")
            
            if self.market_integration_scores is None:
                return {}
            
            # Use Chow test at major conflict dates
            integration_series = self.market_integration_scores['pc1_variance']
            
            # Identify potential break points (major conflicts)
            if self.conflict_data is not None:
                # Get dates of major conflicts
                major_events = self.conflict_data[
                    self.conflict_data['fatalities'] >= self.conflict_threshold * 5
                ]['date'].unique()
                
                break_results = []
                
                for break_date in major_events:
                    # Find index of break date in integration series
                    if break_date in integration_series.index:
                        break_idx = integration_series.index.get_loc(break_date)
                        
                        # Need sufficient data on both sides
                        if break_idx > 20 and break_idx < len(integration_series) - 20:
                            # Simplified Chow test
                            series1 = integration_series.iloc[:break_idx]
                            series2 = integration_series.iloc[break_idx:]
                            
                            # Test for mean difference
                            t_stat, p_value = stats.ttest_ind(series1, series2)
                            
                            # Test for variance difference
                            f_stat, f_pvalue = stats.levene(series1, series2)
                            
                            break_results.append({
                                'break_date': break_date,
                                'mean_before': series1.mean(),
                                'mean_after': series2.mean(),
                                'mean_change': series2.mean() - series1.mean(),
                                't_statistic': t_stat,
                                'p_value': p_value,
                                'variance_test_stat': f_stat,
                                'variance_p_value': f_pvalue,
                                'significant_break': p_value < 0.05 or f_pvalue < 0.05
                            })
                
                return {
                    'n_breaks_tested': len(break_results),
                    'n_significant_breaks': sum(r['significant_break'] for r in break_results),
                    'break_details': pd.DataFrame(break_results) if break_results else None
                }
            
            return {'message': 'No conflict data available for break analysis'}
    
    def _spatial_impact_analysis(self) -> Dict[str, Any]:
        """Analyze spatial patterns of conflict impact on integration.
        
        Returns
        -------
        dict
            Spatial analysis results
        """
        with timer("spatial_impact_analysis"):
            info("Analyzing spatial patterns of conflict impact")
            
            if self.conflict_data is None or self.data is None:
                return {}
            
            # Get markets affected by conflicts
            conflict_markets = self.conflict_data['governorate'].unique()
            all_markets = self.data['governorate'].unique()
            
            # Categorize markets
            directly_affected = set(conflict_markets)
            neighboring = set()  # Would need adjacency data
            unaffected = set(all_markets) - directly_affected - neighboring
            
            # Compare price volatility across categories
            results = {
                'directly_affected_markets': list(directly_affected),
                'n_directly_affected': len(directly_affected),
                'n_total_markets': len(all_markets),
                'pct_markets_affected': len(directly_affected) / len(all_markets) * 100
            }
            
            # Calculate price volatility by market category
            volatility_results = []
            
            for market in all_markets:
                market_data = self.data[self.data['governorate'] == market]
                
                if len(market_data) > 0:
                    # Group by commodity and calculate volatility
                    commodity_volatility = market_data.groupby('commodity')['usd_price'].agg([
                        'std', 'mean'
                    ])
                    commodity_volatility['cv'] = commodity_volatility['std'] / commodity_volatility['mean']
                    
                    volatility_results.append({
                        'market': market,
                        'category': 'affected' if market in directly_affected else 'unaffected',
                        'avg_volatility': commodity_volatility['cv'].mean(),
                        'max_volatility': commodity_volatility['cv'].max()
                    })
            
            volatility_df = pd.DataFrame(volatility_results)
            
            # Compare volatility between affected and unaffected
            if len(volatility_df) > 0:
                affected_vol = volatility_df[volatility_df['category'] == 'affected']['avg_volatility']
                unaffected_vol = volatility_df[volatility_df['category'] == 'unaffected']['avg_volatility']
                
                if len(affected_vol) > 0 and len(unaffected_vol) > 0:
                    t_stat, p_value = stats.ttest_ind(affected_vol, unaffected_vol)
                    
                    results.update({
                        'avg_volatility_affected': affected_vol.mean(),
                        'avg_volatility_unaffected': unaffected_vol.mean(),
                        'volatility_ratio': affected_vol.mean() / unaffected_vol.mean() if unaffected_vol.mean() > 0 else np.nan,
                        'volatility_test_statistic': t_stat,
                        'volatility_test_pvalue': p_value,
                        'higher_volatility_in_conflict': affected_vol.mean() > unaffected_vol.mean()
                    })
                    
                    log_metric("conflict_volatility_ratio", results['volatility_ratio'])
            
            return results
    
    def _granger_causality_tests(self) -> Dict[str, Any]:
        """Test Granger causality between conflicts and integration.
        
        Returns
        -------
        dict
            Granger causality test results
        """
        with timer("granger_causality_tests"):
            info("Testing Granger causality between conflicts and integration")
            
            # Aggregate conflict intensity over time
            conflict_intensity = self.conflict_data.groupby('date')['fatalities'].sum()
            conflict_intensity = conflict_intensity.reindex(
                self.market_integration_scores.index, 
                fill_value=0
            )
            
            # Align series
            aligned_data = pd.DataFrame({
                'integration': self.market_integration_scores['pc1_variance'],
                'conflict': conflict_intensity
            }).dropna()
            
            if len(aligned_data) < 50:
                warning("Insufficient data for Granger causality test")
                return {}
            
            # Test both directions
            results = {}
            
            # Test: Do conflicts Granger-cause integration changes?
            try:
                gc_conflict_to_integration = grangercausalitytests(
                    aligned_data[['integration', 'conflict']], 
                    maxlag=4,
                    verbose=False
                )
                
                # Extract p-values for different lags
                conflict_causes_integration = {
                    f'lag_{lag}': {
                        'f_statistic': gc_conflict_to_integration[lag][0]['ssr_ftest'][0],
                        'p_value': gc_conflict_to_integration[lag][0]['ssr_ftest'][1]
                    }
                    for lag in range(1, 5)
                }
                
                results['conflict_causes_integration'] = conflict_causes_integration
                
            except Exception as e:
                warning(f"Error in Granger test (conflict -> integration): {e}")
            
            # Test: Does integration Granger-cause conflicts?
            try:
                gc_integration_to_conflict = grangercausalitytests(
                    aligned_data[['conflict', 'integration']], 
                    maxlag=4,
                    verbose=False
                )
                
                integration_causes_conflict = {
                    f'lag_{lag}': {
                        'f_statistic': gc_integration_to_conflict[lag][0]['ssr_ftest'][0],
                        'p_value': gc_integration_to_conflict[lag][0]['ssr_ftest'][1]
                    }
                    for lag in range(1, 5)
                }
                
                results['integration_causes_conflict'] = integration_causes_conflict
                
            except Exception as e:
                warning(f"Error in Granger test (integration -> conflict): {e}")
            
            # Find significant relationships
            if results:
                significant_lags = []
                for direction in results:
                    for lag, stats in results[direction].items():
                        if stats['p_value'] < 0.05:
                            significant_lags.append(f"{direction} at {lag}")
                
                results['significant_relationships'] = significant_lags
                results['bidirectional_causality'] = (
                    len([s for s in significant_lags if 'conflict_causes' in s]) > 0 and
                    len([s for s in significant_lags if 'integration_causes' in s]) > 0
                )
            
            return results
    
    def _add_summary_statistics(self, results: ResultsContainer) -> None:
        """Add summary statistics to results.
        
        Parameters
        ----------
        results : ResultsContainer
            Results to update
        """
        # Basic metadata
        results.metadata['n_observations'] = len(self.data) if self.data is not None else 0
        
        if self.conflict_data is not None:
            results.metadata['n_conflict_events'] = len(self.conflict_data)
            results.metadata['total_fatalities'] = self.conflict_data['fatalities'].sum()
            results.metadata['conflict_date_range'] = (
                self.conflict_data['date'].min(),
                self.conflict_data['date'].max()
            )
        
        # Key findings as coefficients
        if 'event_study' in results.tier_specific:
            event_study = results.tier_specific['event_study']
            if 'avg_integration_change' in event_study:
                results.add_coefficient(
                    'avg_conflict_impact',
                    event_study['avg_integration_change'],
                    se=0.0  # Would need bootstrapping for SE
                )
        
        if 'spatial_impact' in results.tier_specific:
            spatial = results.tier_specific['spatial_impact']
            if 'volatility_ratio' in spatial:
                results.add_coefficient(
                    'conflict_volatility_ratio',
                    spatial['volatility_ratio'],
                    se=0.0
                )
        
        # Add warnings for significant findings
        if results.tier_specific.get('event_study', {}).get('pct_events_reducing_integration', 0) > 70:
            results.metadata['warnings'].append(
                "Strong evidence that conflicts reduce market integration"
            )
        
        if results.tier_specific.get('granger_causality', {}).get('bidirectional_causality'):
            results.metadata['warnings'].append(
                "Bidirectional causality detected between conflicts and integration"
            )
    
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Generate predictions (not applicable for validation model).
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            Input data
            
        Returns
        -------
        pd.Series
            Empty series (validation model doesn't predict)
        """
        warning("Conflict validator is a diagnostic model and does not generate predictions")
        return pd.Series(dtype=float)
    
    def get_conflict_impact_summary(self) -> pd.DataFrame:
        """Get summary of conflict impacts on integration.
        
        Returns
        -------
        pd.DataFrame
            Summary table of impacts
        """
        if not self.is_fitted or 'event_study' not in self.results.tier_specific:
            raise ValueError("Model must be fitted first")
        
        event_details = self.results.tier_specific['event_study'].get('event_details')
        
        if event_details is not None and not event_details.empty:
            summary = event_details.groupby('governorate').agg({
                'fatalities': 'sum',
                'integration_change': 'mean',
                'significant': 'sum'
            }).rename(columns={
                'fatalities': 'total_fatalities',
                'integration_change': 'avg_integration_impact',
                'significant': 'n_significant_events'
            })
            
            return summary
        
        return pd.DataFrame()  # Empty if no results