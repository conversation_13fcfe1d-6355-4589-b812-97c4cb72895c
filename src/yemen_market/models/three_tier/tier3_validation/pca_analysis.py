"""PCA-based analysis for market integration validation.

This module provides specialized PCA analysis tools for validating market
integration patterns, including rolling PCA, hierarchical PCA, and
integration strength measurement.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from scipy.spatial.distance import pdist, squareform
from scipy.cluster import hierarchy
from scipy.stats import pearsonr
import warnings

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler
from yemen_market.models.three_tier.core.results_container import ResultsContainer

# Set module context
bind(module=__name__)


class PCAMarketIntegration:
    """PCA-based tools for market integration analysis.
    
    This class provides various PCA-based methods to analyze and validate
    market integration patterns from different perspectives.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize PCA integration analyzer.
        
        Parameters
        ----------
        config : dict, optional
            Configuration parameters:
            - standardize: Whether to standardize data (default: True)
            - min_periods: Minimum periods for rolling PCA (default: 52)
            - window_size: Rolling window size (default: 104)
        """
        self.config = config or {}
        self.standardize = self.config.get('standardize', True)
        self.min_periods = self.config.get('min_periods', 52)
        self.window_size = self.config.get('window_size', 104)
        
        self.panel_handler = PanelDataHandler()
        self.results = {}
        
    def analyze_integration_strength(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze overall market integration strength using PCA.
        
        The first principal component's explanatory power indicates the degree
        of common movement across markets, serving as a measure of integration.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with market, commodity, date, and price columns
            
        Returns
        -------
        dict
            Integration metrics including PC1 variance, loadings, and scores
        """
        with timer("analyze_integration_strength"):
            info("Analyzing market integration strength via PCA")
            
            # Create wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            log_data_shape("wide_matrix_for_integration", wide_matrix)
            
            # Handle missing values
            wide_matrix = self._prepare_data(wide_matrix)
            
            # Standardize if requested
            if self.standardize:
                scaler = StandardScaler()
                data_scaled = scaler.fit_transform(wide_matrix)
            else:
                data_scaled = wide_matrix.values
                
            # Fit PCA
            pca = PCA()
            scores = pca.fit_transform(data_scaled)
            
            # Calculate integration metrics
            pc1_variance = pca.explained_variance_ratio_[0]
            cumvar_first3 = np.sum(pca.explained_variance_ratio_[:3])
            
            # Get loadings for first PC
            loadings_pc1 = pd.Series(
                pca.components_[0],
                index=wide_matrix.columns,
                name='PC1_Loading'
            )
            
            # Calculate effective number of factors (entropy-based)
            explained_var = pca.explained_variance_ratio_
            entropy = -np.sum(explained_var * np.log(explained_var + 1e-10))
            effective_factors = np.exp(entropy)
            
            # Integration interpretation
            integration_level = self._interpret_integration(pc1_variance)
            
            results = {
                'pc1_variance_explained': pc1_variance,
                'cumulative_variance_3pc': cumvar_first3,
                'effective_n_factors': effective_factors,
                'integration_level': integration_level,
                'pc1_loadings': loadings_pc1,
                'eigenvalues': pca.explained_variance_,
                'n_series': len(wide_matrix.columns),
                'n_periods': len(wide_matrix)
            }
            
            # Log key metrics
            log_metric("pc1_variance", pc1_variance)
            log_metric("integration_level", integration_level)
            log_metric("effective_factors", effective_factors)
            
            info(f"Integration analysis complete: {integration_level} "
                 f"(PC1 explains {pc1_variance:.1%} of variance)")
            
            return results
    
    def rolling_pca_analysis(self, data: pd.DataFrame) -> pd.DataFrame:
        """Perform rolling PCA to track integration dynamics over time.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
            
        Returns
        -------
        pd.DataFrame
            Time series of integration metrics
        """
        with timer("rolling_pca_analysis"):
            info(f"Starting rolling PCA with window={self.window_size}")
            
            # Create wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            wide_matrix = self._prepare_data(wide_matrix)
            
            # Initialize results storage
            rolling_results = []
            
            # Progress tracking
            n_windows = len(wide_matrix) - self.window_size + 1
            
            with progress("Rolling PCA windows", total=n_windows) as update:
                for i in range(n_windows):
                    # Extract window
                    window_data = wide_matrix.iloc[i:i + self.window_size]
                    
                    # Skip if too many missing values
                    if window_data.isna().sum().sum() > 0.3 * window_data.size:
                        update(1)
                        continue
                    
                    try:
                        # Standardize window
                        if self.standardize:
                            scaler = StandardScaler()
                            data_scaled = scaler.fit_transform(window_data.fillna(window_data.mean()))
                        else:
                            data_scaled = window_data.fillna(window_data.mean()).values
                        
                        # Fit PCA
                        pca = PCA(n_components=min(5, data_scaled.shape[1]))
                        pca.fit(data_scaled)
                        
                        # Store results
                        window_end = window_data.index[-1]
                        rolling_results.append({
                            'date': window_end,
                            'pc1_variance': pca.explained_variance_ratio_[0],
                            'pc2_variance': pca.explained_variance_ratio_[1] if len(pca.explained_variance_ratio_) > 1 else 0,
                            'cumvar_2pc': np.sum(pca.explained_variance_ratio_[:2]),
                            'effective_factors': self._calculate_effective_factors(pca.explained_variance_ratio_),
                            'max_loading_range': np.ptp(pca.components_[0]),  # Range of PC1 loadings
                            'loading_dispersion': np.std(pca.components_[0])
                        })
                        
                    except Exception as e:
                        warning(f"Error in window {i}: {str(e)}")
                    
                    update(1)
            
            # Convert to DataFrame
            results_df = pd.DataFrame(rolling_results)
            results_df.set_index('date', inplace=True)
            
            # Calculate integration trend
            if len(results_df) > 0:
                results_df['integration_trend'] = results_df['pc1_variance'].rolling(
                    window=12, min_periods=6
                ).mean()
                
                # Detect integration regime changes
                results_df['integration_change'] = results_df['pc1_variance'].diff()
                threshold = results_df['integration_change'].std() * 2
                results_df['regime_change'] = (
                    results_df['integration_change'].abs() > threshold
                )
            
            log_data_shape("rolling_pca_results", results_df)
            
            return results_df
    
    def hierarchical_pca(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform hierarchical PCA to identify market clusters.
        
        First applies PCA, then clusters markets based on their loadings
        to identify groups with similar price dynamics.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
            
        Returns
        -------
        dict
            Clustering results and market groupings
        """
        with timer("hierarchical_pca"):
            info("Performing hierarchical PCA for market clustering")
            
            # Create wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            wide_matrix = self._prepare_data(wide_matrix)
            
            # Standardize
            scaler = StandardScaler()
            data_scaled = scaler.fit_transform(wide_matrix)
            
            # Initial PCA
            n_components = min(10, data_scaled.shape[1] // 2)
            pca = PCA(n_components=n_components)
            scores = pca.fit_transform(data_scaled)
            
            # Get loadings matrix
            loadings = pca.components_.T
            
            # Calculate distance matrix based on loadings
            loading_distances = pdist(loadings, metric='euclidean')
            distance_matrix = squareform(loading_distances)
            
            # Perform hierarchical clustering
            linkage_matrix = hierarchy.linkage(loading_distances, method='ward')
            
            # Find optimal number of clusters using elbow method
            last = linkage_matrix[-10:, 2]
            acceleration = np.diff(last, 2)
            n_clusters = acceleration.argmax() + 2
            
            # Get cluster assignments
            clusters = hierarchy.fcluster(linkage_matrix, n_clusters, criterion='maxclust')
            
            # Create market groups
            market_groups = {}
            for i, entity in enumerate(wide_matrix.columns):
                cluster_id = clusters[i]
                if cluster_id not in market_groups:
                    market_groups[cluster_id] = []
                market_groups[cluster_id].append(entity)
            
            # Analyze cluster characteristics
            cluster_stats = self._analyze_clusters(wide_matrix, clusters)
            
            results = {
                'n_clusters': n_clusters,
                'market_groups': market_groups,
                'cluster_sizes': {k: len(v) for k, v in market_groups.items()},
                'linkage_matrix': linkage_matrix,
                'distance_matrix': pd.DataFrame(
                    distance_matrix,
                    index=wide_matrix.columns,
                    columns=wide_matrix.columns
                ),
                'cluster_stats': cluster_stats,
                'pca_loadings': pd.DataFrame(
                    loadings,
                    index=wide_matrix.columns,
                    columns=[f'PC{i+1}' for i in range(n_components)]
                )
            }
            
            info(f"Identified {n_clusters} market clusters")
            
            return results
    
    def commodity_specific_pca(self, data: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Perform PCA separately for each commodity.
        
        This reveals commodity-specific integration patterns that might
        be masked in the aggregate analysis.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
            
        Returns
        -------
        dict
            PCA results for each commodity
        """
        with timer("commodity_specific_pca"):
            info("Performing commodity-specific PCA analysis")
            
            # Get unique commodities
            commodities = data['commodity'].unique()
            commodity_results = {}
            
            with progress("Analyzing commodities", total=len(commodities)) as update:
                for commodity in commodities:
                    # Extract commodity data
                    commodity_data = self.panel_handler.extract_commodity_panel(data, commodity)
                    
                    if len(commodity_data) == 0:
                        update(1)
                        continue
                    
                    # Create wide matrix for this commodity
                    pivot_data = commodity_data.pivot(
                        index='date',
                        columns='governorate',
                        values='usd_price'
                    )
                    
                    # Skip if insufficient data
                    if pivot_data.shape[1] < 3 or pivot_data.shape[0] < self.min_periods:
                        warning(f"Insufficient data for {commodity}")
                        update(1)
                        continue
                    
                    # Prepare data
                    pivot_data = self._prepare_data(pivot_data)
                    
                    # Standardize
                    scaler = StandardScaler()
                    data_scaled = scaler.fit_transform(pivot_data)
                    
                    # PCA
                    pca = PCA()
                    scores = pca.fit_transform(data_scaled)
                    
                    # Store results
                    commodity_results[commodity] = {
                        'pc1_variance': pca.explained_variance_ratio_[0],
                        'cumvar_3pc': np.sum(pca.explained_variance_ratio_[:min(3, len(pca.explained_variance_ratio_))]),
                        'n_markets': pivot_data.shape[1],
                        'n_periods': pivot_data.shape[0],
                        'integration_level': self._interpret_integration(pca.explained_variance_ratio_[0]),
                        'market_loadings': pd.Series(
                            pca.components_[0],
                            index=pivot_data.columns,
                            name=f'{commodity}_PC1_loading'
                        ),
                        'price_volatility': pivot_data.std().mean(),
                        'missing_data_pct': (pivot_data.isna().sum().sum() / pivot_data.size) * 100
                    }
                    
                    update(1)
            
            # Compare across commodities
            if commodity_results:
                comparison_df = pd.DataFrame({
                    com: {
                        'PC1 Variance': res['pc1_variance'],
                        'Integration': res['integration_level'],
                        'Markets': res['n_markets'],
                        'Volatility': res['price_volatility']
                    }
                    for com, res in commodity_results.items()
                }).T
                
                info("Commodity integration comparison:")
                info(f"\n{comparison_df}")
            
            return commodity_results
    
    def spatial_pca_analysis(self, data: pd.DataFrame, 
                           distance_matrix: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Analyze spatial patterns in PCA loadings.
        
        Tests whether geographically closer markets have similar loadings,
        indicating spatial market integration patterns.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
        distance_matrix : pd.DataFrame, optional
            Matrix of distances between markets
            
        Returns
        -------
        dict
            Spatial analysis results
        """
        with timer("spatial_pca_analysis"):
            info("Analyzing spatial patterns in market integration")
            
            # Create wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            wide_matrix = self._prepare_data(wide_matrix)
            
            # Run PCA
            scaler = StandardScaler()
            data_scaled = scaler.fit_transform(wide_matrix)
            pca = PCA(n_components=5)
            pca.fit(data_scaled)
            
            # Extract market names from entity labels
            markets = []
            for entity in wide_matrix.columns:
                market = entity.split('_')[0]  # Assuming format "market_commodity"
                markets.append(market)
            
            # Group loadings by market (average across commodities)
            market_loadings = {}
            for i, entity in enumerate(wide_matrix.columns):
                market = entity.split('_')[0]
                if market not in market_loadings:
                    market_loadings[market] = []
                market_loadings[market].append(pca.components_[0][i])
            
            # Average loadings per market
            avg_loadings = {
                market: np.mean(loadings) 
                for market, loadings in market_loadings.items()
            }
            
            results = {
                'market_loadings': avg_loadings,
                'loading_variance': np.var(list(avg_loadings.values())),
                'n_markets': len(avg_loadings)
            }
            
            # If distance matrix provided, test spatial correlation
            if distance_matrix is not None:
                spatial_corr = self._test_spatial_correlation(
                    avg_loadings, distance_matrix
                )
                results['spatial_correlation'] = spatial_corr
                
                if spatial_corr['correlation'] < -0.3:
                    info("Significant spatial pattern detected: nearby markets have similar loadings")
                elif spatial_corr['correlation'] > 0.3:
                    info("Inverse spatial pattern: distant markets more integrated")
                else:
                    info("No clear spatial pattern in market integration")
            
            return results
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for PCA analysis.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input data
            
        Returns
        -------
        pd.DataFrame
            Cleaned data
        """
        # Forward fill up to 2 periods
        data = data.ffill(limit=2).bfill(limit=2)
        
        # Interpolate remaining gaps
        data = data.interpolate(method='linear', limit=5)
        
        # Drop columns with too many missing values
        missing_threshold = 0.3
        cols_to_keep = data.columns[data.isna().mean() < missing_threshold]
        data = data[cols_to_keep]
        
        # Fill any remaining NaNs with column means
        data = data.fillna(data.mean())
        
        return data
    
    def _interpret_integration(self, pc1_variance: float) -> str:
        """Interpret integration level based on PC1 variance.
        
        Parameters
        ----------
        pc1_variance : float
            Variance explained by first principal component
            
        Returns
        -------
        str
            Integration level interpretation
        """
        if pc1_variance >= 0.7:
            return "Very High"
        elif pc1_variance >= 0.5:
            return "High"
        elif pc1_variance >= 0.3:
            return "Moderate"
        elif pc1_variance >= 0.2:
            return "Low"
        else:
            return "Very Low"
    
    def _calculate_effective_factors(self, explained_variance: np.ndarray) -> float:
        """Calculate effective number of factors using entropy.
        
        Parameters
        ----------
        explained_variance : np.ndarray
            Array of explained variance ratios
            
        Returns
        -------
        float
            Effective number of factors
        """
        # Avoid log(0)
        explained_variance = explained_variance[explained_variance > 1e-10]
        
        # Shannon entropy
        entropy = -np.sum(explained_variance * np.log(explained_variance))
        
        # Effective number
        return np.exp(entropy)
    
    def _analyze_clusters(self, data: pd.DataFrame, clusters: np.ndarray) -> pd.DataFrame:
        """Analyze characteristics of identified clusters.
        
        Parameters
        ----------
        data : pd.DataFrame
            Wide format price data
        clusters : np.ndarray
            Cluster assignments
            
        Returns
        -------
        pd.DataFrame
            Cluster statistics
        """
        cluster_stats = []
        
        for cluster_id in np.unique(clusters):
            # Get series in this cluster
            cluster_mask = clusters == cluster_id
            cluster_data = data.iloc[:, cluster_mask]
            
            # Calculate statistics
            stats = {
                'cluster_id': cluster_id,
                'n_series': cluster_data.shape[1],
                'avg_correlation': cluster_data.corr().values[np.triu_indices_from(
                    cluster_data.corr().values, k=1)].mean(),
                'price_volatility': cluster_data.std().mean(),
                'price_level': cluster_data.mean().mean()
            }
            
            cluster_stats.append(stats)
        
        return pd.DataFrame(cluster_stats)
    
    def _test_spatial_correlation(self, loadings: Dict[str, float],
                                 distance_matrix: pd.DataFrame) -> Dict[str, float]:
        """Test correlation between spatial distance and loading similarity.
        
        Parameters
        ----------
        loadings : dict
            Market loadings
        distance_matrix : pd.DataFrame
            Distance matrix between markets
            
        Returns
        -------
        dict
            Correlation results
        """
        # Ensure markets align
        common_markets = list(set(loadings.keys()) & set(distance_matrix.index))
        
        if len(common_markets) < 3:
            warning("Insufficient markets for spatial correlation test")
            return {'correlation': np.nan, 'p_value': np.nan}
        
        # Create loading difference matrix
        n_markets = len(common_markets)
        loading_diffs = np.zeros((n_markets, n_markets))
        spatial_dists = np.zeros((n_markets, n_markets))
        
        for i, market1 in enumerate(common_markets):
            for j, market2 in enumerate(common_markets):
                if i < j:
                    loading_diffs[i, j] = abs(loadings[market1] - loadings[market2])
                    spatial_dists[i, j] = distance_matrix.loc[market1, market2]
        
        # Extract upper triangle (excluding diagonal)
        mask = np.triu(np.ones_like(loading_diffs, dtype=bool), k=1)
        loading_diffs_vec = loading_diffs[mask]
        spatial_dists_vec = spatial_dists[mask]
        
        # Calculate correlation
        corr, p_value = pearsonr(spatial_dists_vec, loading_diffs_vec)
        
        return {
            'correlation': corr,
            'p_value': p_value,
            'n_pairs': len(loading_diffs_vec)
        }
    
    def generate_integration_report(self, data: pd.DataFrame) -> ResultsContainer:
        """Generate comprehensive PCA-based integration report.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
            
        Returns
        -------
        ResultsContainer
            Comprehensive results
        """
        with timer("generate_integration_report"):
            info("Generating comprehensive PCA integration report")
            
            results = ResultsContainer(
                tier="tier3_validation",
                model_type="pca_integration_analysis"
            )
            
            # Overall integration strength
            integration = self.analyze_integration_strength(data)
            results.add_tier_specific(
                overall_integration=integration
            )
            
            # Add key metric as coefficient
            results.add_coefficient(
                'Overall_Integration_PC1',
                integration['pc1_variance_explained'],
                se=0.0  # No SE for descriptive statistic
            )
            
            # Commodity-specific analysis
            commodity_results = self.commodity_specific_pca(data)
            results.add_tier_specific(
                commodity_integration=commodity_results
            )
            
            # Market clustering
            clustering = self.hierarchical_pca(data)
            results.add_tier_specific(
                market_clusters=clustering['market_groups'],
                n_clusters=clustering['n_clusters']
            )
            
            # Metadata
            results.metadata['n_observations'] = len(data)
            results.metadata['n_markets'] = data['governorate'].nunique()
            results.metadata['n_commodities'] = data['commodity'].nunique()
            results.metadata['estimation_time'] = timer.get_elapsed("generate_integration_report")
            
            # Summary interpretation
            if integration['pc1_variance_explained'] > 0.5:
                results.metadata['warnings'].append(
                    f"High market integration detected: PC1 explains "
                    f"{integration['pc1_variance_explained']:.1%} of price variation"
                )
            
            return results