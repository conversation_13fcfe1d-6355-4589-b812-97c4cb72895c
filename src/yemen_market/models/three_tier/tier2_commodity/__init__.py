"""Tier 2: Commodity-specific threshold VECM analysis.

This module implements the second tier of analysis, extracting 2D panels
for each commodity and estimating threshold vector error correction models.
"""

from yemen_market.utils.logging import bind
from .commodity_extractor import CommodityExtractor, CommodityExtractorConfig
from .commodity_specific_model import CommoditySpecificModel, CommodityModelConfig
from .threshold_vecm import ThresholdVECM, ThresholdVECMConfig
from .cointegration_tests import CointegrationTestSuite, CointegrationTestConfig

# Set module context
bind(module=__name__)

__all__ = [
    'CommodityExtractor',
    'CommodityExtractorConfig',
    'CommoditySpecificModel', 
    'CommodityModelConfig',
    'ThresholdVECM',
    'ThresholdVECMConfig',
    'CointegrationTestSuite',
    'CointegrationTestConfig'
]