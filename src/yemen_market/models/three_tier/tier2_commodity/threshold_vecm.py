"""Threshold Vector Error Correction Model for commodity-specific analysis.

This module implements a threshold VECM for Tier 2 analysis, allowing for
regime-dependent adjustment speeds based on conflict intensity or other
threshold variables.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import warnings
from statsmodels.tsa.vector_ar.vecm import VECM, select_coint_rank
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.tsa.vector_ar.vecm import coint_johansen
import statsmodels.api as sm
from scipy import stats

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_metric, log_data_shape, bind
)
# from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel
from yemen_market.models.three_tier.core.results_container import ResultsContainer


# Set module context
bind(module=__name__)


@dataclass
class ThresholdVECMConfig:
    """Configuration for threshold VECM models.
    
    Attributes:
        threshold_variable: Variable to use for regime switching
        threshold_value: Fixed threshold value (if not estimating)
        estimate_threshold: Whether to estimate optimal threshold
        n_lags: Number of lags in VECM specification
        deterministic: Deterministic trend specification ('nc', 'c', 'ct', 'ctt')
        trim_pct: Percentage to trim when searching for threshold
        min_obs_per_regime: Minimum observations required per regime
        bootstrap_reps: Number of bootstrap replications for testing
    """
    threshold_variable: str = 'conflict_events'
    threshold_value: Optional[float] = None
    estimate_threshold: bool = True
    n_lags: int = 2
    deterministic: str = 'c'  # constant only
    trim_pct: float = 0.15
    min_obs_per_regime: int = 30
    bootstrap_reps: int = 1000
    
    def __post_init__(self):
        valid_det = ['nc', 'c', 'ct', 'ctt']
        if self.deterministic not in valid_det:
            raise ValueError(f"deterministic must be one of {valid_det}")


class ThresholdVECM:
    """Threshold VECM for commodity-specific market integration analysis.
    
    This model estimates regime-dependent adjustment speeds to the long-run
    equilibrium, allowing for different market integration dynamics under
    different conflict intensities.
    """
    
    def __init__(self,
                 commodity: str,
                 config: Optional[ThresholdVECMConfig] = None,
                 panel_handler: Optional[Any] = None):
        """Initialize threshold VECM model.
        
        Args:
            commodity: Name of the commodity to model
            config: Model configuration. If None, uses defaults
            panel_handler: Panel data handler (not used for VECM)
        """
        self.model_name = f"ThresholdVECM_{commodity}"
        self.tier = 2
        self.panel_handler = panel_handler
        
        self.commodity = commodity
        self.config = config or ThresholdVECMConfig()
        
        # Model components
        self.coint_rank = None
        self.threshold = None
        self.low_regime_model = None
        self.high_regime_model = None
        self.regime_assignment = None
        
        info(f"Initialized ThresholdVECM for {commodity} with "
             f"threshold_var={self.config.threshold_variable}")
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """Validate input data for threshold VECM.
        
        Args:
            df: Input DataFrame to validate
            
        Returns:
            True if valid, raises ValueError otherwise
        """
        # Check required columns
        required = ['market', 'date', 'price', 'commodity']
        missing = [col for col in required if col not in df.columns]
        if missing:
            raise ValueError(f"Missing required columns: {missing}")
        
        # Check threshold variable if needed
        if self.config.threshold_variable not in df.columns:
            warning(f"Threshold variable '{self.config.threshold_variable}' not found in data")
        
        # Check minimum markets for VECM
        if 'market' in df.columns:
            n_markets = df['market'].nunique()
            if n_markets < 2:
                raise ValueError(f"VECM requires at least 2 markets, found {n_markets}")
        
        return True
    
    def prepare_data(self, df: pd.DataFrame,
                    markets: Optional[List[str]] = None) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare commodity data for VECM analysis.
        
        Args:
            df: Input DataFrame with price data
            markets: List of markets to include. If None, uses all
            
        Returns:
            Tuple of (wide_price_df, threshold_series)
        """
        with timer(f"prepare_vecm_data_{self.commodity}"):
            # Filter for commodity
            if 'commodity' in df.columns:
                df = df[df['commodity'] == self.commodity].copy()
            
            # Select markets
            if markets is None:
                markets = df['market'].unique()
                info(f"Using all {len(markets)} markets for {self.commodity}")
            else:
                df = df[df['market'].isin(markets)]
                info(f"Using {len(markets)} selected markets for {self.commodity}")
            
            # Ensure date is datetime
            df['date'] = pd.to_datetime(df['date'])
            
            # Pivot to wide format
            price_wide = df.pivot(
                index='date',
                columns='market', 
                values='price'
            )
            
            # Handle missing values
            missing_before = price_wide.isna().sum().sum()
            price_wide = price_wide.ffill().bfill()
            missing_after = price_wide.isna().sum().sum()
            
            if missing_after > 0:
                warning(f"Still {missing_after} missing values after forward/backward fill")
            else:
                info(f"Filled {missing_before} missing values")
            
            # Get threshold variable
            if self.config.threshold_variable in df.columns:
                # Average across markets for each date
                threshold_series = df.groupby('date')[self.config.threshold_variable].mean()
                threshold_series = threshold_series.reindex(price_wide.index).fillna(0)
            else:
                warning(f"Threshold variable '{self.config.threshold_variable}' not found, using zeros")
                threshold_series = pd.Series(0, index=price_wide.index)
            
            log_data_shape(f"{self.commodity}_price_wide", price_wide)
            
            return price_wide, threshold_series
    
    def test_cointegration(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """Test for cointegration among markets.
        
        Args:
            price_data: Wide-format price data (dates × markets)
            
        Returns:
            Dictionary with cointegration test results
        """
        with timer(f"cointegration_test_{self.commodity}"):
            info("Testing for cointegration among markets")
            
            # Take logs
            log_prices = np.log(price_data)
            
            # Test stationarity of individual series
            stationarity_results = {}
            for market in log_prices.columns:
                adf_stat, adf_pval, _, _, _ = adfuller(log_prices[market].dropna())
                kpss_stat, kpss_pval, _, _ = kpss(log_prices[market].dropna(), regression='c')
                
                stationarity_results[market] = {
                    'adf_stat': adf_stat,
                    'adf_pval': adf_pval,
                    'kpss_stat': kpss_stat,
                    'kpss_pval': kpss_pval,
                    'is_stationary': adf_pval < 0.05 and kpss_pval > 0.05
                }
            
            # Count non-stationary series
            n_nonstationary = sum(not res['is_stationary'] 
                                 for res in stationarity_results.values())
            info(f"Found {n_nonstationary}/{len(stationarity_results)} non-stationary series")
            
            # Johansen cointegration test
            try:
                # Select cointegration rank
                rank_test = select_coint_rank(
                    log_prices.dropna(),
                    det_order=0 if self.config.deterministic == 'nc' else 1,
                    k_ar_diff=self.config.n_lags - 1,
                    method='trace',
                    signif=0.05
                )
                selected_rank = rank_test.rank
                
                # Get full test results
                johansen_result = coint_johansen(
                    log_prices.dropna().values,
                    det_order=0 if self.config.deterministic == 'nc' else 1,
                    k_ar_diff=self.config.n_lags - 1
                )
                
                coint_results = {
                    'selected_rank': selected_rank,
                    'trace_stats': johansen_result.lr1,
                    'trace_crit_vals': johansen_result.cvt,
                    'max_eig_stats': johansen_result.lr2,
                    'max_eig_crit_vals': johansen_result.cvm,
                    'eigenvectors': johansen_result.evec,
                    'eigenvalues': johansen_result.eig,
                    'stationarity_tests': stationarity_results
                }
                
                log_metric(f"{self.commodity}_coint_rank", selected_rank)
                info(f"Selected cointegration rank: {selected_rank}")
                
            except Exception as e:
                error(f"Cointegration test failed: {str(e)}")
                coint_results = {
                    'selected_rank': 0,
                    'error': str(e),
                    'stationarity_tests': stationarity_results
                }
            
            return coint_results
    
    def estimate_threshold(self, price_data: pd.DataFrame,
                          threshold_series: pd.Series,
                          coint_rank: int) -> float:
        """Estimate optimal threshold value using grid search.
        
        Args:
            price_data: Wide-format price data
            threshold_series: Series of threshold variable values
            coint_rank: Number of cointegrating relationships
            
        Returns:
            Optimal threshold value
        """
        with timer(f"estimate_threshold_{self.commodity}"):
            info("Estimating optimal threshold value")
            
            # Get unique threshold values
            thr_values = threshold_series.values
            thr_sorted = np.sort(np.unique(thr_values))
            
            # Determine search grid with trimming
            n_obs = len(thr_values)
            trim_n = int(n_obs * self.config.trim_pct)
            grid_points = thr_sorted[trim_n:-trim_n]
            
            if len(grid_points) < 5:
                warning("Insufficient threshold values for search, using median")
                return float(np.median(thr_values))
            
            # Search for optimal threshold
            ssr_values = []
            valid_thresholds = []
            
            with progress(f"Threshold search", total=len(grid_points)) as update:
                for thr in grid_points:
                    # Check minimum observations per regime
                    n_low = np.sum(thr_values <= thr)
                    n_high = np.sum(thr_values > thr)
                    
                    if (n_low >= self.config.min_obs_per_regime and 
                        n_high >= self.config.min_obs_per_regime):
                        
                        # Calculate SSR for this threshold
                        ssr = self._calculate_threshold_ssr(
                            price_data, threshold_series, thr, coint_rank
                        )
                        ssr_values.append(ssr)
                        valid_thresholds.append(thr)
                    
                    update(1)
            
            if not valid_thresholds:
                warning("No valid thresholds found, using median")
                return float(np.median(thr_values))
            
            # Find minimum SSR
            min_idx = np.argmin(ssr_values)
            optimal_threshold = float(valid_thresholds[min_idx])
            
            log_metric(f"{self.commodity}_optimal_threshold", optimal_threshold)
            info(f"Optimal threshold: {optimal_threshold:.2f}")
            
            return optimal_threshold
    
    def _calculate_threshold_ssr(self, price_data: pd.DataFrame,
                                threshold_series: pd.Series,
                                threshold: float,
                                coint_rank: int) -> float:
        """Calculate sum of squared residuals for a given threshold.
        
        Args:
            price_data: Wide-format price data
            threshold_series: Threshold variable series
            threshold: Threshold value to test
            coint_rank: Number of cointegrating relationships
            
        Returns:
            Sum of squared residuals
        """
        try:
            # Split data by regime
            low_regime = threshold_series <= threshold
            high_regime = ~low_regime
            
            # Estimate VECM for each regime
            log_prices = np.log(price_data)
            
            ssr_total = 0.0
            
            for regime_mask, regime_name in [(low_regime, 'low'), (high_regime, 'high')]:
                regime_data = log_prices[regime_mask].dropna()
                
                if len(regime_data) < self.config.min_obs_per_regime:
                    return np.inf
                
                try:
                    # Fit VECM for this regime
                    if coint_rank > 0:
                        vecm = VECM(
                            regime_data,
                            k_ar_diff=self.config.n_lags - 1,
                            coint_rank=coint_rank,
                            deterministic=self.config.deterministic
                        )
                        vecm_fit = vecm.fit()
                        residuals = vecm_fit.resid
                    else:
                        # VAR in differences if no cointegration
                        diff_data = regime_data.diff().dropna()
                        var_model = sm.OLS(
                            diff_data.values[1:],
                            sm.add_constant(diff_data.values[:-1])
                        )
                        var_fit = var_model.fit()
                        residuals = var_fit.resid
                    
                    ssr_total += np.sum(residuals**2)
                    
                except Exception:
                    return np.inf
            
            return ssr_total
            
        except Exception:
            return np.inf
    
    def fit(self, df: pd.DataFrame,
            markets: Optional[List[str]] = None) -> ResultsContainer:
        """Fit the threshold VECM model.
        
        Args:
            df: Input DataFrame with price and threshold data
            markets: List of markets to include
            
        Returns:
            ResultsContainer with model results
        """
        with timer(f"fit_threshold_vecm_{self.commodity}"):
            # Prepare data
            price_data, threshold_series = self.prepare_data(df, markets)
            
            # Test for cointegration
            coint_results = self.test_cointegration(price_data)
            self.coint_rank = coint_results['selected_rank']
            
            if self.coint_rank == 0:
                warning("No cointegration found, will estimate VAR in differences")
            
            # Determine threshold
            if self.config.estimate_threshold and self.config.threshold_value is None:
                self.threshold = self.estimate_threshold(
                    price_data, threshold_series, self.coint_rank
                )
            else:
                self.threshold = self.config.threshold_value or 50.0
                info(f"Using fixed threshold: {self.threshold}")
            
            # Split by regime
            low_regime = threshold_series <= self.threshold
            high_regime = ~low_regime
            
            n_low = np.sum(low_regime)
            n_high = np.sum(high_regime)
            
            info(f"Regime split: {n_low} low, {n_high} high observations")
            
            # Estimate regime-specific models
            regime_results = {}
            log_prices = np.log(price_data)
            
            for regime_mask, regime_name in [(low_regime, 'low'), (high_regime, 'high')]:
                regime_data = log_prices[regime_mask].dropna()
                
                if len(regime_data) < self.config.min_obs_per_regime:
                    warning(f"Insufficient data for {regime_name} regime")
                    regime_results[regime_name] = None
                    continue
                
                try:
                    if self.coint_rank > 0:
                        # Estimate VECM
                        vecm = VECM(
                            regime_data,
                            k_ar_diff=self.config.n_lags - 1,
                            coint_rank=self.coint_rank,
                            deterministic=self.config.deterministic
                        )
                        vecm_fit = vecm.fit()
                        
                        regime_results[regime_name] = {
                            'model': vecm_fit,
                            'alpha': vecm_fit.alpha,  # Adjustment speeds
                            'beta': vecm_fit.beta,    # Cointegrating vectors
                            'gamma': vecm_fit.gamma if hasattr(vecm_fit, 'gamma') else None,
                            'sigma': vecm_fit.sigma_u,
                            'llf': vecm_fit.llf,
                            'aic': vecm_fit.aic,
                            'bic': vecm_fit.bic,
                            'resid': vecm_fit.resid
                        }
                    else:
                        # Estimate VAR in differences
                        diff_data = regime_data.diff().dropna()
                        # Simple VAR(1) in differences
                        y = diff_data.values[1:]
                        X = sm.add_constant(diff_data.values[:-1])
                        
                        var_model = sm.OLS(y, X)
                        var_fit = var_model.fit()
                        
                        regime_results[regime_name] = {
                            'model': var_fit,
                            'params': var_fit.params,
                            'resid': var_fit.resid,
                            'rsquared': var_fit.rsquared,
                            'llf': var_fit.llf,
                            'aic': var_fit.aic,
                            'bic': var_fit.bic
                        }
                    
                    info(f"Successfully estimated {regime_name} regime model")
                    
                except Exception as e:
                    error(f"Failed to estimate {regime_name} regime: {str(e)}")
                    regime_results[regime_name] = None
            
            # Store fitted models
            self.low_regime_model = regime_results.get('low')
            self.high_regime_model = regime_results.get('high')
            self.regime_assignment = low_regime.astype(int)  # 0 for low, 1 for high
            
            # Create results container
            results = self._create_results(
                regime_results, coint_results, price_data, threshold_series
            )
            
            self.results = results
            info(f"Threshold VECM estimation complete for {self.commodity}")
            
            return results
    
    def _create_results(self, regime_results: Dict[str, Any],
                       coint_results: Dict[str, Any],
                       price_data: pd.DataFrame,
                       threshold_series: pd.Series) -> ResultsContainer:
        """Create standardized results container.
        
        Args:
            regime_results: Results from regime-specific models
            coint_results: Cointegration test results
            price_data: Wide-format price data
            threshold_series: Threshold variable series
            
        Returns:
            ResultsContainer with all results
        """
        # Extract key results
        coefficients = {}
        model_stats = {
            'threshold': self.threshold,
            'coint_rank': self.coint_rank,
            'n_markets': len(price_data.columns),
            'n_obs': len(price_data),
            'n_obs_low': np.sum(threshold_series <= self.threshold),
            'n_obs_high': np.sum(threshold_series > self.threshold)
        }
        
        # Add regime-specific results
        for regime_name, regime_res in regime_results.items():
            if regime_res is not None:
                if 'alpha' in regime_res:
                    # VECM results
                    coefficients[f'{regime_name}_alpha'] = regime_res['alpha'].tolist()
                    coefficients[f'{regime_name}_beta'] = regime_res['beta'].tolist()
                    model_stats[f'{regime_name}_llf'] = regime_res['llf']
                    model_stats[f'{regime_name}_aic'] = regime_res['aic']
                else:
                    # VAR results
                    coefficients[f'{regime_name}_params'] = regime_res['params'].tolist()
                    model_stats[f'{regime_name}_rsquared'] = regime_res['rsquared']
        
        # Diagnostics
        diagnostics = {
            'cointegration_tests': coint_results,
            'threshold_variable': self.config.threshold_variable,
            'threshold_estimated': self.config.estimate_threshold
        }
        
        # Metadata
        metadata = {
            'commodity': self.commodity,
            'markets': list(price_data.columns),
            'date_range': [
                str(price_data.index.min()),
                str(price_data.index.max())
            ],
            'config': self.config.__dict__
        }
        
        return ResultsContainer(
            tier=2,
            model_type="threshold_vecm",
            coefficients=coefficients,
            model_stats=model_stats,
            fixed_effects=None,
            predictions=None,  # Not implemented for VECM
            diagnostics=diagnostics,
            metadata=metadata,
            comparison_metrics=None
        )
    
    def forecast(self, steps: int = 12) -> pd.DataFrame:
        """Generate forecasts from the threshold VECM.
        
        Args:
            steps: Number of periods to forecast
            
        Returns:
            DataFrame with forecasts
        """
        raise NotImplementedError("Forecasting not yet implemented for threshold VECM")
    
    def impulse_response(self, periods: int = 20) -> Dict[str, np.ndarray]:
        """Calculate regime-dependent impulse responses.
        
        Args:
            periods: Number of periods for impulse response
            
        Returns:
            Dictionary with impulse responses for each regime
        """
        raise NotImplementedError("Impulse responses not yet implemented")