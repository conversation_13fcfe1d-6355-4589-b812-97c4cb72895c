"""Commodity data extraction and validation for Tier 2 analysis.

This module provides functionality to extract and validate commodity-specific
panels from the 3D panel data structure.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Set, Any
from dataclasses import dataclass
import logging

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_metric, log_data_shape, bind
)

# Set module context
bind(module=__name__)


@dataclass
class CommodityExtractorConfig:
    """Configuration for commodity data extraction.
    
    Attributes:
        min_markets: Minimum number of markets required for a commodity to be included
        min_periods: Minimum number of time periods required for a commodity
        min_observations: Minimum total observations required for a commodity
        max_missing_pct: Maximum allowed percentage of missing values
        required_columns: List of columns that must be present in the input data
    """
    min_markets: int = 3
    min_periods: int = 50
    min_observations: int = 100
    max_missing_pct: float = 0.5
    required_columns: List[str] = None
    
    def __post_init__(self):
        if self.required_columns is None:
            self.required_columns = ['market', 'commodity', 'date', 'price']


class CommodityExtractor:
    """Extracts and validates commodity-specific panels from 3D panel data.
    
    This class handles the extraction of 2D panels for each commodity from the
    3D panel structure (market × commodity × time). It performs validation to
    ensure each commodity has sufficient data for analysis.
    """
    
    def __init__(self, config: Optional[CommodityExtractorConfig] = None):
        """Initialize the commodity extractor.
        
        Args:
            config: Configuration parameters. If None, uses defaults.
        """
        self.config = config or CommodityExtractorConfig()
        self.logger = logging.getLogger(__name__)
        
        # Storage for extracted commodities
        self.extracted_commodities: Dict[str, pd.DataFrame] = {}
        self.coverage_stats: Dict[str, Dict[str, Any]] = {}
        
        info("Initialized CommodityExtractor with config:"
             f" min_markets={self.config.min_markets}, min_periods={self.config.min_periods}, "
             f"min_obs={self.config.min_observations}, max_missing={self.config.max_missing_pct}")
    
    def extract_all_commodities(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Extract 2D panels for all valid commodities.
        
        Args:
            df: Input DataFrame with 3D panel data (market × commodity × time)
            
        Returns:
            Dictionary mapping commodity names to their respective 2D panels
            
        Raises:
            ValueError: If required columns are missing from input data
        """
        with timer("extract_all_commodities"):
            # Validate input data
            self._validate_input_data(df)
            
            # Reset index to ensure we have columns for market, commodity, date
            df = df.reset_index()
            
            # Get unique commodities
            commodities = df['commodity'].unique()
            info(f"Found {len(commodities)} unique commodities in the dataset")
            
            # Process each commodity
            valid_count = 0
            with progress("Processing commodities", total=len(commodities)) as update:
                for commodity in sorted(commodities):
                    try:
                        # Extract and validate commodity data
                        commodity_df = df[df['commodity'] == commodity].copy()
                        is_valid, validation_msgs = self.validate_commodity_data(commodity, commodity_df)
                        
                        if is_valid:
                            # Store the extracted commodity data
                            self.extracted_commodities[commodity] = commodity_df
                            valid_count += 1
                            
                            # Log coverage statistics
                            self._log_coverage_stats(commodity, commodity_df)
                            
                            info(f"✓ Validated commodity: {commodity}")
                        else:
                            warning(f"Skipping commodity {commodity} - {', '.join(validation_msgs)}")
                            
                    except Exception as e:
                        error(f"Error processing commodity {commodity}: {str(e)}")
                    
                    update(1)
            
            log_metric("valid_commodities", valid_count)
            log_metric("total_commodities", len(commodities))
            info(f"Extracted {valid_count} out of {len(commodities)} commodities")
            
            return self.extracted_commodities
    
    def validate_commodity_data(self, commodity: str, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate if a commodity has sufficient data for analysis.
        
        Args:
            commodity: Name of the commodity
            df: DataFrame containing data for the specified commodity
            
        Returns:
            Tuple of (is_valid, validation_messages)
        """
        validation_msgs = []
        
        # Check for required columns
        missing_cols = [col for col in self.config.required_columns if col not in df.columns]
        if missing_cols:
            return False, [f"Missing required columns: {', '.join(missing_cols)}"]
        
        # Check number of unique markets
        n_markets = df['market'].nunique()
        if n_markets < self.config.min_markets:
            validation_msgs.append(
                f"Insufficient markets: {n_markets} < {self.config.min_markets} minimum"
            )
        
        # Check time span
        try:
            dates = pd.to_datetime(df['date'])
            date_range = dates.max() - dates.min()
            if date_range.days < self.config.min_periods:
                validation_msgs.append(
                    f"Insufficient time span: {date_range.days} days < {self.config.min_periods} minimum"
                )
        except Exception as e:
            validation_msgs.append(f"Invalid date format: {str(e)}")
        
        # Check total observations
        if len(df) < self.config.min_observations:
            validation_msgs.append(
                f"Insufficient observations: {len(df)} < {self.config.min_observations} minimum"
            )
        
        # Check missing values in price
        missing_pct = df['price'].isna().mean()
        if missing_pct > self.config.max_missing_pct:
            validation_msgs.append(
                f"Excessive missing prices: {missing_pct:.1%} > {self.config.max_missing_pct:.0%} maximum"
            )
        
        return len(validation_msgs) == 0, validation_msgs
    
    def prepare_for_vecm(self, commodity: str, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """Prepare commodity data for VECM analysis.
        
        Args:
            commodity: Name of the commodity
            df: DataFrame containing the commodity data
            
        Returns:
            Tuple of (prepared_df, metadata)
        """
        with timer(f"prepare_for_vecm_{commodity}"):
            # Make a copy to avoid modifying the original
            df = df.copy()
            
            # Ensure date is datetime and sort
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values(['market', 'date'])
            
            # Pivot to wide format (markets × dates)
            wide_df = df.pivot(
                index='date',
                columns='market',
                values='price'
            )
            
            # Forward fill and then backfill any remaining NAs
            wide_df = wide_df.ffill().bfill()
            
            # Calculate log prices and differences
            log_prices = np.log(wide_df)
            log_returns = log_prices.diff().dropna()
            
            # Prepare metadata
            metadata = {
                'commodity': commodity,
                'n_markets': len(wide_df.columns),
                'date_range': (wide_df.index.min(), wide_df.index.max()),
                'n_observations': len(wide_df),
                'missing_values': wide_df.isna().sum().sum(),
                'price_stats': {
                    'mean': wide_df.mean().mean(),
                    'std': wide_df.stack().std(),
                    'min': wide_df.min().min(),
                    'max': wide_df.max().max(),
                }
            }
            
            # Log preparation summary
            info(f"Prepared {commodity} for VECM analysis: {metadata['n_markets']} markets, "
                 f"{metadata['n_observations']} time periods")
            
            return wide_df, metadata
    
    def _validate_input_data(self, df: pd.DataFrame) -> None:
        """Validate that input data contains all required columns.
        
        Args:
            df: Input DataFrame to validate
            
        Raises:
            ValueError: If required columns are missing
        """
        missing_cols = [col for col in self.config.required_columns if col not in df.columns]
        if missing_cols:
            raise ValueError(
                f"Input data is missing required columns: {', '.join(missing_cols)}"
            )
    
    def _log_coverage_stats(self, commodity: str, df: pd.DataFrame) -> None:
        """Log coverage statistics for a commodity.
        
        Args:
            commodity: Name of the commodity
            df: DataFrame containing the commodity data
        """
        # Calculate coverage statistics
        n_obs = len(df)
        n_markets = df['market'].nunique()
        n_periods = df['date'].nunique()
        missing_pct = df['price'].isna().mean()
        
        # Store stats
        self.coverage_stats[commodity] = {
            'n_observations': n_obs,
            'n_markets': n_markets,
            'n_periods': n_periods,
            'missing_pct': missing_pct,
            'start_date': df['date'].min(),
            'end_date': df['date'].max(),
            'markets': sorted(df['market'].unique().tolist())
        }
        
        # Log summary
        log_metric(f"{commodity}_n_markets", n_markets)
        log_metric(f"{commodity}_n_periods", n_periods)
        log_metric(f"{commodity}_missing_pct", missing_pct)
        
        info(f"  • {commodity}: {n_markets} markets, {n_periods} periods, "
             f"{n_obs} obs, {missing_pct:.1%} missing")
