"""Pooled panel regression implementation for Tier 1 analysis.

This module implements the pooled panel regression with multi-way fixed effects
using the linearmodels package. It handles the 3D panel structure by creating
market-commodity entities.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple, List, Union
import warnings
import sys

try:
    from linearmodels import PanelOLS
    from linearmodels.panel.data import PanelData
    from linearmodels.panel.results import PanelEffectsResults
    LINEARMODELS_AVAILABLE = True
except ImportError as e:
    error_message = (
        f"Failed to import linearmodels from pooled_panel_model.py. "
        f"Original error: {e}. "
        f"Current sys.path: {sys.path}"
    )
    raise ImportError(error_message)

from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind, log_metric
)
from ..core.base_model import BaseThreeTierModel, TierType
from ..core.panel_data_handler import PanelDataHandler
from ..core.results_container import ResultsContainer
from ..core.data_validator import PanelDataValidator
from .fixed_effects import extract_estimated_effects
from pydantic import BaseModel, Field


# Set module context
bind(module=__name__)


class PooledPanelConfig(BaseModel):
    """Configuration for the PooledPanelModel."""
    entity_effects: bool = Field(True, description="Include entity (market-commodity) fixed effects.")
    time_effects: bool = Field(True, description="Include time fixed effects.")
    cluster_entity: bool = Field(True, description="Cluster standard errors by entity.")
    cov_type: str = Field('clustered', description="Covariance estimator type (e.g., 'clustered', 'robust', 'unadjusted').")
    drop_absorbed: bool = Field(True, description="Drop absorbed variables during fixed effect estimation.")
    dependent_var: str = Field('usd_price', description="Name of the dependent variable.")
    independent_vars: Optional[List[str]] = Field(None, description="List of independent variable names. If None, uses defaults.")

    class Config:
        extra = 'forbid'


class PooledPanelModel(BaseThreeTierModel):
    """Pooled panel regression with multi-way fixed effects.
    
    This model implements Tier 1 of the three-tier methodology, estimating:
    
    p_{it} = α_i + γ_t + β'X_{it} + ε_{it}
    
    where:
    - i represents market-commodity entities
    - t represents time periods
    - α_i are entity fixed effects
    - γ_t are time fixed effects
    - X_{it} are control variables
    """
    
    def __init__(self, config: Optional[PooledPanelConfig] = None):
        """Initialize pooled panel model.
        
        Parameters
        ----------
        config : PooledPanelConfig, optional
            Configuration object for the pooled panel model.
        """
        self.config = config or PooledPanelConfig()
        super().__init__(tier=TierType.TIER1_POOLED, config=self.config.model_dump()) # Pass dict to parent
        
        # Check dependencies
        if not LINEARMODELS_AVAILABLE:
            raise ImportError("linearmodels package required for Tier 1 analysis")
        
        # Model configuration from PooledPanelConfig
        self.entity_effects = self.config.entity_effects
        self.time_effects = self.config.time_effects
        self.cluster_entity = self.config.cluster_entity
        self.cov_type = self.config.cov_type
        self.drop_absorbed = self.config.drop_absorbed
        
        # Components
        self.panel_handler = PanelDataHandler()
        self.validator = PanelDataValidator()
        self.model = None
        self.panel_data = None
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data for pooled panel requirements.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate
            
        Returns
        -------
        bool
            True if valid
        """
        is_valid, issues = self.validator.validate_for_tier1(data)
        
        if not is_valid:
            for issue in issues:
                error(f"Validation issue: {issue}")
        
        return is_valid
    
    def prepare_panel_data(self, data: pd.DataFrame, 
                          dependent_var: str = 'usd_price',
                          independent_vars: Optional[List[str]] = None) -> Tuple[pd.DataFrame, str, List[str]]:
        """Prepare data for panel regression.
        
        Parameters
        ----------
        data : pd.DataFrame
            Raw 3D panel data
        dependent_var : str
            Dependent variable name
        independent_vars : list, optional
            Independent variable names
            
        Returns
        -------
        tuple
            (panel_df, dependent_var, independent_vars)
        """
        with timer("prepare_panel_data"):
            # Create entity panel
            panel_df = self.panel_handler.create_entity_panel(data)
            
            # Set up variables
            if independent_vars is None:
                # Default: look for common control variables
                potential_vars = ['conflict_intensity', 'distance_to_capital', 
                                 'rainfall', 'temperature', 'fuel_price']
                independent_vars = [v for v in potential_vars if v in panel_df.columns]
                
                if not independent_vars:
                    warning("No independent variables found, running intercept-only model")
                    # Create constant for intercept-only model
                    panel_df['const'] = 1
                    independent_vars = ['const']
            
            # Log variable setup
            info(f"Dependent variable: {dependent_var}")
            info(f"Independent variables: {independent_vars}")
            
            # Handle missing data
            required_cols = [dependent_var] + independent_vars
            # Keep all columns, especially governorate and commodity for grouping
            all_cols = list(set(required_cols + ['entity', 'date', 'entity_id', 'time_id', 'governorate', 'commodity']))
            # Only keep columns that actually exist
            cols_to_keep = [col for col in all_cols if col in panel_df.columns]
            panel_df_subset = panel_df[cols_to_keep]
            
            panel_df = self.panel_handler.handle_missing_data(
                panel_df_subset,
                method='forward_fill',
                max_gap=2
            )
            
            return panel_df, dependent_var, independent_vars
    
    def fit(self, data: pd.DataFrame, 
            dependent_var: str = 'usd_price',
            independent_vars: Optional[List[str]] = None,
            **kwargs) -> 'PooledPanelModel':
        """Fit pooled panel regression.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
        dependent_var : str
            Dependent variable
        independent_vars : list, optional
            Independent variables
        **kwargs
            Additional arguments passed to PanelOLS
            
        Returns
        -------
        self
            Fitted model
        """
        with timer("fit_pooled_panel"):
            self._log_estimation_start(data)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
            
            # Prepare panel data
            panel_df, dep_var, indep_vars = self.prepare_panel_data(
                data, dependent_var=self.config.dependent_var, independent_vars=self.config.independent_vars
            )
            
            # Set index for panel
            panel_df = panel_df.set_index(['entity', 'date'])
            
            # Extract dependent and independent variables
            y = panel_df[dep_var]
            X = panel_df[indep_vars]
            
            log_data_shape("dependent_var", y)
            log_data_shape("independent_vars", X)
            
            # Create PanelData object
            self.panel_data = PanelData(panel_df)
            
            # Initialize model
            self.model = PanelOLS(
                y, X,
                entity_effects=self.entity_effects,
                time_effects=self.time_effects,
                drop_absorbed=self.drop_absorbed
            )
            
            # Fit model
            info("Fitting pooled panel regression")
            try:
                self.fit_result = self.model.fit(**kwargs)
                info(f"Model fitting completed. R-squared: {self.fit_result.rsquared_overall:.4f}")
            except Exception as e:
                error(f"Error fitting model: {e}")
                raise
            
            # Store results
            self._store_results(panel_df, dep_var, indep_vars)
            
            self.is_fitted = True
            self._log_estimation_complete(0.0)  # Use placeholder time
            
            return self
    
    def _store_results(self, panel_df: pd.DataFrame, dep_var: str, indep_vars: List[str]):
        """Store estimation results in standardized container."""
        # Initialize results container
        self.results = ResultsContainer(
            tier="tier1_pooled",
            model_type="PanelOLS"
        )
        
        # Store coefficients
        for var in indep_vars:
            if var in self.fit_result.params.index:
                coef = self.fit_result.params[var]
                se = self.fit_result.std_errors[var]
                ci = (self.fit_result.conf_int().loc[var, 'lower'],
                      self.fit_result.conf_int().loc[var, 'upper'])
                
                self.results.add_coefficient(var, coef, se, ci)
        
        # Store model fit statistics
        n_obs = self.fit_result.nobs
        n_entities = panel_df.index.get_level_values(0).nunique()
        n_periods = panel_df.index.get_level_values(1).nunique()
        
        self.results.metadata.update({
            'n_observations': n_obs,
            'n_entities': n_entities,
            'n_periods': n_periods,
            'estimation_time': 0.0,  # linearmodels doesn't provide this
            'convergence': True
        })
        
        # R-squared values
        self.results.set_comparison_metrics(
            n_obs=n_obs,
            n_params=len(self.fit_result.params),
            log_lik=self.fit_result.loglik if hasattr(self.fit_result, 'loglik') else -np.inf,
            r2=self.fit_result.rsquared
        )
        
        # Store additional R-squared measures
        self.results.tier_specific['r_squared_within'] = self.fit_result.rsquared_within
        self.results.tier_specific['r_squared_between'] = self.fit_result.rsquared_between
        self.results.tier_specific['r_squared_overall'] = self.fit_result.rsquared
        
        # Store fitted values and residuals
        self.results.fitted_values = self.fit_result.fitted_values
        self.results.residuals = self.fit_result.resids
        
        # F-test for joint significance
        if hasattr(self.fit_result, 'f_statistic'):
            self.results.add_diagnostics(
                f_statistic=(self.fit_result.f_statistic.stat, 
                           self.fit_result.f_statistic.pval)
            )
        
        # Effects counts
        if self.entity_effects:
            self.results.tier_specific['n_entity_effects'] = n_entities
        if self.time_effects:
            self.results.tier_specific['n_time_effects'] = n_periods
        
        # Log key metrics
        log_metric("r_squared_within", self.fit_result.rsquared_within)
        log_metric("r_squared_between", self.fit_result.rsquared_between)
        log_metric("n_parameters", len(self.fit_result.params))
    
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Generate predictions from fitted model.
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            New data for prediction
            
        Returns
        -------
        pd.Series
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        if data is None:
            # In-sample prediction
            return self.fit_result.fitted_values
        else:
            # Out-of-sample prediction
            # Prepare new data
            panel_df, _, indep_vars = self.prepare_panel_data(data)
            panel_df = panel_df.set_index(['entity', 'date'])
            X_new = panel_df[indep_vars]
            
            # Generate predictions
            predictions = self.fit_result.predict(X_new)
            
            return predictions
    
    def get_fixed_effects(self) -> Dict[str, pd.Series]:
        """Extract estimated fixed effects.
        
        Returns
        -------
        dict
            Dictionary with 'entity' and 'time' effects
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        # Use the extract_estimated_effects function
        return extract_estimated_effects(self.fit_result)
    
    def residual_diagnostics(self) -> pd.DataFrame:
        """Run diagnostic tests on residuals.
        
        Returns
        -------
        pd.DataFrame
            Diagnostic test results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        resids = self.fit_result.resids
        
        # Basic residual statistics
        diag_results = {
            'mean': resids.mean(),
            'std': resids.std(),
            'skewness': resids.skew(),
            'kurtosis': resids.kurtosis(),
            'min': resids.min(),
            'max': resids.max()
        }
        
        # Jarque-Bera test for normality
        from scipy import stats
        jb_stat, jb_pval = stats.jarque_bera(resids.dropna())
        diag_results['jarque_bera_stat'] = jb_stat
        diag_results['jarque_bera_pval'] = jb_pval
        
        # Store in results container
        self.results.add_diagnostics(jarque_bera=(jb_stat, jb_pval))
        
        return pd.Series(diag_results).to_frame('value')
    
    def summary(self) -> str:
        """Get model summary.
        
        Returns
        -------
        str
            Formatted summary
        """
        if not self.is_fitted:
            return "Model not yet fitted"
        
        # Use linearmodels summary as base
        base_summary = str(self.fit_result.summary)
        
        # Add our custom summary
        custom_summary = self.results.summary()
        
        return f"{base_summary}\n\n{custom_summary}"
