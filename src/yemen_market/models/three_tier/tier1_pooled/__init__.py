"""Tier 1: Pooled panel regression with multi-way fixed effects.

This module implements the first tier of analysis using linearmodels
to estimate pooled panel regressions with market and commodity fixed effects.
"""

from yemen_market.utils.logging import bind
from .pooled_panel_model import PooledPanelModel, PooledPanelConfig
from .fixed_effects import FixedEffectsExtractor
from .standard_errors import StandardErrorCorrector # Changed Calculator to Corrector

# Set module context
bind(module=__name__)

__all__ = [
    'PooledPanelModel',
    'PooledPanelConfig',
    'FixedEffectsExtractor',
    'StandardErrorCorrector' # Changed Calculator to Corrector
]
