"""Three-tier analysis orchestrator.

This module coordinates the execution of all three tiers of the market
integration methodology, ensuring proper data flow and result aggregation.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path
import json
import warnings

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler
from yemen_market.models.three_tier.core.results_container import ResultsContainer

# Import tier models
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel
from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor
from yemen_market.models.three_tier.tier3_validation import (
    StaticFactorModel, PCAMarketIntegration, ConflictIntegrationValidator
)

# Set module context
bind(module=__name__)


class ThreeTierAnalysis:
    """Master coordinator for three-tier market integration analysis.
    
    This class orchestrates the full three-tier methodology:
    1. Tier 1: Pooled panel analysis with fixed effects
    2. Tier 2: Commodity-specific threshold models
    3. Tier 3: Factor analysis and validation
    
    It handles data flow between tiers, error handling, and result aggregation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize three-tier analysis.
        
        Parameters
        ----------
        config : dict, optional
            Configuration for all tiers:
            - tier1_config: Config for pooled panel model
            - tier2_config: Config for commodity models
            - tier3_config: Config for validation models
            - output_dir: Directory for saving results
            - run_parallel: Whether to run tiers in parallel (default: False)
        """
        self.config = config or {}
        
        # Extract tier-specific configs
        self.tier1_config = self.config.get('tier1_config', {})
        self.tier2_config = self.config.get('tier2_config', {})
        self.tier3_config = self.config.get('tier3_config', {})
        
        # General settings
        self.output_dir = Path(self.config.get('output_dir', 'results/three_tier'))
        self.run_parallel = self.config.get('run_parallel', False)
        
        # Initialize components
        self.panel_handler = PanelDataHandler()
        self.tier1_model = None
        self.tier2_models = {}
        self.tier3_models = {}
        
        # Results storage
        self.results = {
            'tier1': None,
            'tier2': {},
            'tier3': {},
            'summary': {}
        }
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        info(f"Initialized ThreeTierAnalysis with output to {self.output_dir}")
    
    def run_full_analysis(self, data: pd.DataFrame, 
                         conflict_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Run complete three-tier analysis.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with columns: governorate, commodity, date, usd_price
        conflict_data : pd.DataFrame, optional
            Conflict events for Tier 3 validation
            
        Returns
        -------
        dict
            Complete results from all tiers
        """
        with timer("full_three_tier_analysis"):
            info("Starting three-tier market integration analysis")
            
            # Validate input data
            if not self.panel_handler.validate_3d_structure(data):
                raise ValueError("Invalid panel data structure")
            
            # Store data for access by all tiers
            self.data = data
            self.conflict_data = conflict_data
            
            try:
                # Run Tier 1: Pooled panel analysis
                info("=" * 60)
                info("TIER 1: Pooled Panel Analysis")
                info("=" * 60)
                self._run_tier1()
                
                # Run Tier 2: Commodity-specific analysis
                info("=" * 60)
                info("TIER 2: Commodity-Specific Analysis")
                info("=" * 60)
                self._run_tier2()
                
                # Run Tier 3: Validation and factor analysis
                info("=" * 60)
                info("TIER 3: Validation Analysis")
                info("=" * 60)
                self._run_tier3()
                
                # Cross-tier validation
                info("=" * 60)
                info("CROSS-TIER VALIDATION")
                info("=" * 60)
                self._cross_tier_validation()
                
                # Generate summary
                self._generate_summary()
                
                # Save all results
                self._save_results()
                
                elapsed = timer.get_elapsed("full_three_tier_analysis")
                info(f"Three-tier analysis completed in {elapsed:.2f} seconds")
                
            except Exception as e:
                error(f"Error in three-tier analysis: {str(e)}")
                raise
            
            return self.results
    
    def _run_tier1(self) -> None:
        """Run Tier 1 pooled panel analysis."""
        with timer("tier1_analysis"):
            try:
                # Initialize model
                self.tier1_model = PooledPanelModel(self.tier1_config)
                
                # Fit model
                self.tier1_model.fit(self.data)
                
                # Store results
                self.results['tier1'] = self.tier1_model.get_results()
                
                # Log key metrics
                results = self.results['tier1']
                if hasattr(results, 'comparison_metrics') and results.comparison_metrics:
                    log_metric("tier1_r_squared", results.comparison_metrics.r_squared)
                
                info("Tier 1 analysis completed successfully")
                
            except Exception as e:
                error(f"Error in Tier 1: {str(e)}")
                self.results['tier1'] = {'error': str(e)}
    
    def _run_tier2(self) -> None:
        """Run Tier 2 commodity-specific analysis."""
        with timer("tier2_analysis"):
            # Get unique commodities
            commodities = self.data['commodity'].unique()
            info(f"Running Tier 2 for {len(commodities)} commodities")
            
            # Initialize commodity extractor
            extractor = CommodityExtractor(self.tier2_config)
            
            with progress("Analyzing commodities", total=len(commodities)) as update:
                for commodity in commodities:
                    try:
                        # Extract and analyze commodity
                        commodity_results = extractor.analyze_commodity(
                            self.data, 
                            commodity
                        )
                        
                        # Store results
                        self.tier2_models[commodity] = commodity_results
                        self.results['tier2'][commodity] = commodity_results.get('results')
                        
                        info(f"Completed analysis for {commodity}")
                        
                    except Exception as e:
                        warning(f"Error analyzing {commodity}: {str(e)}")
                        self.results['tier2'][commodity] = {'error': str(e)}
                    
                    update(1)
            
            info(f"Tier 2 completed: {len(self.tier2_models)} commodities analyzed")
    
    def _run_tier3(self) -> None:
        """Run Tier 3 validation analysis."""
        with timer("tier3_analysis"):
            # 1. Static factor analysis
            try:
                info("Running static factor analysis")
                static_model = StaticFactorModel(self.tier3_config)
                static_model.fit(self.data)
                self.tier3_models['static_factors'] = static_model
                self.results['tier3']['static_factors'] = static_model.get_results()
            except Exception as e:
                warning(f"Error in static factor analysis: {str(e)}")
                self.results['tier3']['static_factors'] = {'error': str(e)}
            
            # 2. PCA integration analysis
            try:
                info("Running PCA integration analysis")
                pca_analyzer = PCAMarketIntegration(self.tier3_config)
                integration_report = pca_analyzer.generate_integration_report(self.data)
                self.tier3_models['pca_integration'] = pca_analyzer
                self.results['tier3']['pca_integration'] = integration_report
            except Exception as e:
                warning(f"Error in PCA integration analysis: {str(e)}")
                self.results['tier3']['pca_integration'] = {'error': str(e)}
            
            # 3. Conflict validation (if data available)
            if self.conflict_data is not None:
                try:
                    info("Running conflict validation analysis")
                    conflict_validator = ConflictIntegrationValidator(self.tier3_config)
                    
                    # Get integration scores from PCA analysis
                    integration_scores = None
                    if 'pca_integration' in self.tier3_models:
                        integration_scores = self.tier3_models['pca_integration'].rolling_pca_analysis(self.data)
                    
                    conflict_validator.fit(
                        self.data,
                        conflict_data=self.conflict_data,
                        integration_scores=integration_scores
                    )
                    
                    self.tier3_models['conflict_validation'] = conflict_validator
                    self.results['tier3']['conflict_validation'] = conflict_validator.get_results()
                    
                except Exception as e:
                    warning(f"Error in conflict validation: {str(e)}")
                    self.results['tier3']['conflict_validation'] = {'error': str(e)}
            else:
                info("Skipping conflict validation (no conflict data provided)")
            
            info("Tier 3 validation completed")
    
    def _cross_tier_validation(self) -> None:
        """Perform cross-tier validation and consistency checks."""
        with timer("cross_tier_validation"):
            validation_results = {}
            
            # 1. Compare Tier 1 and Tier 2 results
            if self.results['tier1'] and self.results['tier2']:
                validation_results['tier1_vs_tier2'] = self._compare_tier1_tier2()
            
            # 2. Validate Tier 2 patterns with Tier 3 factors
            if self.results['tier2'] and self.results['tier3'].get('static_factors'):
                validation_results['tier2_vs_tier3'] = self._validate_tier2_with_factors()
            
            # 3. Check consistency of integration measures
            validation_results['integration_consistency'] = self._check_integration_consistency()
            
            # Store validation results
            self.results['cross_validation'] = validation_results
            
            # Log any inconsistencies
            for check, result in validation_results.items():
                if result.get('inconsistent'):
                    warning(f"Inconsistency found in {check}: {result.get('message')}")
    
    def _compare_tier1_tier2(self) -> Dict[str, Any]:
        """Compare results between Tier 1 and Tier 2."""
        comparison = {
            'consistent': True,
            'details': {}
        }
        
        # Extract Tier 1 coefficients if available
        tier1_results = self.results['tier1']
        if hasattr(tier1_results, 'coefficients'):
            tier1_effects = tier1_results.coefficients
            
            # Compare with average effects across commodities in Tier 2
            commodity_effects = []
            for commodity, results in self.results['tier2'].items():
                if isinstance(results, dict) and 'coefficients' in results:
                    commodity_effects.append(results['coefficients'])
            
            if commodity_effects:
                # Check if signs are consistent
                # This is a simplified check - real implementation would be more sophisticated
                comparison['details']['n_commodities_analyzed'] = len(commodity_effects)
                comparison['details']['tier1_has_results'] = bool(tier1_effects)
            
        return comparison
    
    def _validate_tier2_with_factors(self) -> Dict[str, Any]:
        """Validate Tier 2 patterns using Tier 3 factor analysis."""
        validation = {
            'factor_alignment': {},
            'commodity_clustering': {}
        }
        
        # Get factor loadings from Tier 3
        tier3_results = self.results['tier3'].get('static_factors')
        if hasattr(tier3_results, 'tier_specific') and 'loadings' in tier3_results.tier_specific:
            loadings = tier3_results.tier_specific['loadings']
            
            # Check which commodities load heavily on each factor
            # This helps validate commodity-specific patterns from Tier 2
            for commodity in self.results['tier2']:
                # Extract commodity-specific series from loadings
                commodity_loadings = loadings[loadings.index.str.contains(commodity)]
                
                if not commodity_loadings.empty:
                    # Find dominant factor for this commodity
                    dominant_factor = commodity_loadings.abs().mean().idxmax()
                    validation['factor_alignment'][commodity] = {
                        'dominant_factor': dominant_factor,
                        'loading_strength': commodity_loadings[dominant_factor].mean()
                    }
        
        return validation
    
    def _check_integration_consistency(self) -> Dict[str, Any]:
        """Check consistency of integration measures across tiers."""
        consistency = {
            'measures': {},
            'consistent': True
        }
        
        # Collect integration measures from different sources
        
        # From Tier 1 - R-squared as integration proxy
        if self.results['tier1'] and hasattr(self.results['tier1'], 'comparison_metrics'):
            consistency['measures']['tier1_r_squared'] = (
                self.results['tier1'].comparison_metrics.r_squared
            )
        
        # From Tier 3 - PCA first component variance
        tier3_pca = self.results['tier3'].get('pca_integration')
        if tier3_pca and hasattr(tier3_pca, 'tier_specific'):
            overall_integration = tier3_pca.tier_specific.get('overall_integration', {})
            if 'pc1_variance_explained' in overall_integration:
                consistency['measures']['tier3_pc1_variance'] = (
                    overall_integration['pc1_variance_explained']
                )
        
        # Check if measures are aligned (both high or both low)
        if len(consistency['measures']) >= 2:
            values = list(consistency['measures'].values())
            # Simple consistency check - all above 0.5 or all below 0.5
            if not (all(v > 0.5 for v in values) or all(v < 0.5 for v in values)):
                consistency['consistent'] = False
                consistency['message'] = "Integration measures show conflicting signals"
        
        return consistency
    
    def _generate_summary(self) -> None:
        """Generate executive summary of findings."""
        summary = {
            'overview': {},
            'key_findings': [],
            'recommendations': []
        }
        
        # Data overview
        summary['overview'] = {
            'n_observations': len(self.data),
            'n_markets': self.data['governorate'].nunique(),
            'n_commodities': self.data['commodity'].nunique(),
            'date_range': (
                self.data['date'].min().strftime('%Y-%m-%d'),
                self.data['date'].max().strftime('%Y-%m-%d')
            ),
            'tiers_completed': sum(
                1 for tier_results in [self.results['tier1'], self.results['tier2'], self.results['tier3']]
                if tier_results
            )
        }
        
        # Extract key findings from each tier
        
        # Tier 1 findings
        if self.results['tier1'] and hasattr(self.results['tier1'], 'metadata'):
            if self.results['tier1'].metadata.get('warnings'):
                summary['key_findings'].extend([
                    f"Tier 1: {w}" for w in self.results['tier1'].metadata['warnings']
                ])
        
        # Tier 2 findings
        high_integration_commodities = []
        for commodity, results in self.results['tier2'].items():
            if isinstance(results, dict) and results.get('integration_level') == 'High':
                high_integration_commodities.append(commodity)
        
        if high_integration_commodities:
            summary['key_findings'].append(
                f"Tier 2: High integration found for: {', '.join(high_integration_commodities)}"
            )
        
        # Tier 3 findings
        tier3_pca = self.results['tier3'].get('pca_integration')
        if tier3_pca and hasattr(tier3_pca, 'tier_specific'):
            overall = tier3_pca.tier_specific.get('overall_integration', {})
            if 'integration_level' in overall:
                summary['key_findings'].append(
                    f"Tier 3: Overall market integration level is {overall['integration_level']}"
                )
        
        # Conflict impact
        conflict_results = self.results['tier3'].get('conflict_validation')
        if conflict_results and hasattr(conflict_results, 'tier_specific'):
            event_study = conflict_results.tier_specific.get('event_study', {})
            if 'pct_events_reducing_integration' in event_study:
                summary['key_findings'].append(
                    f"Tier 3: {event_study['pct_events_reducing_integration']:.1f}% of conflict "
                    "events reduce market integration"
                )
        
        # Generate recommendations based on findings
        if summary['overview'].get('tiers_completed', 0) == 3:
            summary['recommendations'].append(
                "Complete three-tier analysis provides robust evidence for policy decisions"
            )
        
        # Store summary
        self.results['summary'] = summary
    
    def _save_results(self) -> None:
        """Save all results to disk."""
        with timer("save_results"):
            info(f"Saving results to {self.output_dir}")
            
            # Save individual tier results
            for tier in ['tier1', 'tier2', 'tier3']:
                tier_dir = self.output_dir / tier
                tier_dir.mkdir(exist_ok=True)
                
                if tier == 'tier2':
                    # Save each commodity separately
                    for commodity, results in self.results[tier].items():
                        if isinstance(results, (ResultsContainer, dict)):
                            output_file = tier_dir / f"{commodity}_results.json"
                            self._save_tier_results(results, output_file)
                else:
                    # Save tier results
                    if self.results[tier]:
                        output_file = tier_dir / f"{tier}_results.json"
                        self._save_tier_results(self.results[tier], output_file)
            
            # Save cross-validation results
            if 'cross_validation' in self.results:
                cv_file = self.output_dir / "cross_validation_results.json"
                with open(cv_file, 'w') as f:
                    json.dump(self.results['cross_validation'], f, indent=2, default=str)
            
            # Save summary
            summary_file = self.output_dir / "analysis_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(self.results['summary'], f, indent=2, default=str)
            
            # Create summary report
            self._create_summary_report()
            
            info("All results saved successfully")
    
    def _save_tier_results(self, results: Union[ResultsContainer, Dict], 
                          output_file: Path) -> None:
        """Save tier-specific results."""
        if isinstance(results, ResultsContainer):
            results.save(output_file)
        else:
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
    
    def _create_summary_report(self) -> None:
        """Create a markdown summary report."""
        report_file = self.output_dir / "three_tier_analysis_report.md"
        
        lines = [
            "# Three-Tier Market Integration Analysis Report",
            f"\nGenerated: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "\n## Executive Summary\n"
        ]
        
        # Add overview
        summary = self.results['summary']
        lines.append(f"- **Markets analyzed**: {summary['overview']['n_markets']}")
        lines.append(f"- **Commodities**: {summary['overview']['n_commodities']}")
        lines.append(f"- **Time period**: {summary['overview']['date_range'][0]} to {summary['overview']['date_range'][1]}")
        lines.append(f"- **Total observations**: {summary['overview']['n_observations']:,}")
        
        # Key findings
        if summary['key_findings']:
            lines.append("\n## Key Findings\n")
            for finding in summary['key_findings']:
                lines.append(f"- {finding}")
        
        # Tier summaries
        lines.append("\n## Tier Results\n")
        
        # Tier 1
        lines.append("### Tier 1: Pooled Panel Analysis")
        if self.results['tier1'] and hasattr(self.results['tier1'], 'comparison_metrics'):
            metrics = self.results['tier1'].comparison_metrics
            lines.append(f"- R-squared: {metrics.r_squared:.4f}")
            lines.append(f"- Observations: {self.results['tier1'].metadata.get('n_observations', 'N/A')}")
        
        # Tier 2
        lines.append("\n### Tier 2: Commodity-Specific Analysis")
        lines.append(f"- Commodities analyzed: {len(self.results['tier2'])}")
        
        # Tier 3
        lines.append("\n### Tier 3: Validation Analysis")
        if self.results['tier3']:
            lines.append(f"- Validation methods: {', '.join(self.results['tier3'].keys())}")
        
        # Recommendations
        if summary['recommendations']:
            lines.append("\n## Recommendations\n")
            for rec in summary['recommendations']:
                lines.append(f"- {rec}")
        
        # Write report
        with open(report_file, 'w') as f:
            f.write('\n'.join(lines))
        
        info(f"Summary report saved to {report_file}")
    
    def get_commodity_comparison(self) -> pd.DataFrame:
        """Get comparison of results across commodities.
        
        Returns
        -------
        pd.DataFrame
            Comparison table
        """
        if not self.results['tier2']:
            return pd.DataFrame()
        
        comparison_data = []
        
        for commodity, results in self.results['tier2'].items():
            if isinstance(results, dict) and 'error' not in results:
                row = {
                    'commodity': commodity,
                    'integration_level': results.get('integration_level', 'Unknown'),
                    'n_observations': results.get('n_observations', 0),
                    'r_squared': results.get('r_squared', np.nan)
                }
                
                # Add threshold information if available
                if 'threshold_value' in results:
                    row['has_threshold'] = True
                    row['threshold_value'] = results['threshold_value']
                else:
                    row['has_threshold'] = False
                    
                comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def visualize_results(self) -> None:
        """Create visualizations of results (placeholder for future implementation)."""
        warning("Visualization not yet implemented. Results saved to JSON files.")
        # Future: Add matplotlib/seaborn visualizations