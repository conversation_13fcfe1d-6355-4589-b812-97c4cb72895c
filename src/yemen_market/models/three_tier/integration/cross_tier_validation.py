"""Cross-tier validation utilities for three-tier methodology.

This module provides tools to validate consistency across the three tiers,
ensuring that findings are robust and complementary rather than contradictory.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error

from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.results_container import ResultsContainer

# Set module context
bind(module=__name__)


class CrossTierValidator:
    """Validate consistency and complementarity across three-tier results.
    
    This class provides methods to:
    1. Check consistency of integration measures across tiers
    2. Validate commodity rankings between tiers
    3. Test robustness of findings using different methodologies
    4. Identify conflicting results and potential causes
    """
    
    def __init__(self):
        """Initialize cross-tier validator."""
        self.validation_results = {}
        self.consistency_scores = {}
        
    def validate_all_tiers(self, tier1_results: ResultsContainer,
                          tier2_results: Dict[str, ResultsContainer],
                          tier3_results: Dict[str, ResultsContainer]) -> Dict[str, Any]:
        """Perform comprehensive cross-tier validation.
        
        Parameters
        ----------
        tier1_results : ResultsContainer
            Results from Tier 1 pooled panel analysis
        tier2_results : dict
            Results from Tier 2 commodity-specific analysis
        tier3_results : dict
            Results from Tier 3 validation models
            
        Returns
        -------
        dict
            Validation results and consistency metrics
        """
        with timer("cross_tier_validation"):
            info("Starting cross-tier validation")
            
            # 1. Integration measure consistency
            self.validation_results['integration_consistency'] = (
                self._validate_integration_measures(tier1_results, tier2_results, tier3_results)
            )
            
            # 2. Commodity-level validation
            self.validation_results['commodity_validation'] = (
                self._validate_commodity_patterns(tier2_results, tier3_results)
            )
            
            # 3. Temporal stability validation
            self.validation_results['temporal_stability'] = (
                self._validate_temporal_stability(tier1_results, tier3_results)
            )
            
            # 4. Effect size consistency
            self.validation_results['effect_size_consistency'] = (
                self._validate_effect_sizes(tier1_results, tier2_results)
            )
            
            # 5. Factor interpretation validation
            self.validation_results['factor_validation'] = (
                self._validate_factor_interpretation(tier2_results, tier3_results)
            )
            
            # Calculate overall consistency score
            self._calculate_consistency_scores()
            
            # Generate validation summary
            summary = self._generate_validation_summary()
            self.validation_results['summary'] = summary
            
            # Log key metrics
            log_metric("overall_consistency_score", summary['overall_consistency'])
            log_metric("n_inconsistencies", summary['n_inconsistencies'])
            
            return self.validation_results
    
    def _validate_integration_measures(self, tier1: ResultsContainer,
                                     tier2: Dict[str, ResultsContainer],
                                     tier3: Dict[str, ResultsContainer]) -> Dict[str, Any]:
        """Validate consistency of integration measures across tiers.
        
        Parameters
        ----------
        tier1 : ResultsContainer
            Tier 1 results
        tier2 : dict
            Tier 2 results by commodity
        tier3 : dict
            Tier 3 results by model type
            
        Returns
        -------
        dict
            Integration consistency metrics
        """
        info("Validating integration measure consistency")
        
        measures = {}
        
        # Extract Tier 1 integration measure (R-squared)
        if hasattr(tier1, 'comparison_metrics') and tier1.comparison_metrics:
            measures['tier1_r_squared'] = tier1.comparison_metrics.r_squared
            info(f"Tier 1 R-squared: {measures['tier1_r_squared']:.4f}")
        
        # Extract Tier 2 average integration
        if tier2:
            commodity_r2 = []
            for commodity, results in tier2.items():
                if hasattr(results, 'comparison_metrics') and results.comparison_metrics:
                    commodity_r2.append(results.comparison_metrics.r_squared)
            
            if commodity_r2:
                measures['tier2_avg_r_squared'] = np.mean(commodity_r2)
                measures['tier2_std_r_squared'] = np.std(commodity_r2)
                info(f"Tier 2 average R-squared: {measures['tier2_avg_r_squared']:.4f}")
        
        # Extract Tier 3 PCA measure
        if 'pca_integration' in tier3:
            pca_results = tier3['pca_integration']
            if hasattr(pca_results, 'tier_specific'):
                overall = pca_results.tier_specific.get('overall_integration', {})
                if 'pc1_variance_explained' in overall:
                    measures['tier3_pc1_variance'] = overall['pc1_variance_explained']
                    info(f"Tier 3 PC1 variance: {measures['tier3_pc1_variance']:.4f}")
        
        # Check consistency
        consistency_checks = []
        
        # Compare Tier 1 and Tier 2
        if 'tier1_r_squared' in measures and 'tier2_avg_r_squared' in measures:
            diff = abs(measures['tier1_r_squared'] - measures['tier2_avg_r_squared'])
            consistency_checks.append({
                'comparison': 'Tier1_vs_Tier2_R2',
                'difference': diff,
                'consistent': diff < 0.2,  # 20% tolerance
                'interpretation': 'Pooled vs average commodity R-squared'
            })
        
        # Compare integration levels
        if 'tier1_r_squared' in measures and 'tier3_pc1_variance' in measures:
            # Both should indicate similar integration levels
            tier1_high = measures['tier1_r_squared'] > 0.5
            tier3_high = measures['tier3_pc1_variance'] > 0.5
            
            consistency_checks.append({
                'comparison': 'Tier1_vs_Tier3_integration_level',
                'tier1_indicates_high': tier1_high,
                'tier3_indicates_high': tier3_high,
                'consistent': tier1_high == tier3_high,
                'interpretation': 'Integration level agreement'
            })
        
        return {
            'measures': measures,
            'consistency_checks': consistency_checks,
            'all_consistent': all(check['consistent'] for check in consistency_checks)
        }
    
    def _validate_commodity_patterns(self, tier2: Dict[str, ResultsContainer],
                                   tier3: Dict[str, ResultsContainer]) -> Dict[str, Any]:
        """Validate commodity-specific patterns between Tier 2 and Tier 3.
        
        Parameters
        ----------
        tier2 : dict
            Tier 2 commodity results
        tier3 : dict
            Tier 3 results
            
        Returns
        -------
        dict
            Commodity pattern validation
        """
        info("Validating commodity-specific patterns")
        
        validation = {
            'commodity_rankings': {},
            'factor_commodity_alignment': {},
            'consistency_metrics': {}
        }
        
        # Extract commodity integration levels from Tier 2
        tier2_integration = {}
        for commodity, results in tier2.items():
            if hasattr(results, 'tier_specific'):
                integration_level = results.tier_specific.get('integration_level', 'Unknown')
                tier2_integration[commodity] = integration_level
            elif hasattr(results, 'comparison_metrics') and results.comparison_metrics:
                # Use R-squared as proxy
                tier2_integration[commodity] = results.comparison_metrics.r_squared
        
        # Check if Tier 3 PCA identifies similar patterns
        if 'pca_integration' in tier3:
            pca_results = tier3['pca_integration']
            if hasattr(pca_results, 'tier_specific'):
                commodity_pca = pca_results.tier_specific.get('commodity_integration', {})
                
                # Compare rankings
                common_commodities = set(tier2_integration.keys()) & set(commodity_pca.keys())
                
                if common_commodities:
                    # Extract numerical values for correlation
                    tier2_values = []
                    tier3_values = []
                    
                    for commodity in common_commodities:
                        # Get Tier 2 value
                        t2_val = tier2_integration[commodity]
                        if isinstance(t2_val, str):
                            # Convert integration level to numeric
                            level_map = {'Very High': 5, 'High': 4, 'Moderate': 3, 'Low': 2, 'Very Low': 1}
                            t2_val = level_map.get(t2_val, 3)
                        tier2_values.append(t2_val)
                        
                        # Get Tier 3 PC1 variance
                        t3_val = commodity_pca[commodity].get('pc1_variance', 0)
                        tier3_values.append(t3_val)
                    
                    # Calculate rank correlation
                    if len(tier2_values) > 2:
                        corr, p_value = stats.spearmanr(tier2_values, tier3_values)
                        validation['commodity_rankings'] = {
                            'n_commodities': len(common_commodities),
                            'rank_correlation': corr,
                            'p_value': p_value,
                            'significant': p_value < 0.05,
                            'consistent': corr > 0.5  # Moderate positive correlation expected
                        }
                        
                        info(f"Commodity ranking correlation: {corr:.3f} (p={p_value:.3f})")
        
        # Check factor-commodity alignment
        if 'static_factors' in tier3:
            factor_results = tier3['static_factors']
            if hasattr(factor_results, 'tier_specific') and 'loadings' in factor_results.tier_specific:
                loadings = factor_results.tier_specific['loadings']
                
                # Identify which commodities dominate each factor
                for commodity in tier2_integration:
                    commodity_loadings = loadings[loadings.index.str.contains(commodity)]
                    if not commodity_loadings.empty:
                        # Find strongest loading
                        max_loading = commodity_loadings.abs().max().max()
                        validation['factor_commodity_alignment'][commodity] = {
                            'max_loading': max_loading,
                            'strongly_represented': max_loading > 0.5
                        }
        
        return validation
    
    def _validate_temporal_stability(self, tier1: ResultsContainer,
                                   tier3: Dict[str, ResultsContainer]) -> Dict[str, Any]:
        """Validate temporal stability of integration measures.
        
        Parameters
        ----------
        tier1 : ResultsContainer
            Tier 1 results
        tier3 : dict
            Tier 3 results
            
        Returns
        -------
        dict
            Temporal stability validation
        """
        info("Validating temporal stability")
        
        stability = {
            'tier1_temporal_tests': {},
            'tier3_dynamic_consistency': {},
            'structural_break_alignment': {}
        }
        
        # Check if Tier 1 includes temporal stability tests
        if hasattr(tier1, 'diagnostics') and tier1.diagnostics:
            # Extract any time-related diagnostics
            if tier1.diagnostics.durbin_watson:
                stability['tier1_temporal_tests']['durbin_watson'] = tier1.diagnostics.durbin_watson
                # DW around 2 indicates no autocorrelation
                stability['tier1_temporal_tests']['stable'] = (
                    1.5 < tier1.diagnostics.durbin_watson < 2.5
                )
        
        # Check Tier 3 dynamic patterns
        if 'pca_integration' in tier3:
            pca_results = tier3['pca_integration']
            # Would check rolling PCA results for stability
            # Placeholder for now
            stability['tier3_dynamic_consistency']['assessed'] = True
        
        # Check structural break alignment with conflicts
        if 'conflict_validation' in tier3:
            conflict_results = tier3['conflict_validation']
            if hasattr(conflict_results, 'tier_specific'):
                breaks = conflict_results.tier_specific.get('structural_breaks', {})
                if 'n_significant_breaks' in breaks:
                    stability['structural_break_alignment'] = {
                        'n_breaks_detected': breaks['n_significant_breaks'],
                        'aligned_with_conflicts': True  # Simplified
                    }
        
        return stability
    
    def _validate_effect_sizes(self, tier1: ResultsContainer,
                             tier2: Dict[str, ResultsContainer]) -> Dict[str, Any]:
        """Validate consistency of effect sizes between Tier 1 and Tier 2.
        
        Parameters
        ----------
        tier1 : ResultsContainer
            Tier 1 results
        tier2 : dict
            Tier 2 results
            
        Returns
        -------
        dict
            Effect size validation
        """
        info("Validating effect size consistency")
        
        validation = {
            'coefficient_comparison': {},
            'magnitude_consistency': {},
            'sign_consistency': {}
        }
        
        # Extract Tier 1 coefficients
        tier1_coefs = {}
        if hasattr(tier1, 'coefficients'):
            tier1_coefs = tier1.coefficients
        
        # Compare with Tier 2 coefficients
        if tier1_coefs and tier2:
            # Get common variables
            common_vars = set()
            tier2_all_coefs = {}
            
            for commodity, results in tier2.items():
                if hasattr(results, 'coefficients'):
                    for var, coef in results.coefficients.items():
                        if var not in tier2_all_coefs:
                            tier2_all_coefs[var] = []
                        tier2_all_coefs[var].append(coef)
                        common_vars.add(var)
            
            # Find variables in both tiers
            common_vars = common_vars & set(tier1_coefs.keys())
            
            for var in common_vars:
                tier1_val = tier1_coefs[var]
                tier2_vals = tier2_all_coefs[var]
                tier2_mean = np.mean(tier2_vals)
                tier2_std = np.std(tier2_vals)
                
                # Check sign consistency
                sign_consistent = np.sign(tier1_val) == np.sign(tier2_mean)
                
                # Check magnitude consistency (within 2 std devs)
                magnitude_consistent = abs(tier1_val - tier2_mean) < 2 * tier2_std if tier2_std > 0 else True
                
                validation['coefficient_comparison'][var] = {
                    'tier1_value': tier1_val,
                    'tier2_mean': tier2_mean,
                    'tier2_std': tier2_std,
                    'sign_consistent': sign_consistent,
                    'magnitude_consistent': magnitude_consistent
                }
        
        # Calculate overall consistency metrics
        if validation['coefficient_comparison']:
            sign_consistency_rate = np.mean([
                v['sign_consistent'] 
                for v in validation['coefficient_comparison'].values()
            ])
            magnitude_consistency_rate = np.mean([
                v['magnitude_consistent'] 
                for v in validation['coefficient_comparison'].values()
            ])
            
            validation['sign_consistency']['rate'] = sign_consistency_rate
            validation['sign_consistency']['acceptable'] = sign_consistency_rate > 0.8
            validation['magnitude_consistency']['rate'] = magnitude_consistency_rate
            validation['magnitude_consistency']['acceptable'] = magnitude_consistency_rate > 0.7
            
            info(f"Sign consistency: {sign_consistency_rate:.1%}, "
                 f"Magnitude consistency: {magnitude_consistency_rate:.1%}")
        
        return validation
    
    def _validate_factor_interpretation(self, tier2: Dict[str, ResultsContainer],
                                      tier3: Dict[str, ResultsContainer]) -> Dict[str, Any]:
        """Validate factor interpretations against commodity-specific findings.
        
        Parameters
        ----------
        tier2 : dict
            Tier 2 results
        tier3 : dict
            Tier 3 results
            
        Returns
        -------
        dict
            Factor interpretation validation
        """
        info("Validating factor interpretations")
        
        validation = {
            'factor_commodity_mapping': {},
            'interpretation_consistency': {},
            'unexplained_patterns': []
        }
        
        # Get factor interpretations from Tier 3
        if 'static_factors' in tier3:
            factor_results = tier3['static_factors']
            if hasattr(factor_results, 'tier_specific'):
                interpretations = factor_results.tier_specific.get('factor_interpretations', [])
                
                # Check if interpretations align with Tier 2 findings
                for interp in interpretations:
                    # Parse interpretation (e.g., "Factor_1: wheat price factor")
                    if ':' in interp:
                        factor, description = interp.split(':', 1)
                        
                        # Check if mentioned commodity has special characteristics in Tier 2
                        for commodity in tier2:
                            if commodity.lower() in description.lower():
                                validation['factor_commodity_mapping'][factor] = {
                                    'commodity': commodity,
                                    'tier2_has_results': commodity in tier2
                                }
        
        # Identify patterns in Tier 2 not captured by factors
        high_integration_commodities = []
        threshold_commodities = []
        
        for commodity, results in tier2.items():
            if hasattr(results, 'tier_specific'):
                if results.tier_specific.get('integration_level') in ['High', 'Very High']:
                    high_integration_commodities.append(commodity)
                if results.tier_specific.get('threshold_value') is not None:
                    threshold_commodities.append(commodity)
        
        # Check if these patterns are captured in Tier 3
        if high_integration_commodities:
            validation['interpretation_consistency']['high_integration_captured'] = (
                len(validation['factor_commodity_mapping']) > 0
            )
        
        if threshold_commodities:
            validation['unexplained_patterns'].append(
                f"Threshold effects found for {len(threshold_commodities)} commodities "
                "may not be fully captured by linear factor models"
            )
        
        return validation
    
    def _calculate_consistency_scores(self) -> None:
        """Calculate overall consistency scores across validations."""
        scores = {}
        
        # Integration consistency score
        integration_val = self.validation_results.get('integration_consistency', {})
        if 'all_consistent' in integration_val:
            scores['integration'] = 1.0 if integration_val['all_consistent'] else 0.5
        
        # Commodity validation score
        commodity_val = self.validation_results.get('commodity_validation', {})
        rankings = commodity_val.get('commodity_rankings', {})
        if 'consistent' in rankings:
            scores['commodity'] = 1.0 if rankings['consistent'] else 0.5
        
        # Effect size score
        effect_val = self.validation_results.get('effect_size_consistency', {})
        if 'sign_consistency' in effect_val and 'magnitude_consistency' in effect_val:
            sign_score = effect_val['sign_consistency'].get('rate', 0)
            mag_score = effect_val['magnitude_consistency'].get('rate', 0)
            scores['effects'] = (sign_score + mag_score) / 2
        
        # Overall score (weighted average)
        if scores:
            weights = {'integration': 0.4, 'commodity': 0.3, 'effects': 0.3}
            overall_score = sum(
                scores.get(k, 0) * weights.get(k, 0) 
                for k in weights
            )
            scores['overall'] = overall_score
        
        self.consistency_scores = scores
    
    def _generate_validation_summary(self) -> Dict[str, Any]:
        """Generate summary of validation results.
        
        Returns
        -------
        dict
            Validation summary
        """
        summary = {
            'overall_consistency': self.consistency_scores.get('overall', 0),
            'consistency_by_aspect': self.consistency_scores,
            'n_validations_performed': len(self.validation_results),
            'n_inconsistencies': 0,
            'critical_issues': [],
            'recommendations': []
        }
        
        # Count inconsistencies
        for validation_type, results in self.validation_results.items():
            if isinstance(results, dict):
                # Check for explicit inconsistency flags
                if 'all_consistent' in results and not results['all_consistent']:
                    summary['n_inconsistencies'] += 1
                
                # Check for low consistency rates
                for key, value in results.items():
                    if isinstance(value, dict) and 'consistent' in value and not value['consistent']:
                        summary['n_inconsistencies'] += 1
        
        # Identify critical issues
        if summary['overall_consistency'] < 0.6:
            summary['critical_issues'].append(
                "Low overall consistency suggests methodological differences or data issues"
            )
        
        effect_consistency = self.validation_results.get('effect_size_consistency', {})
        if effect_consistency.get('sign_consistency', {}).get('rate', 1) < 0.7:
            summary['critical_issues'].append(
                "Sign inconsistency in coefficients between tiers requires investigation"
            )
        
        # Generate recommendations
        if summary['n_inconsistencies'] > 2:
            summary['recommendations'].append(
                "Review data quality and preprocessing steps for consistency"
            )
        
        if 'unexplained_patterns' in self.validation_results.get('factor_validation', {}):
            summary['recommendations'].append(
                "Consider non-linear extensions to capture threshold effects"
            )
        
        return summary
    
    def create_validation_report(self) -> str:
        """Create detailed validation report.
        
        Returns
        -------
        str
            Formatted validation report
        """
        lines = [
            "# Cross-Tier Validation Report",
            "\n## Overall Consistency",
            f"**Score**: {self.consistency_scores.get('overall', 0):.1%}\n"
        ]
        
        # Detailed scores
        lines.append("### Consistency by Aspect:")
        for aspect, score in self.consistency_scores.items():
            if aspect != 'overall':
                lines.append(f"- {aspect.capitalize()}: {score:.1%}")
        
        # Key findings
        lines.append("\n## Key Findings\n")
        
        # Integration consistency
        integration = self.validation_results.get('integration_consistency', {})
        if integration.get('measures'):
            lines.append("### Integration Measures:")
            for measure, value in integration['measures'].items():
                lines.append(f"- {measure}: {value:.4f}")
        
        # Commodity patterns
        commodity = self.validation_results.get('commodity_validation', {})
        rankings = commodity.get('commodity_rankings', {})
        if rankings:
            lines.append(f"\n### Commodity Pattern Validation:")
            lines.append(f"- Rank correlation: {rankings.get('rank_correlation', 0):.3f}")
            lines.append(f"- Consistent: {'Yes' if rankings.get('consistent') else 'No'}")
        
        # Critical issues
        summary = self.validation_results.get('summary', {})
        if summary.get('critical_issues'):
            lines.append("\n## Critical Issues\n")
            for issue in summary['critical_issues']:
                lines.append(f"- {issue}")
        
        # Recommendations
        if summary.get('recommendations'):
            lines.append("\n## Recommendations\n")
            for rec in summary['recommendations']:
                lines.append(f"- {rec}")
        
        return '\n'.join(lines)