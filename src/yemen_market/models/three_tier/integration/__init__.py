"""Integration layer for coordinating three-tier analysis.

This module provides the master coordinator that orchestrates all three
tiers of analysis and ensures consistent results across the methodology.

Components:
- ThreeTierAnalysis: Main orchestrator for running all tiers
- CrossTierValidator: Validates consistency across tier results
"""

from .three_tier_runner import ThreeTierAnalysis
from .cross_tier_validation import CrossTierValidator
from yemen_market.utils.logging import bind

# Set module context
bind(module=__name__)

__all__ = [
    'ThreeTierAnalysis',
    'CrossTierValidator'
]