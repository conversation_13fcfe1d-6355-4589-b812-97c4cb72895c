"""Base model class for three-tier econometric methodology.

Adapted from the original base.py to support 3D panel data structures
and the specific requirements of the three-tier approach.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import pickle
import json
from datetime import datetime

from yemen_market.utils.logging import (
    bind, timer, info, warning, error, log_metric, log_data_shape
)

# Set module context
bind(module=__name__)


class BaseModelConfig:
    """Base configuration class for three-tier models."""

    def __init__(self, **kwargs):
        """Initialize configuration with default values and custom parameters.

        Parameters
        ----------
        **kwargs
            Configuration parameters
        """
        # First, set class-level defaults for subclasses
        for attr_name in dir(self.__class__):
            if not attr_name.startswith('_') and not callable(getattr(self.__class__, attr_name)):
                default_value = getattr(self.__class__, attr_name)
                setattr(self, attr_name, default_value)

        # Default settings
        self.verbose: bool = kwargs.get('verbose', getattr(self, 'verbose', False))
        self.random_state: Optional[int] = kwargs.get('random_state', getattr(self, 'random_state', None))
        self.n_jobs: int = kwargs.get('n_jobs', getattr(self, 'n_jobs', 1))

        # Data handling
        self.handle_missing: str = kwargs.get('handle_missing', getattr(self, 'handle_missing', 'drop'))
        self.min_observations: int = kwargs.get('min_observations', getattr(self, 'min_observations', 30))

        # Estimation settings
        self.max_iterations: int = kwargs.get('max_iterations', getattr(self, 'max_iterations', 1000))
        self.tolerance: float = kwargs.get('tolerance', getattr(self, 'tolerance', 1e-6))

        # Output settings
        self.save_diagnostics: bool = kwargs.get('save_diagnostics', getattr(self, 'save_diagnostics', True))
        self.save_residuals: bool = kwargs.get('save_residuals', getattr(self, 'save_residuals', False))

        # Override with any provided kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        # Validate parameters
        self._validate()

    def _validate(self):
        """Validate configuration parameters."""
        if self.random_state is not None and not isinstance(self.random_state, int):
            raise ValueError("random_state must be an integer or None")

        if self.n_jobs < -1:
            raise ValueError("n_jobs must be >= -1")

        if self.handle_missing not in ['drop', 'interpolate', 'forward_fill']:
            raise ValueError("handle_missing must be one of: 'drop', 'interpolate', 'forward_fill'")

        if self.min_observations < 1:
            raise ValueError("min_observations must be >= 1")

        if self.max_iterations < 1:
            raise ValueError("max_iterations must be >= 1")

        if self.tolerance <= 0:
            raise ValueError("tolerance must be > 0")

    def __eq__(self, other):
        """Check equality with another config."""
        if not isinstance(other, BaseModelConfig):
            return False
        return self.__dict__ == other.__dict__

    def __repr__(self):
        """String representation."""
        return f"{self.__class__.__name__}({self.__dict__})"


class TierType(Enum):
    """Enumeration of analysis tiers."""
    TIER1_POOLED = "tier1_pooled_panel"
    TIER2_COMMODITY = "tier2_commodity_specific"
    TIER3_VALIDATION = "tier3_factor_validation"


@dataclass
class ThreeTierResults:
    """Container for results from three-tier analysis."""

    # Tier identification
    tier: TierType
    commodity: Optional[str] = None  # For Tier 2

    # Common elements
    coefficients: Dict[str, float] = field(default_factory=dict)
    standard_errors: Dict[str, float] = field(default_factory=dict)
    t_statistics: Dict[str, float] = field(default_factory=dict)
    p_values: Dict[str, float] = field(default_factory=dict)

    # Model diagnostics
    n_observations: int = 0
    n_entities: Optional[int] = None
    n_periods: Optional[int] = None
    r_squared: float = 0.0
    r_squared_within: Optional[float] = None
    r_squared_between: Optional[float] = None

    # Statistical tests
    f_statistic: Optional[float] = None
    f_p_value: Optional[float] = None
    hausman_statistic: Optional[float] = None
    hausman_p_value: Optional[float] = None

    # Tier 1 specific
    fixed_effects: Optional[Dict[str, np.ndarray]] = None
    clustered_se_type: Optional[str] = None

    # Tier 2 specific
    threshold_value: Optional[float] = None
    threshold_ci: Optional[Tuple[float, float]] = None
    regime_splits: Optional[Dict[str, int]] = None
    cointegration_rank: Optional[int] = None

    # Tier 3 specific
    n_factors: Optional[int] = None
    factor_loadings: Optional[np.ndarray] = None
    variance_explained: Optional[np.ndarray] = None
    conflict_correlation: Optional[float] = None

    # Metadata
    estimation_time: float = 0.0
    convergence_achieved: bool = True
    warnings: List[str] = field(default_factory=list)

    def summary(self) -> str:
        """Generate summary string of results."""
        lines = [
            f"\n{self.tier.value} Results",
            "=" * 50
        ]

        if self.commodity:
            lines.append(f"Commodity: {self.commodity}")

        lines.extend([
            f"Observations: {self.n_observations}",
            f"R-squared: {self.r_squared:.4f}"
        ])

        if self.r_squared_within is not None:
            lines.append(f"R-squared (within): {self.r_squared_within:.4f}")

        if self.coefficients:
            lines.extend(["\nCoefficients:", "-" * 20])
            for var, coef in self.coefficients.items():
                se = self.standard_errors.get(var, np.nan)
                t_stat = self.t_statistics.get(var, np.nan)
                p_val = self.p_values.get(var, np.nan)
                lines.append(
                    f"{var:20} {coef:10.4f} ({se:8.4f})  "
                    f"t={t_stat:6.2f}  p={p_val:6.4f}"
                )

        if self.warnings:
            lines.extend(["\nWarnings:", "-" * 20])
            lines.extend(self.warnings)

        return "\n".join(lines)


class BaseThreeTierModel(ABC):
    """Abstract base class for three-tier econometric models.

    This class provides the interface and common functionality for all
    models in the three-tier methodology.
    """

    def __init__(self, config: Optional[BaseModelConfig] = None):
        """Initialize base model.

        Parameters
        ----------
        config : BaseModelConfig, optional
            Model configuration parameters
        """
        self.config = config or BaseModelConfig()
        self.results: Optional[ThreeTierResults] = None
        self.data: Optional[pd.DataFrame] = None
        self.is_fitted = False

        # Model metadata
        self.metadata = {
            'created_at': datetime.now().isoformat(),
            'version': '1.0.0',
            'model_class': self.__class__.__name__
        }

        # Set up logging context
        bind(model=self.__class__.__name__)
        info(f"Initialized {self.__class__.__name__}")

    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate input data for model requirements.

        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate

        Returns
        -------
        tuple
            (is_valid, list_of_error_messages)
        """
        pass

    @abstractmethod
    def fit(self, data: pd.DataFrame, **kwargs):
        """Fit the model to data.

        Parameters
        ----------
        data : pd.DataFrame
            Panel data to fit
        **kwargs
            Additional model-specific parameters

        Returns
        -------
        ResultsContainer
            Model results
        """
        pass

    @abstractmethod
    def predict(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Generate predictions from fitted model.

        Parameters
        ----------
        data : pd.DataFrame
            Data for prediction
        **kwargs
            Additional prediction parameters

        Returns
        -------
        pd.DataFrame
            Model predictions
        """
        pass

    def get_results(self) -> ThreeTierResults:
        """Get model results.

        Returns
        -------
        ThreeTierResults
            Results container

        Raises
        ------
        ValueError
            If model is not fitted
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before getting results")
        return self.results

    def save_results(self, path: Union[str, Path]) -> None:
        """Save model results to file.

        Parameters
        ----------
        path : str or Path
            Path to save results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before saving results")

        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        # Convert results to dict for serialization
        results_dict = {
            'tier': self.results.tier.value,
            'commodity': self.results.commodity,
            'coefficients': self.results.coefficients,
            'standard_errors': self.results.standard_errors,
            'diagnostics': {
                'n_observations': self.results.n_observations,
                'r_squared': self.results.r_squared,
                'r_squared_within': self.results.r_squared_within,
            },
            'warnings': self.results.warnings
        }

        # Save based on file extension
        if path.suffix == '.json':
            import json
            with open(path, 'w') as f:
                json.dump(results_dict, f, indent=2)
        else:
            # Default to pickle for complex objects
            import pickle
            with open(path, 'wb') as f:
                pickle.dump(self.results, f)

        info(f"Saved results to {path}")

    def save(self, path: Union[str, Path]) -> None:
        """Save model to file.

        Parameters
        ----------
        path : str or Path
            Path to save model
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, 'wb') as f:
            pickle.dump(self, f)

        info(f"Saved model to {path}")

    @classmethod
    def load(cls, path: Union[str, Path]):
        """Load model from file.

        Parameters
        ----------
        path : str or Path
            Path to load model from

        Returns
        -------
        BaseThreeTierModel
            Loaded model instance
        """
        with open(path, 'rb') as f:
            model = pickle.load(f)

        info(f"Loaded model from {path}")
        return model

    def summary(self, results) -> Dict[str, Any]:
        """Generate model summary.

        Parameters
        ----------
        results : ResultsContainer
            Model results

        Returns
        -------
        dict
            Summary information
        """
        return {
            'model_type': self.__class__.__name__,
            'config': self.config,
            'metadata': self.metadata,
            'results_summary': results.summary() if hasattr(results, 'summary') else {}
        }

    def clone(self):
        """Create a clone of this model with same configuration.

        Returns
        -------
        BaseThreeTierModel
            Cloned model instance
        """
        return self.__class__(self.config)

    def fit_batch(self, datasets: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Fit model to multiple datasets.

        Parameters
        ----------
        datasets : dict
            Dictionary of dataset_name -> DataFrame

        Returns
        -------
        dict
            Dictionary of dataset_name -> results
        """
        results = {}

        for name, data in datasets.items():
            info(f"Fitting model for dataset: {name}")
            try:
                result = self.fit(data, commodity=name)
                results[name] = result
            except Exception as e:
                warning(f"Failed to fit model for {name}: {e}")
                results[name] = None

        return results

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        pass

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"{self.__class__.__name__}(config={self.config})"

    def _log_estimation_start(self, data: pd.DataFrame) -> None:
        """Log information at start of estimation."""
        log_data_shape("input_data", data)
        info("Starting estimation")

        # Log configuration
        if self.config:
            info(f"Configuration: {self.config}")

    def _log_estimation_complete(self, elapsed_time: float) -> None:
        """Log information after estimation completes."""
        info(f"Estimation complete in {elapsed_time:.2f} seconds")