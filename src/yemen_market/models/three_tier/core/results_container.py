"""Standardized results containers for three-tier methodology.

This module provides consistent result structures across all tiers,
ensuring uniform output format and easy comparison between models.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union, Any
import pandas as pd
import numpy as np
from pathlib import Path
import json

from yemen_market.utils.logging import info, bind

# Set module context
bind(module=__name__)


@dataclass
class ResultsSummaryStats:
    """Summary statistics for model results."""
    mean: float
    std: float
    min: float
    max: float
    q25: float
    q50: float
    q75: float

    @classmethod
    def from_series(cls, series: pd.Series) -> 'ResultsSummaryStats':
        """Create from pandas Series."""
        return cls(
            mean=series.mean(),
            std=series.std(),
            min=series.min(),
            max=series.max(),
            q25=series.quantile(0.25),
            q50=series.quantile(0.50),
            q75=series.quantile(0.75)
        )


@dataclass
class DiagnosticTests:
    """Container for diagnostic test results."""
    # Residual tests
    jarque_bera: Optional[Tuple[float, float]] = None  # statistic, p-value
    breusch_pagan: Optional[Tuple[float, float]] = None
    durbin_watson: Optional[float] = None
    ljung_box: Optional[Tuple[float, float]] = None

    # Model specification tests
    reset_test: Optional[Tuple[float, float]] = None
    hausman_test: Optional[Tuple[float, float]] = None

    # Panel-specific tests
    pesaran_cd: Optional[Tuple[float, float]] = None  # cross-sectional dependence
    wooldridge_ar1: Optional[Tuple[float, float]] = None  # serial correlation

    def to_dataframe(self) -> pd.DataFrame:
        """Convert diagnostic tests to DataFrame."""
        tests = []

        if self.jarque_bera:
            tests.append(('Jarque-Bera', self.jarque_bera[0], self.jarque_bera[1]))
        if self.breusch_pagan:
            tests.append(('Breusch-Pagan', self.breusch_pagan[0], self.breusch_pagan[1]))
        if self.durbin_watson:
            tests.append(('Durbin-Watson', self.durbin_watson, None))
        if self.ljung_box:
            tests.append(('Ljung-Box', self.ljung_box[0], self.ljung_box[1]))
        if self.reset_test:
            tests.append(('RESET', self.reset_test[0], self.reset_test[1]))
        if self.hausman_test:
            tests.append(('Hausman', self.hausman_test[0], self.hausman_test[1]))
        if self.pesaran_cd:
            tests.append(('Pesaran CD', self.pesaran_cd[0], self.pesaran_cd[1]))
        if self.wooldridge_ar1:
            tests.append(('Wooldridge AR(1)', self.wooldridge_ar1[0], self.wooldridge_ar1[1]))

        return pd.DataFrame(tests, columns=['Test', 'Statistic', 'P-value'])


@dataclass
class ModelComparison:
    """Container for model comparison metrics."""
    aic: float
    bic: float
    log_likelihood: float
    n_parameters: int

    # Out-of-sample performance
    rmse: Optional[float] = None
    mae: Optional[float] = None
    mape: Optional[float] = None

    # In-sample fit
    r_squared: float = 0.0
    adj_r_squared: float = 0.0

    def to_series(self) -> pd.Series:
        """Convert to pandas Series for easy comparison."""
        return pd.Series({
            'AIC': self.aic,
            'BIC': self.bic,
            'Log-Likelihood': self.log_likelihood,
            'Parameters': self.n_parameters,
            'R-squared': self.r_squared,
            'Adj. R-squared': self.adj_r_squared,
            'RMSE': self.rmse,
            'MAE': self.mae,
            'MAPE': self.mape
        })


class ResultsContainer:
    """Unified container for all three-tier results.

    This class provides a consistent interface for storing and accessing
    results from any tier of the analysis.
    """

    def __init__(self, commodity: str, model_type: str, results: Dict[str, Any],
                 diagnostics: Optional[Dict[str, Any]] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        """Initialize results container.

        Parameters
        ----------
        commodity : str
            Commodity name
        model_type : str
            Specific model type
        results : dict
            Model results dictionary
        diagnostics : dict, optional
            Diagnostic test results
        metadata : dict, optional
            Additional metadata
        """
        # Validate inputs
        if not isinstance(commodity, str):
            raise TypeError("commodity must be a string")
        if not isinstance(results, dict):
            raise TypeError("results must be a dictionary")

        self.commodity = commodity
        self.model_type = model_type
        self.results = results
        self.diagnostics = diagnostics or {}
        self.metadata = metadata or {}
        self.timestamp = pd.Timestamp.now()

        # Extract common result components for easy access
        self._extract_result_components()

    def _extract_result_components(self):
        """Extract common components from results dictionary."""
        # Extract coefficients
        self.coefficients = self.results.get('coefficients', {})

        # Extract standard errors
        self.standard_errors = self.results.get('standard_errors', {})

        # Extract fit statistics
        self.fit_statistics = self.results.get('fit_statistics', {})

        # Extract p-values if available
        self.p_values = self.results.get('p_values', {})

    def get_coefficient(self, name: str, default: Optional[float] = None) -> Optional[float]:
        """Get coefficient value.

        Parameters
        ----------
        name : str
            Variable name
        default : float, optional
            Default value if not found

        Returns
        -------
        float or None
            Coefficient value
        """
        return self.coefficients.get(name, default)

    def get_standard_error(self, name: str, default: Optional[float] = None) -> Optional[float]:
        """Get standard error.

        Parameters
        ----------
        name : str
            Variable name
        default : float, optional
            Default value if not found

        Returns
        -------
        float or None
            Standard error
        """
        return self.standard_errors.get(name, default)

    def get_t_statistic(self, name: str) -> Optional[float]:
        """Calculate t-statistic.

        Parameters
        ----------
        name : str
            Variable name

        Returns
        -------
        float or None
            t-statistic
        """
        coef = self.get_coefficient(name)
        se = self.get_standard_error(name)

        if coef is not None and se is not None and se != 0:
            return coef / se
        return None

    def get_p_value(self, name: str) -> Optional[float]:
        """Get or calculate p-value.

        Parameters
        ----------
        name : str
            Variable name

        Returns
        -------
        float or None
            p-value
        """
        # First check if p-value is directly available
        if name in self.p_values:
            return self.p_values[name]

        # Calculate from t-statistic
        t_stat = self.get_t_statistic(name)
        if t_stat is not None:
            # Get degrees of freedom from metadata
            df = self.metadata.get('degrees_of_freedom', 1000)  # Default fallback

            # Two-tailed p-value using normal approximation for large df
            from scipy import stats
            return 2 * (1 - stats.norm.cdf(abs(t_stat)))
        return None



    def summary_dict(self) -> Dict[str, Any]:
        """Generate summary dictionary.

        Returns
        -------
        dict
            Summary information
        """
        # Extract key results
        key_results = {}
        if 'r_squared' in self.fit_statistics:
            key_results['r_squared'] = self.fit_statistics['r_squared']
        if 'adj_r_squared' in self.fit_statistics:
            key_results['adj_r_squared'] = self.fit_statistics['adj_r_squared']
        if 'nobs' in self.fit_statistics:
            key_results['n_observations'] = self.fit_statistics['nobs']
        elif 'n_observations' in self.fit_statistics:
            key_results['n_observations'] = self.fit_statistics['n_observations']

        # Check diagnostic tests
        diagnostics_passed = {}
        for test_name, test_result in self.diagnostics.items():
            if isinstance(test_result, dict) and 'passed' in test_result:
                diagnostics_passed[test_name.replace('_test', '')] = test_result['passed']

        return {
            'commodity': self.commodity,
            'model_type': self.model_type,
            'key_results': key_results,
            'diagnostics_passed': diagnostics_passed,
            'timestamp': self.timestamp.isoformat()
        }

    def filter_significant(self, alpha: float = 0.05) -> Dict[str, float]:
        """Filter significant coefficients.

        Parameters
        ----------
        alpha : float
            Significance level

        Returns
        -------
        dict
            Significant coefficients
        """
        significant = {}
        for name in self.coefficients:
            p_val = self.get_p_value(name)
            if p_val is not None and p_val < alpha:
                significant[name] = self.coefficients[name]
        return significant

    def format_table(self) -> pd.DataFrame:
        """Format results as a table.

        Returns
        -------
        pd.DataFrame
            Formatted results table
        """
        data = []
        for name in self.coefficients:
            coef = self.get_coefficient(name)
            se = self.get_standard_error(name)
            t_stat = self.get_t_statistic(name)
            p_val = self.get_p_value(name)

            data.append({
                'Variable': name,
                'Coefficient': coef,
                'Std. Error': se,
                't-statistic': t_stat,
                'P-value': p_val
            })

        return pd.DataFrame(data).set_index('Variable')

    def to_dict(self) -> Dict[str, Any]:
        """Convert results to dictionary."""
        return {
            'commodity': self.commodity,
            'model_type': self.model_type,
            'timestamp': self.timestamp.isoformat(),
            'results': self.results,
            'diagnostics': self.diagnostics,
            'metadata': self.metadata
        }

    def to_json(self) -> str:
        """Convert to JSON string.

        Returns
        -------
        str
            JSON representation
        """
        return json.dumps(self.to_dict(), indent=2, default=str)

    def to_latex(self) -> str:
        """Export to LaTeX table format.

        Returns
        -------
        str
            LaTeX table
        """
        df = self.format_table()
        return df.to_latex(escape=True, float_format='%.3f')

    def get_nested(self, path: str, default: Any = None) -> Any:
        """Get nested value from results using dot notation.

        Parameters
        ----------
        path : str
            Dot-separated path (e.g., 'regime1.coefficients.var1')
        default : Any
            Default value if path not found

        Returns
        -------
        Any
            Value at path or default
        """
        try:
            value = self.results
            for key in path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def compute_summary_stats(self) -> Dict[str, Any]:
        """Compute summary statistics.

        Returns
        -------
        dict
            Summary statistics
        """
        return {
            'n_coefficients': len(self.coefficients),
            'mean_coefficient': np.mean(list(self.coefficients.values())) if self.coefficients else 0,
            'max_coefficient': max(self.coefficients.values()) if self.coefficients else 0,
            'min_coefficient': min(self.coefficients.values()) if self.coefficients else 0
        }

    def save(self, path: Union[str, Path]) -> None:
        """Save results to file.

        Parameters
        ----------
        path : str or Path
            Output file path
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        if path.suffix == '.json':
            with open(path, 'w') as f:
                json.dump(self.to_dict(), f, indent=2, default=str)
        else:
            # Use pickle for complex objects
            import pickle
            with open(path, 'wb') as f:
                pickle.dump(self, f)

        info(f"Results saved to {path}")

    @classmethod
    def load(cls, path: Union[str, Path]) -> 'ResultsContainer':
        """Load results from file.

        Parameters
        ----------
        path : str or Path
            Input file path

        Returns
        -------
        ResultsContainer
            Loaded results container
        """
        path = Path(path)

        if path.suffix == '.json':
            with open(path, 'r') as f:
                data = json.load(f)
            return cls(
                commodity=data['commodity'],
                model_type=data['model_type'],
                results=data['results'],
                diagnostics=data.get('diagnostics', {}),
                metadata=data.get('metadata', {})
            )
        else:
            # Use pickle
            import pickle
            with open(path, 'rb') as f:
                return pickle.load(f)

    @classmethod
    def merge(cls, containers: List['ResultsContainer'], commodity: str,
              model_type: str) -> 'ResultsContainer':
        """Merge multiple containers.

        Parameters
        ----------
        containers : list
            List of ResultsContainer objects
        commodity : str
            Commodity for merged container
        model_type : str
            Model type for merged container

        Returns
        -------
        ResultsContainer
            Merged container
        """
        merged_results = {}
        merged_diagnostics = {}
        merged_metadata = {}

        for i, container in enumerate(containers):
            merged_results[container.model_type] = container.results
            merged_diagnostics[container.model_type] = container.diagnostics
            merged_metadata[container.model_type] = container.metadata

        return cls(
            commodity=commodity,
            model_type=model_type,
            results=merged_results,
            diagnostics=merged_diagnostics,
            metadata=merged_metadata
        )

    @classmethod
    def compare(cls, containers: List['ResultsContainer']) -> Dict[str, Any]:
        """Compare multiple containers.

        Parameters
        ----------
        containers : list
            List of ResultsContainer objects to compare

        Returns
        -------
        dict
            Comparison results
        """
        comparison = {
            'r_squared_values': {},
            'aic_values': {},
            'best_r_squared': None,
            'best_aic': None
        }

        best_r2 = -float('inf')
        best_aic = float('inf')

        for container in containers:
            model_name = container.model_type

            # R-squared comparison
            r2 = container.fit_statistics.get('r_squared', 0)
            comparison['r_squared_values'][model_name] = r2
            if r2 > best_r2:
                best_r2 = r2
                comparison['best_r_squared'] = model_name

            # AIC comparison (lower is better)
            aic = container.fit_statistics.get('aic', float('inf'))
            comparison['aic_values'][model_name] = aic
            if aic < best_aic:
                best_aic = aic
                comparison['best_aic'] = model_name

        return comparison