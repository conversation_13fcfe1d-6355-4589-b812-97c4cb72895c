"""Migration utilities for transitioning to three-tier methodology.

This module helps transition from the old dual-track approach to the new
three-tier methodology, providing tools for:
- Configuration migration
- Results conversion
- Script updates
- Validation and comparison
"""

from .model_migration import ModelMigrationHelper, compare_methodologies

__all__ = [
    'ModelMigrationHelper',
    'compare_methodologies'
]