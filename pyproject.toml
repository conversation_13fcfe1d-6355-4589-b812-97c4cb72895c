[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "yemen-market-integration"
version = "1.0.0"
description = "Econometric analysis of market integration in conflict-affected Yemen"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.10",
    "Topic :: Scientific/Engineering :: Economic Analysis",
]

dependencies = [
    # Core data packages
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    "pyarrow>=12.0.0",
    
    # Statistical modeling
    "statsmodels>=0.14.0",
    "linearmodels>=5.0",
    "arch>=6.0",
    "scikit-learn>=1.3.0",
    
    # Bayesian modeling
    "pymc>=5.10.0",
    "arviz>=0.17.0",
    "pytensor>=2.18.0",
    
    # Geospatial packages
    "geopandas>=0.13.0",
    "shapely>=2.0.0",
    "fiona>=1.9.0",
    "pyproj>=3.5.0",
    "libpysal>=4.7.0",
    "spreg>=1.3.0",
    
    # Visualization
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.14.0",
    
    # Data access
    "hdx-python-api>=6.0.0",
    "requests>=2.28.0",
    "python-dotenv>=1.0.0",
    
    # Analysis tools
    "networkx>=3.1",
    "shap>=0.42.0",
    "joblib>=1.3.0",
    
    # Logging dependencies
    "loguru>=0.7.0",
    "structlog>=24.0.0",
    "rich>=13.0.0",
    "python-json-logger>=2.0.0",
    
    # Jupyter
    "jupyter>=1.0.0",
    "ipykernel>=6.25.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.3.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.3.0",
    "ruff>=0.0.270",
    "mypy>=1.3.0",
    "pre-commit>=3.3.0",
    "jupyter>=1.0.0",
    "nbconvert>=7.4.0",
    "sphinx>=6.2.0",
]

experiment = [
    "mlflow>=2.10.0",
    "neptune>=1.8.0",
    "wandb>=0.16.0",
]

ml = [
    "tensorflow>=2.12.0",
    "torch>=2.0.0",
]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.ruff]
select = ["E", "F", "I", "N", "UP", "PL", "NPY"]
line-length = 88
target-version = "py310"

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
# addopts = "-v --cov=yemen_market --cov-report=html"
