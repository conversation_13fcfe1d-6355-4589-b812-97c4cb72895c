#!/usr/bin/env python3
"""
Generate Claude command files dynamically from project state.
This script extracts current project information and generates
up-to-date command files for Claude instances.
"""

import json
import os
import re
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

# Project root
PROJECT_ROOT = Path(__file__).parent.parent.parent


def get_git_info() -> Dict[str, any]:
    """Extract git information."""
    try:
        # Get current branch
        branch = subprocess.check_output(
            ["git", "rev-parse", "--abbrev-ref", "HEAD"],
            cwd=PROJECT_ROOT,
            text=True
        ).strip()
        
        # Get recent commits
        commits = subprocess.check_output(
            ["git", "log", "--oneline", "-5"],
            cwd=PROJECT_ROOT,
            text=True
        ).strip().split('\n')
        
        # Get last commit date
        last_commit_date = subprocess.check_output(
            ["git", "log", "-1", "--format=%cd", "--date=short"],
            cwd=PROJECT_ROOT,
            text=True
        ).strip()
        
        return {
            "branch": branch,
            "recent_commits": commits[:3],  # Top 3
            "last_commit_date": last_commit_date
        }
    except Exception:
        return {
            "branch": "main",
            "recent_commits": ["Unable to fetch commits"],
            "last_commit_date": "Unknown"
        }


def get_progress_info() -> Dict[str, any]:
    """Extract progress information from reports/progress/README.md."""
    progress_file = PROJECT_ROOT / "reports" / "progress" / "README.md"
    
    if not progress_file.exists():
        return {
            "percentage": "Unknown",
            "current_phase": "Unknown",
            "completed_items": [],
            "next_steps": []
        }
    
    content = progress_file.read_text()
    
    # Extract percentage
    percentage_match = re.search(r'(\d+)%\s+[Cc]omplete', content)
    percentage = percentage_match.group(1) if percentage_match else "Unknown"
    
    # Extract current phase
    phase_match = re.search(r'Current Phase[:\s]+([^\n]+)', content, re.IGNORECASE)
    current_phase = phase_match.group(1).strip() if phase_match else "Implementation"
    
    # Extract completed items (look for checkmarks)
    completed = re.findall(r'✅\s+([^\n]+)', content)
    
    # Extract next steps
    next_section = re.search(r'Next Steps.*?$(.*?)^#', content, re.MULTILINE | re.DOTALL | re.IGNORECASE)
    next_steps = []
    if next_section:
        next_steps = [line.strip().lstrip('- ').lstrip('* ').lstrip('1234567890. ')
                     for line in next_section.group(1).split('\n')
                     if line.strip() and not line.strip().startswith('#')][:5]
    
    return {
        "percentage": percentage,
        "current_phase": current_phase,
        "completed_items": completed[:3],  # Top 3
        "next_steps": next_steps[:5]  # Top 5
    }


def get_test_coverage() -> Dict[str, any]:
    """Extract test coverage information."""
    test_dir = PROJECT_ROOT / "tests"
    
    # Count test files
    test_files = list(test_dir.rglob("test_*.py"))
    test_count = len(test_files)
    
    # Count test lines (approximate)
    total_lines = 0
    for file in test_files:
        try:
            total_lines += len(file.read_text().splitlines())
        except Exception:
            pass
    
    # Check for coverage report
    coverage_file = PROJECT_ROOT / ".coverage"
    coverage_html = PROJECT_ROOT / "htmlcov" / "index.html"
    
    coverage_info = {
        "file_count": test_count,
        "line_count": total_lines,
        "percentage": "Run 'make test' to generate",
        "has_report": coverage_html.exists()
    }
    
    # Try to extract coverage percentage from HTML report
    if coverage_html.exists():
        try:
            html_content = coverage_html.read_text()
            match = re.search(r'pc_cov">(\d+)%</span>', html_content)
            if match:
                coverage_info["percentage"] = f"{match.group(1)}%"
        except Exception:
            pass
    
    return coverage_info


def get_model_implementation_status() -> Dict[str, bool]:
    """Check implementation status of three-tier models."""
    models_dir = PROJECT_ROOT / "src" / "yemen_market" / "models" / "three_tier"
    
    status = {}
    
    # Check Tier 1
    tier1_dir = models_dir / "tier1_pooled"
    status["tier1"] = (tier1_dir / "pooled_panel_model.py").exists()
    
    # Check Tier 2
    tier2_dir = models_dir / "tier2_commodity"
    status["tier2"] = (tier2_dir / "commodity_specific_model.py").exists()
    
    # Check Tier 3
    tier3_dir = models_dir / "tier3_validation"
    status["tier3"] = (tier3_dir / "pca_analysis.py").exists()
    
    # Check integration
    status["integration"] = (models_dir / "integration" / "three_tier_runner.py").exists()
    
    return status


def get_active_context() -> Dict[str, any]:
    """Extract information from .claude/ACTIVE_CONTEXT.md."""
    context_file = PROJECT_ROOT / ".claude" / "ACTIVE_CONTEXT.md"
    
    if not context_file.exists():
        return {
            "current_sprint": "Unknown",
            "focus_areas": [],
            "to_resume": "Check .claude/ACTIVE_CONTEXT.md"
        }
    
    content = context_file.read_text()
    
    # Extract current sprint
    sprint_match = re.search(r'Current Sprint[:\s]+([^\n]+)', content, re.IGNORECASE)
    current_sprint = sprint_match.group(1).strip() if sprint_match else "Unknown"
    
    # Extract focus areas
    focus_section = re.search(r'Focus Areas.*?$(.*?)^#', content, re.MULTILINE | re.DOTALL | re.IGNORECASE)
    focus_areas = []
    if focus_section:
        focus_areas = [line.strip().lstrip('- ').lstrip('* ')
                      for line in focus_section.group(1).split('\n')
                      if line.strip()][:3]
    
    return {
        "current_sprint": current_sprint,
        "focus_areas": focus_areas,
        "to_resume": "See .claude/ACTIVE_CONTEXT.md for details"
    }


def generate_unified_commands() -> str:
    """Generate the unified Claude commands file."""
    # Gather all dynamic information
    git_info = get_git_info()
    progress = get_progress_info()
    coverage = get_test_coverage()
    models = get_model_implementation_status()
    context = get_active_context()
    
    # Calculate overall implementation percentage
    impl_count = sum(1 for v in models.values() if v)
    impl_percentage = (impl_count / len(models)) * 100 if models else 0
    
    # Generate timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Build the unified commands file
    content = f"""# Claude Commands - Yemen Market Integration
# Auto-generated: {timestamp}
# Run 'make update-claude-commands' to refresh

## 🚀 Initialize New Claude Session

Copy and paste this entire block to initialize a new Claude session:

```
Initialize Yemen Market Integration project context:

PROJECT: Econometric analysis of market integration in conflict-affected Yemen
PURPOSE: Analyze how dual exchange rates and territorial fragmentation affect commodity prices
STATUS: {progress['percentage']}% complete - {progress['current_phase']}
APPROACH: Three-tier models - (1) Pooled panel regression, (2) Commodity-specific VECM, (3) Factor analysis

IMPLEMENTATION STATUS:
{"✓" if models['tier1'] else "○"} Tier 1: Pooled Panel Model
{"✓" if models['tier2'] else "○"} Tier 2: Commodity-Specific Model  
{"✓" if models['tier3'] else "○"} Tier 3: Validation/Factor Analysis
{"✓" if models['integration'] else "○"} Integration: Three-Tier Runner

TEST COVERAGE: {coverage['file_count']} test files, {coverage['line_count']:,} lines of test code
Coverage: {coverage['percentage']} {"(report available)" if coverage['has_report'] else "(run 'make test' to generate)"}

RECENT PROGRESS:
{chr(10).join(f"- {item}" for item in progress['completed_items'][:3]) if progress['completed_items'] else "- Check reports/progress/README.md"}

NEXT STEPS:
{chr(10).join(f"{i+1}. {step}" for i, step in enumerate(progress['next_steps'][:5])) if progress['next_steps'] else "1. Check reports/progress/README.md"}

CRITICAL DEVELOPMENT RULES:
1. ✓ Use enhanced logging from yemen_market.utils.logging (NOT print/logging)
2. ✓ Never create temporary files - only production-ready code
3. ✓ Complete all steps - no shortcuts or sampling
4. ✓ Maintain 100% test coverage
5. ✓ Follow documentation hierarchy strictly

CURRENT FOCUS: {context['current_sprint']}
GIT: Branch '{git_info['branch']}', Last commit: {git_info['last_commit_date']}

KEY COMMANDS:
make week5-models     # Run three-tier analysis
make test            # Run all tests with coverage
make lint            # Check code style
make update-claude-commands  # Refresh this file

IMPORT PATTERN:
from yemen_market.utils.logging import info, timer, progress, log_data_shape
from yemen_market.models.three_tier import ThreeTierRunner
from yemen_market.data import PanelBuilder, WFPProcessor
```

## 📋 Quick Reference Card

### Project Status
- **Progress**: {progress['percentage']}% complete
- **Phase**: {progress['current_phase']}
- **Models**: {impl_count}/{len(models)} implemented ({impl_percentage:.0f}%)
- **Tests**: {coverage['file_count']} files, {coverage['line_count']:,} lines
- **Coverage**: {coverage['percentage']}

### Essential Rules
```
ALWAYS: Enhanced logging | NO: Temp files | COMPLETE: All steps | TARGET: 100% coverage
```

### File Locations
```
Progress → reports/progress/README.md
Active → .claude/ACTIVE_CONTEXT.md  
Rules → CLAUDE.md
Methods → METHODOLOGY.md
Models → src/yemen_market/models/three_tier/
Tests → tests/unit/models/three_tier/
```

### Three-Tier Architecture
```
Tier 1: Pooled Panel (linearmodels.PanelOLS) → Average effects
Tier 2: Commodity VECM (threshold models) → Market-specific dynamics  
Tier 3: PCA/Factors (validation) → Latent patterns
```

### Enhanced Logging Pattern
```python
from yemen_market.utils.logging import info, timer, progress, bind

# Set context
bind(module=__name__)

# Time operations
with timer("operation_name"):
    # Show progress for loops
    with progress("Processing items", total=len(items)) as update:
        for item in items:
            # Your code here
            info("Processed", item=item.name, result=result)
            update(1)
```

## 🔧 Common Tasks

### Running Analysis
```bash
# Full three-tier analysis
make week5-models

# Quick model tests only
make test-models-quick

# Full test suite with coverage
make test

# Update this command file
make update-claude-commands
```

### Working with Data
```python
# Build panel dataset
from yemen_market.data import PanelBuilder
builder = PanelBuilder()
panel = builder.build_panel_dataset(
    start_date="2019-01-01",
    end_date="2024-12-31",
    commodities=["wheat", "rice", "sugar", "fuel"]
)

# Always log data shapes
from yemen_market.utils.logging import log_data_shape
log_data_shape("panel_data", panel)
```

### Testing Patterns
```python
# Tests define the contract - implement exactly what they expect
# Example: If test expects Tuple[bool, List[str]], return exactly that

# Run specific test
pytest tests/unit/models/three_tier/tier1_pooled/test_pooled_panel_model.py -xvs

# Check coverage for module
pytest --cov=yemen_market.models.three_tier --cov-report=term-missing
```

## 📊 Current Git Status
**Branch**: {git_info['branch']}  
**Recent Commits**:
{chr(10).join(f"- {commit}" for commit in git_info['recent_commits'][:3])}

## 🎯 Session Checklist

Before starting work:
- [ ] Reviewed this initialization ({progress['percentage']}% complete)
- [ ] Understand three-tier methodology
- [ ] Clear on enhanced logging requirement
- [ ] Know the no-temp-files rule
- [ ] Checked current focus: {context['current_sprint']}

## 💡 Remember

This project aims to inform World Bank policy for humanitarian interventions in Yemen. 
Every line of code should be production-ready, tested, and purposeful.

---
*Generated automatically by scripts/utilities/generate_claude_commands.py*
*Last updated: {timestamp}*
"""
    
    return content


def main():
    """Generate the Claude commands file."""
    # Ensure .claude/commands directory exists
    commands_dir = PROJECT_ROOT / ".claude" / "commands"
    commands_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate unified commands
    content = generate_unified_commands()
    
    # Write to single unified file
    output_file = commands_dir / "project_context.md"
    output_file.write_text(content)
    
    # Remove old separate files if they exist
    old_files = [
        "refresh_project_context.md",
        "quick_reference.md",
        "init_claude_session.md"
    ]
    
    for old_file in old_files:
        old_path = commands_dir / old_file
        if old_path.exists():
            old_path.unlink()
            print(f"Removed old file: {old_file}")
    
    print(f"✓ Generated unified Claude commands: {output_file}")
    print(f"  - Progress: {get_progress_info()['percentage']}% complete")
    print(f"  - Tests: {get_test_coverage()['file_count']} files")
    print(f"  - Branch: {get_git_info()['branch']}")
    print("\nRun 'make update-claude-commands' to refresh anytime.")


if __name__ == "__main__":
    main()