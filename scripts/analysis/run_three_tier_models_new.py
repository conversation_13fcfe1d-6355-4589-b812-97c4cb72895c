#!/usr/bin/env python3
"""
Run three-tier econometric analysis for Yemen market integration.

This script uses the NEW three-tier methodology implementation:
- Tier 1: Pooled panel regression with multi-way fixed effects
- Tier 2: Commodity-specific threshold VECMs  
- Tier 3: Factor analysis and conflict validation

This replaces the old run_three_tier_models.py script.
"""

import sys
from pathlib import Path
import numpy as np
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from yemen_market.utils.logging import (
    setup_logging, info, error, warning, timer, progress, 
    log_metric, log_data_shape, bind
)
from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier.integration import ThreeTierAnalysis
from yemen_market.models.three_tier.migration import ModelMigrationHelper
from yemen_market.visualization import create_figure_directory


def load_panel_data():
    """Load integrated panel data."""
    bind(module="data_loading")
    
    with timer("load_panel_data"):
        panel_path = Path("data/processed/panels/integrated_panel.parquet")
        
        if not panel_path.exists():
            error(f"Panel data not found at {panel_path}")
            raise FileNotFoundError(f"Run build_panel_datasets.py first")
        
        df = pd.read_parquet(panel_path)
        log_data_shape("integrated_panel", df)
        
        # Rename columns to match new methodology expectations
        column_mapping = {
            'market': 'governorate',
            'price_usd': 'usd_price'
        }
        
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df[new_col] = df[old_col]
                info(f"Renamed column: {old_col} → {new_col}")
        
        # Validate structure
        required_cols = ['date', 'governorate', 'commodity', 'usd_price']
        missing = set(required_cols) - set(df.columns)
        if missing:
            error(f"Missing required columns: {missing}")
            raise ValueError(f"Panel missing required columns: {missing}")
        
        return df


def load_conflict_data():
    """Load conflict data if available."""
    bind(module="conflict_loading")
    
    conflict_path = Path("data/processed/conflict/acled_processed.parquet")
    
    if not conflict_path.exists():
        warning("Conflict data not found. Tier 3 conflict validation will be skipped.")
        return None
    
    try:
        conflict_df = pd.read_parquet(conflict_path)
        log_data_shape("conflict_data", conflict_df)
        
        # Ensure required columns
        required = ['date', 'governorate', 'fatalities']
        if all(col in conflict_df.columns for col in required):
            return conflict_df
        else:
            warning("Conflict data missing required columns")
            return None
            
    except Exception as e:
        warning(f"Failed to load conflict data: {str(e)}")
        return None


def configure_analysis(old_config=None):
    """Configure three-tier analysis."""
    bind(module="configuration")
    
    if old_config:
        # Migrate old configuration
        info("Migrating old configuration to three-tier format")
        migrator = ModelMigrationHelper()
        config = migrator.migrate_configuration(old_config)
    else:
        # Default configuration
        config = {
            'tier1_config': {
                'fixed_effects': ['entity', 'time'],
                'cluster_var': 'entity',
                'driscoll_kraay': True
            },
            'tier2_config': {
                'min_observations': 50,
                'test_thresholds': True,
                'max_lags': 4,
                'threshold_var': 'conflict_intensity'
            },
            'tier3_config': {
                'n_factors': 3,
                'standardize': True,
                'conflict_validation': True,
                'conflict_threshold': 10
            },
            'output_dir': 'results/three_tier_analysis_new',
            'run_parallel': False
        }
    
    info("Configuration set for three-tier analysis")
    return config


def run_analysis(df, conflict_df=None, config=None):
    """Run complete three-tier analysis using new methodology."""
    bind(module="three_tier_analysis")
    
    info("="*60)
    info("Three-Tier Econometric Analysis (New Methodology)")
    info("="*60)
    
    with timer("complete_three_tier_analysis"):
        # Initialize analysis
        analysis = ThreeTierAnalysis(config)
        
        # Run all tiers
        results = analysis.run_full_analysis(df, conflict_data=conflict_df)
        
        # Log key metrics from each tier
        if results['tier1'] and hasattr(results['tier1'], 'comparison_metrics'):
            log_metric("tier1_r_squared", results['tier1'].comparison_metrics.r_squared)
            info(f"Tier 1 R-squared: {results['tier1'].comparison_metrics.r_squared:.4f}")
        
        # Tier 2 summary
        if results['tier2']:
            n_commodities = len(results['tier2'])
            n_threshold = sum(1 for r in results['tier2'].values() 
                            if isinstance(r, dict) and 'threshold_value' in r)
            info(f"Tier 2: Analyzed {n_commodities} commodities, {n_threshold} with thresholds")
        
        # Tier 3 summary
        if results['tier3']:
            if 'static_factors' in results['tier3']:
                factor_results = results['tier3']['static_factors']
                if hasattr(factor_results, 'tier_specific'):
                    variance = factor_results.tier_specific.get('cumulative_variance', [])
                    if len(variance) >= 3:
                        info(f"Tier 3: First 3 factors explain {variance[2]:.1%} of variance")
        
        # Cross-validation summary
        if 'cross_validation' in results:
            cv = results['cross_validation']
            if 'integration_consistency' in cv:
                consistency = cv['integration_consistency'].get('consistent', False)
                info(f"Cross-tier consistency: {'✓' if consistency else '✗'}")
        
        return results


def compare_with_old_methodology(df, results_new):
    """Compare new results with old methodology if available."""
    bind(module="methodology_comparison")
    
    info("\n" + "="*40)
    info("Methodology Comparison")
    info("="*40)
    
    try:
        # Try to load old results if they exist
        old_results_path = Path("results/three_tier_analysis/three_tier_results_summary.json")
        
        if old_results_path.exists():
            old_results = pd.read_json(old_results_path, typ='series')
            
            info("Comparing with previous results:")
            
            # Compare Tier 1
            if 'tier1' in old_results and results_new.get('tier1'):
                old_coef = old_results['tier1'].get('conflict_coefficient')
                # Extract new coefficient if available
                new_results_tier1 = results_new['tier1']
                if hasattr(new_results_tier1, 'coefficients'):
                    new_coef = new_results_tier1.coefficients.get('conflict_intensity', None)
                    if old_coef and new_coef:
                        diff_pct = abs(new_coef - old_coef) / abs(old_coef) * 100
                        info(f"Tier 1 conflict coefficient difference: {diff_pct:.1f}%")
            
            # Compare Tier 3
            if 'tier3' in old_results and results_new.get('tier3'):
                old_var = old_results['tier3'].get('variance_factor1')
                new_var = None
                
                if 'static_factors' in results_new['tier3']:
                    factor_results = results_new['tier3']['static_factors']
                    if hasattr(factor_results, 'tier_specific'):
                        variance = factor_results.tier_specific.get('variance_explained', [])
                        if variance:
                            new_var = variance[0]
                
                if old_var and new_var:
                    info(f"Factor 1 variance: Old={old_var:.3f}, New={new_var:.3f}")
        else:
            info("No old results found for comparison")
            
    except Exception as e:
        warning(f"Could not compare with old methodology: {str(e)}")


def generate_report(results, output_dir):
    """Generate comprehensive analysis report."""
    bind(module="reporting")
    
    output_dir = Path(output_dir)
    report_path = output_dir / "analysis_report.md"
    
    with open(report_path, 'w') as f:
        f.write("# Three-Tier Analysis Report\n\n")
        f.write(f"Generated: {pd.Timestamp.now()}\n\n")
        
        # Executive Summary
        f.write("## Executive Summary\n\n")
        
        if 'summary' in results:
            summary = results['summary']
            if 'key_findings' in summary:
                f.write("### Key Findings\n\n")
                for finding in summary['key_findings']:
                    f.write(f"- {finding}\n")
                f.write("\n")
        
        # Tier Results
        f.write("## Detailed Results\n\n")
        
        # Tier 1
        if results.get('tier1'):
            f.write("### Tier 1: Pooled Panel Analysis\n\n")
            tier1 = results['tier1']
            if hasattr(tier1, 'summary'):
                f.write("```\n")
                f.write(tier1.summary())
                f.write("\n```\n\n")
        
        # Tier 2
        if results.get('tier2'):
            f.write("### Tier 2: Commodity-Specific Analysis\n\n")
            f.write("| Commodity | Integration Level | Threshold | R-squared |\n")
            f.write("|-----------|------------------|-----------|----------|\n")
            
            for commodity, res in results['tier2'].items():
                if isinstance(res, dict):
                    integration = res.get('integration_level', 'N/A')
                    threshold = res.get('threshold_value', 'N/A')
                    r2 = res.get('r_squared', 'N/A')
                    
                    if threshold != 'N/A' and isinstance(threshold, (int, float)):
                        threshold = f"{threshold:.2f}"
                    if r2 != 'N/A' and isinstance(r2, (int, float)):
                        r2 = f"{r2:.3f}"
                    
                    f.write(f"| {commodity} | {integration} | {threshold} | {r2} |\n")
            f.write("\n")
        
        # Tier 3
        if results.get('tier3'):
            f.write("### Tier 3: Validation Analysis\n\n")
            
            if 'pca_integration' in results['tier3']:
                pca_results = results['tier3']['pca_integration']
                if hasattr(pca_results, 'tier_specific'):
                    overall = pca_results.tier_specific.get('overall_integration', {})
                    if 'integration_level' in overall:
                        f.write(f"- Overall integration level: {overall['integration_level']}\n")
                    if 'pc1_variance_explained' in overall:
                        f.write(f"- PC1 variance explained: {overall['pc1_variance_explained']:.1%}\n")
            
            if 'conflict_validation' in results['tier3']:
                f.write("\n#### Conflict Impact\n")
                conflict_results = results['tier3']['conflict_validation']
                if hasattr(conflict_results, 'tier_specific'):
                    event_study = conflict_results.tier_specific.get('event_study', {})
                    if 'pct_events_reducing_integration' in event_study:
                        f.write(f"- {event_study['pct_events_reducing_integration']:.0f}% of conflicts reduce integration\n")
        
        f.write("\n## Methodology\n\n")
        f.write("This analysis uses the three-tier econometric methodology:\n\n")
        f.write("1. **Tier 1**: Pooled panel with entity and time fixed effects\n")
        f.write("2. **Tier 2**: Commodity-specific threshold models\n")
        f.write("3. **Tier 3**: Factor analysis and external validation\n\n")
        
        f.write("See `docs/models/yemen_panel_methodology.md` for details.\n")
    
    info(f"Report saved to {report_path}")


def main():
    """Run complete three-tier analysis with new methodology."""
    setup_logging("INFO")
    bind(module="main")
    
    try:
        # Load data
        df = load_panel_data()
        conflict_df = load_conflict_data()
        
        # Configure analysis
        config = configure_analysis()
        
        # Run analysis
        results = run_analysis(df, conflict_df, config)
        
        # Compare with old methodology
        compare_with_old_methodology(df, results)
        
        # Generate report
        generate_report(results, config['output_dir'])
        
        info("\n✅ Three-tier analysis complete with new methodology!")
        
        # Print migration note
        info("\n" + "="*60)
        info("MIGRATION NOTE:")
        info("This script uses the new three-tier implementation.")
        info("To migrate other scripts, use:")
        info("  from yemen_market.models.three_tier.integration import ThreeTierAnalysis")
        info("  from yemen_market.models.three_tier.migration import ModelMigrationHelper")
        info("="*60)
        
    except Exception as e:
        error(f"Analysis failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()