#!/usr/bin/env python3
"""
Run three-tier econometric analysis for Yemen market integration.

This script implements the complete three-tier methodology:
- Tier 1: Pooled panel regression with multi-way fixed effects
- Tier 2: Commodity-specific threshold VECMs
- Tier 3: Factor analysis for validation
"""

import sys
from pathlib import Path
import numpy as np
import pandas as pd
from linearmodels import PanelOLS
from sklearn.decomposition import PCA
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from yemen_market.utils.logging import (
    setup_logging, info, error, warning, timer, progress, 
    log_metric, log_data_shape, bind
)
from yemen_market.data import PanelBuilder
from yemen_market.models.track2_simple import SimpleThresholdVECM
from yemen_market.visualization import create_figure_directory


def load_panel_data():
    """Load integrated panel data."""
    bind(module="data_loading")
    
    with timer("load_panel_data"):
        panel_path = Path("data/processed/panels/integrated_panel.parquet")
        
        if not panel_path.exists():
            error(f"Panel data not found at {panel_path}")
            raise FileNotFoundError(f"Run build_panel_datasets.py first")
        
        df = pd.read_parquet(panel_path)
        log_data_shape("integrated_panel", df)
        
        # Validate structure
        required_cols = ['date', 'market', 'commodity', 'price_usd', 'conflict_intensity']
        missing = set(required_cols) - set(df.columns)
        if missing:
            error(f"Missing required columns: {missing}")
            raise ValueError(f"Panel missing required columns: {missing}")
        
        # Add log price
        df['log_price'] = np.log(df['price_usd'])
        
        # Add entity identifier
        df['entity'] = df['market'] + '_' + df['commodity']
        
        return df


def run_tier1_pooled_panel(df):
    """Tier 1: Pooled panel regression with multi-way fixed effects."""
    bind(module="tier1_pooled")
    info("Starting Tier 1: Pooled panel regression")
    
    with timer("tier1_analysis"):
        # Set up panel structure
        panel = df.set_index(['entity', 'date']).sort_index()
        
        # Define variables
        y = panel['log_price']
        X = panel[['conflict_intensity']]
        
        # Add controls if available
        control_vars = ['log_exchange_rate', 'rainfall_anomaly']
        available_controls = [v for v in control_vars if v in panel.columns]
        if available_controls:
            X = panel[['conflict_intensity'] + available_controls]
            info(f"Including controls: {available_controls}")
        
        # Add commodity dummies (excluding one for reference)
        commodities = df['commodity'].unique()
        for i, comm in enumerate(commodities[1:]):  # Skip first as reference
            X[f'commodity_{comm}'] = (df['commodity'] == comm).astype(int)
        
        # Estimate model with multi-way fixed effects
        info("Estimating pooled panel model...")
        model = PanelOLS(
            y, X,
            entity_effects=True,  # Market-commodity FE
            time_effects=True     # Time FE
        )
        
        # Fit with clustered standard errors
        results = model.fit(
            cov_type='clustered',
            cluster_entity=True
        )
        
        # Log key results
        coef = results.params['conflict_intensity']
        se = results.std_errors['conflict_intensity']
        pval = results.pvalues['conflict_intensity']
        
        log_metric("tier1_conflict_coef", coef)
        log_metric("tier1_conflict_se", se)
        log_metric("tier1_conflict_pval", pval)
        
        info(f"Conflict coefficient: {coef:.4f} (SE: {se:.4f}, p={pval:.4f})")
        
        # Economic interpretation
        effect_10_events = (np.exp(coef * 10) - 1) * 100
        info(f"10-event increase → {effect_10_events:.1f}% price increase")
        
        # Check fixed effects
        info(f"Entity FE F-stat: {results.f_statistic_entity:.2f}")
        info(f"Time FE F-stat: {results.f_statistic_time:.2f}")
        
        return results


def run_tier2_commodity_specific(df):
    """Tier 2: Commodity-specific threshold VECMs."""
    bind(module="tier2_commodity")
    info("Starting Tier 2: Commodity-specific analysis")
    
    commodities = ['Wheat', 'Rice', 'Sugar', 'Fuel']
    results = {}
    
    with progress("Commodity analysis", total=len(commodities)) as update:
        for commodity in commodities:
            try:
                with timer(f"tier2_{commodity.lower()}"):
                    info(f"Analyzing {commodity}...")
                    
                    # Extract commodity data
                    comm_df = df[df['commodity'] == commodity].copy()
                    
                    if len(comm_df) < 100:
                        warning(f"Insufficient data for {commodity}: {len(comm_df)} obs")
                        update(1)
                        continue
                    
                    # Create price matrix (time × markets)
                    price_matrix = comm_df.pivot(
                        index='date',
                        columns='market',
                        values='price_usd'
                    )
                    
                    # Create conflict matrix for threshold
                    conflict_matrix = comm_df.pivot(
                        index='date',
                        columns='market',
                        values='conflict_intensity'
                    )
                    
                    # Handle missing values
                    price_matrix = price_matrix.fillna(method='ffill', limit=2)
                    
                    # Average conflict across markets for threshold variable
                    threshold_var = conflict_matrix.mean(axis=1)
                    
                    # Initialize and fit threshold model
                    model = SimpleThresholdVECM(
                        n_lags=2,
                        n_coint=1,
                        threshold_variable='conflict'
                    )
                    
                    # Fit with threshold estimation
                    model.fit(
                        price_matrix,
                        threshold_variable=threshold_var,
                        estimate_threshold=True
                    )
                    
                    # Store results
                    if hasattr(model, 'threshold_value'):
                        results[commodity] = {
                            'threshold': model.threshold_value,
                            'low_regime_alpha': model.low_regime_results.alpha if hasattr(model, 'low_regime_results') else None,
                            'high_regime_alpha': model.high_regime_results.alpha if hasattr(model, 'high_regime_results') else None,
                            'observations': len(price_matrix)
                        }
                        
                        info(f"{commodity} threshold: {model.threshold_value:.1f} events/month")
                        
                        log_metric(f"tier2_{commodity.lower()}_threshold", model.threshold_value)
                    else:
                        warning(f"No threshold found for {commodity}")
                    
                    update(1)
                    
            except Exception as e:
                error(f"Error analyzing {commodity}: {str(e)}")
                update(1)
                continue
    
    return results


def run_tier3_factor_analysis(df):
    """Tier 3: Factor analysis for validation."""
    bind(module="tier3_factor")
    info("Starting Tier 3: Factor analysis")
    
    with timer("tier3_analysis"):
        # Create wide price matrix
        price_wide = df.pivot_table(
            index='date',
            columns=['market', 'commodity'],
            values='log_price'
        )
        
        log_data_shape("price_wide_matrix", price_wide)
        
        # Handle missing data
        price_filled = price_wide.fillna(method='ffill').fillna(method='bfill')
        
        # Drop columns with too many missing values
        missing_pct = price_filled.isna().mean()
        keep_cols = missing_pct[missing_pct < 0.3].index
        price_filled = price_filled[keep_cols]
        
        info(f"Retained {len(keep_cols)} of {len(price_wide.columns)} series")
        
        # Static factor analysis (PCA)
        info("Running PCA...")
        pca = PCA(n_components=5)
        factors_static = pca.fit_transform(price_filled.fillna(price_filled.mean()))
        
        var_explained = pca.explained_variance_ratio_
        cumsum_var = var_explained.cumsum()
        
        info(f"Variance explained by first 3 factors: {cumsum_var[2]:.1%}")
        for i in range(3):
            log_metric(f"tier3_factor{i+1}_variance", var_explained[i])
        
        # Dynamic factor model
        try:
            info("Running dynamic factor model...")
            dfm = DynamicFactor(
                price_filled,
                k_factors=2,
                factor_order=1
            )
            dfm_results = dfm.fit(disp=False)
            
            # Extract factors
            factors_dynamic = dfm_results.factors.filtered
            
            # Correlate with conflict
            conflict_avg = df.groupby('date')['conflict_intensity'].mean()
            
            # Align indices
            common_dates = factors_dynamic.index.intersection(conflict_avg.index)
            
            if len(common_dates) > 0:
                corr1 = factors_dynamic.loc[common_dates].iloc[:, 0].corr(
                    conflict_avg.loc[common_dates]
                )
                corr2 = factors_dynamic.loc[common_dates].iloc[:, 1].corr(
                    conflict_avg.loc[common_dates]
                )
                
                info(f"Factor 1 conflict correlation: {corr1:.3f}")
                info(f"Factor 2 conflict correlation: {corr2:.3f}")
                
                log_metric("tier3_factor1_conflict_corr", corr1)
                log_metric("tier3_factor2_conflict_corr", corr2)
                
                # Interpretation
                if abs(corr1) > 0.5 or abs(corr2) > 0.5:
                    info("Strong conflict factor detected")
                else:
                    info("Factors likely capture national trends")
            
        except Exception as e:
            warning(f"Dynamic factor model failed: {str(e)}")
            warning("Continuing with PCA results only")
        
        return {
            'pca': pca,
            'factors_static': factors_static,
            'variance_explained': var_explained
        }


def compare_results(tier1_results, tier2_results, tier3_results):
    """Compare and validate results across tiers."""
    bind(module="comparison")
    info("Comparing results across tiers")
    
    # Extract tier 1 conflict effect
    tier1_effect = tier1_results.params['conflict_intensity']
    
    # Calculate average tier 2 effect (if available)
    if tier2_results:
        threshold_effects = []
        for commodity, results in tier2_results.items():
            if results.get('low_regime_alpha') and results.get('high_regime_alpha'):
                effect = abs(results['high_regime_alpha'] - results['low_regime_alpha'])
                threshold_effects.append(effect)
        
        if threshold_effects:
            tier2_avg_effect = np.mean(threshold_effects)
            info(f"Average threshold effect: {tier2_avg_effect:.3f}")
        else:
            tier2_avg_effect = None
    else:
        tier2_avg_effect = None
    
    # Check consistency
    info("\n=== Cross-Tier Validation ===")
    info(f"Tier 1 conflict effect: {tier1_effect:.4f}")
    if tier2_avg_effect:
        info(f"Tier 2 average effect: {tier2_avg_effect:.4f}")
    
    if tier3_results and 'variance_explained' in tier3_results:
        info(f"Tier 3 common variation: {tier3_results['variance_explained'][0]:.1%}")
    
    # Generate summary report
    summary = {
        'tier1': {
            'conflict_coefficient': tier1_effect,
            'significance': tier1_results.pvalues['conflict_intensity'] < 0.05
        },
        'tier2': {
            'commodities_analyzed': len(tier2_results),
            'average_threshold': np.mean([r['threshold'] for r in tier2_results.values() if 'threshold' in r]) if tier2_results else None
        },
        'tier3': {
            'variance_factor1': tier3_results['variance_explained'][0] if tier3_results else None,
            'conflict_correlation': 'Strong' if tier3_results else 'Not computed'
        }
    }
    
    return summary


def save_results(results, output_dir):
    """Save results to files."""
    bind(module="save_results")
    
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save summary
    summary_path = output_dir / "three_tier_results_summary.json"
    pd.Series(results).to_json(summary_path, indent=2)
    info(f"Results saved to {summary_path}")


def main():
    """Run complete three-tier analysis."""
    setup_logging("INFO")
    bind(module="main")
    
    info("="*60)
    info("Three-Tier Econometric Analysis for Yemen Market Integration")
    info("="*60)
    
    try:
        # Load data
        df = load_panel_data()
        
        # Run all tiers
        info("\n" + "="*40)
        tier1_results = run_tier1_pooled_panel(df)
        
        info("\n" + "="*40)
        tier2_results = run_tier2_commodity_specific(df)
        
        info("\n" + "="*40)
        tier3_results = run_tier3_factor_analysis(df)
        
        # Compare results
        info("\n" + "="*40)
        summary = compare_results(tier1_results, tier2_results, tier3_results)
        
        # Save results
        save_results(summary, "results/three_tier_analysis")
        
        info("\n✅ Three-tier analysis complete!")
        
    except Exception as e:
        error(f"Analysis failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()