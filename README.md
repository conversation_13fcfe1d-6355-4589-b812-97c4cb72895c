# Yemen Market Integration Analysis

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Overview

This repository implements econometric analysis of market integration in conflict-affected Yemen, examining how dual exchange rates and territorial fragmentation affect commodity price transmission using a three-tier panel methodology.

## Installation

```bash
# Clone the repository
git clone https://github.com/worldbank/yemen-market-integration.git
cd yemen-market-integration

# Create conda environment
conda env create -f environment.yml
conda activate yemen-market

# Install package in development mode
pip install -e .
```

## Quick Start

```bash
# 1. Download data
python scripts/data_collection/download_data.py
python scripts/data_collection/download_acled_data.py

# 2. Process data
python scripts/data_processing/process_wfp_data.py
python scripts/data_processing/process_acaps_data.py
python scripts/data_processing/process_acled_data.py
python scripts/data_processing/run_spatial_joins.py

# 3. Build analysis datasets
python scripts/analysis/build_panel_datasets.py

# 4. Run econometric models
python scripts/analysis/run_three_tier_models.py

# 5. Run tests
pytest tests/
```

## Project Structure

```
yemen-market-integration/
├── data/                   # Data directories (gitignored)
│   ├── raw/               # Downloaded from HDX
│   ├── interim/           # Intermediate processing
│   └── processed/         # Analysis-ready datasets
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   ├── guides/            # User guides
│   ├── models/            # Model specifications
│   └── data/              # Data documentation
├── notebooks/             # Jupyter notebooks for analysis
├── reports/               # Generated outputs
│   ├── figures/          # Visualizations
│   └── progress/         # Project progress tracking
├── scripts/              # Executable scripts
│   ├── data_collection/  # Data download scripts
│   ├── data_processing/  # Processing scripts
│   ├── analysis/         # Analysis scripts
│   └── utilities/        # Utility scripts
├── src/yemen_market/     # Main package
│   ├── config/          # Configuration
│   ├── data/            # Data processing modules
│   ├── models/          # Econometric models
│   ├── utils/           # Utilities (enhanced logging)
│   └── visualization/   # Plotting functions
└── tests/               # Unit and integration tests
```

## Key Components

### Data Pipeline

- **HDXClient**: Fetch humanitarian data from HDX platform
- **WFPProcessor**: Process World Food Programme price data
- **ACAPSProcessor**: Process territorial control data
- **ACLEDProcessor**: Process conflict event data
- **SpatialJoiner**: Map markets to control zones
- **PanelBuilder**: Create analysis-ready panel datasets

### Econometric Models (Three-Tier Approach)

- **Tier 1**: Pooled panel regression with multi-way fixed effects
- **Tier 2**: Commodity-specific threshold VECMs
- **Tier 3**: Factor analysis for validation

### Documentation

📍 **New to the project?** See our [Documentation Map](./docs/DOCUMENTATION_MAP.md) for easy navigation!

- [Methodology](./METHODOLOGY.md) - Technical approach
- [API Reference](./docs/api/README.md) - Complete API documentation
- [User Guides](./docs/guides/) - How-to guides
- [Data Documentation](./docs/data/) - Data pipeline details
- [Contributing](./CONTRIBUTING.md) - How to contribute

## Data Sources

- **World Food Programme (WFP)**: Weekly commodity prices and exchange rates
- **ACAPS Yemen Analysis Hub**: Bi-weekly territorial control data
- **ACLED**: Conflict event data
- **HDX**: Administrative boundaries and infrastructure

## Usage Examples

### Basic Analysis

```python
from yemen_market.data import PanelBuilder
from yemen_market.models import ThreeTierAnalysis

# Load integrated panel
panel = PanelBuilder().load_integrated_panel()

# Run three-tier analysis
analysis = ThreeTierAnalysis()
results = analysis.fit(panel)

# View results
print(results.tier1_summary())  # Pooled panel results
print(results.tier2_summary())  # Commodity-specific results
print(results.tier3_summary())  # Factor analysis results
```

### Custom Analysis

See [notebooks/](./notebooks/) for detailed examples and [docs/guides/](./docs/guides/) for comprehensive tutorials.

## Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/

# Run with coverage
pytest --cov=yemen_market --cov-report=html
```

## Contributing

Please see [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines on contributing to this project.

## License

This project is licensed under the MIT License - see [LICENSE](./LICENSE) for details.

## Citation

If you use this code in your research, please cite:

```bibtex
@software{yemen_market_integration,
  title={Yemen Market Integration Analysis},
  author={World Bank Development Research Group},
  year={2025},
  url={https://github.com/worldbank/yemen-market-integration}
}
```

## Contact

For questions or issues, please open an issue on GitHub or contact the maintainers.
