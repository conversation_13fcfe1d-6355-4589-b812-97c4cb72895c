Provide me with a quick context refresh for the Yemen Market Integration project:

This is an econometric analysis project examining market integration in conflict-affected Yemen using a three-tier methodology:
- Tier 1: Pooled panel regression (linearmodels.PanelOLS)
- Tier 2: Commodity-specific threshold VECM
- Tier 3: PCA/factor validation

Key rules:
- ALWAYS use enhanced logging from yemen_market.utils.logging (never print/basic logging)
- NEVER create temporary files
- COMPLETE all steps (no shortcuts)
- Target 100% test coverage

Run `make update-claude-commands` to get the latest progress and status.