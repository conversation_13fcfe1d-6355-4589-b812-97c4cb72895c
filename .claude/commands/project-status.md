Generate a comprehensive status report for the Yemen Market Integration project and update my context with the current state. Include:

1. Current progress percentage from reports/progress/README.md
2. Test coverage statistics (file count, line count, coverage percentage)
3. Implementation status of all three-tier models
4. Recent git commits and current branch
5. Active sprint and focus areas from .claude/ACTIVE_CONTEXT.md
6. Next steps and priorities

Then run the automated update script to refresh all context files:
```bash
make update-claude-commands
```

After updating, provide me with the key information I need to continue working effectively on this project.