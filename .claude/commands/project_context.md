# Claude Commands - Yemen Market Integration
# Auto-generated: 2025-05-28 22:44:40
# Run 'make update-claude-commands' to refresh

## 🚀 Initialize New Claude Session

Copy and paste this entire block to initialize a new Claude session:

```
Initialize Yemen Market Integration project context:

PROJECT: Econometric analysis of market integration in conflict-affected Yemen
PURPOSE: Analyze how dual exchange rates and territorial fragmentation affect commodity prices
STATUS: 88% complete - Implementation
APPROACH: Three-tier models - (1) Pooled panel regression, (2) Commodity-specific VECM, (3) Factor analysis

IMPLEMENTATION STATUS:
✓ Tier 1: Pooled Panel Model
✓ Tier 2: Commodity-Specific Model  
✓ Tier 3: Validation/Factor Analysis
✓ Integration: Three-Tier Runner

TEST COVERAGE: 32 test files, 10,386 lines of test code
Coverage: 10% (report available)

RECENT PROGRESS:
- Complete | 100% | Week 2 | All components implemented |
- Complete | 100% | Week 4 | All tasks completed, 100% doc coverage |
- Complete | 100% | Week 6 | Both tracks implemented, 100% production-ready |

NEXT STEPS:
1. [ ] Run models on full dataset with all commodities
2. [ ] Implement policy simulation tools
3. [ ] Create presentation-ready visualizations
4. [ ] Write up methodology and results
5. [ ] Cross-validate Track 1 vs Track 2 results

CRITICAL DEVELOPMENT RULES:
1. ✓ Use enhanced logging from yemen_market.utils.logging (NOT print/logging)
2. ✓ Never create temporary files - only production-ready code
3. ✓ Complete all steps - no shortcuts or sampling
4. ✓ Maintain 100% test coverage
5. ✓ Follow documentation hierarchy strictly

CURRENT FOCUS: Unknown
GIT: Branch 'main', Last commit: 2025-05-28

KEY COMMANDS:
make week5-models     # Run three-tier analysis
make test            # Run all tests with coverage
make lint            # Check code style
make update-claude-commands  # Refresh this file

IMPORT PATTERN:
from yemen_market.utils.logging import info, timer, progress, log_data_shape
from yemen_market.models.three_tier import ThreeTierRunner
from yemen_market.data import PanelBuilder, WFPProcessor
```

## 📋 Quick Reference Card

### Project Status
- **Progress**: 88% complete
- **Phase**: Implementation
- **Models**: 4/4 implemented (100%)
- **Tests**: 32 files, 10,386 lines
- **Coverage**: 10%

### Essential Rules
```
ALWAYS: Enhanced logging | NO: Temp files | COMPLETE: All steps | TARGET: 100% coverage
```

### File Locations
```
Progress → reports/progress/README.md
Active → .claude/ACTIVE_CONTEXT.md  
Rules → CLAUDE.md
Methods → METHODOLOGY.md
Models → src/yemen_market/models/three_tier/
Tests → tests/unit/models/three_tier/
```

### Three-Tier Architecture
```
Tier 1: Pooled Panel (linearmodels.PanelOLS) → Average effects
Tier 2: Commodity VECM (threshold models) → Market-specific dynamics  
Tier 3: PCA/Factors (validation) → Latent patterns
```

### Enhanced Logging Pattern
```python
from yemen_market.utils.logging import info, timer, progress, bind

# Set context
bind(module=__name__)

# Time operations
with timer("operation_name"):
    # Show progress for loops
    with progress("Processing items", total=len(items)) as update:
        for item in items:
            # Your code here
            info("Processed", item=item.name, result=result)
            update(1)
```

## 🔧 Common Tasks

### Running Analysis
```bash
# Full three-tier analysis
make week5-models

# Quick model tests only
make test-models-quick

# Full test suite with coverage
make test

# Update this command file
make update-claude-commands
```

### Working with Data
```python
# Build panel dataset
from yemen_market.data import PanelBuilder
builder = PanelBuilder()
panel = builder.build_panel_dataset(
    start_date="2019-01-01",
    end_date="2024-12-31",
    commodities=["wheat", "rice", "sugar", "fuel"]
)

# Always log data shapes
from yemen_market.utils.logging import log_data_shape
log_data_shape("panel_data", panel)
```

### Testing Patterns
```python
# Tests define the contract - implement exactly what they expect
# Example: If test expects Tuple[bool, List[str]], return exactly that

# Run specific test
pytest tests/unit/models/three_tier/tier1_pooled/test_pooled_panel_model.py -xvs

# Check coverage for module
pytest --cov=yemen_market.models.three_tier --cov-report=term-missing
```

## 📊 Current Git Status
**Branch**: main  
**Recent Commits**:
- 29112a6 Refactor BaseModelConfig for flexible initialization
- fe03ed1 Refactor: Introduce dedicated config classes for models
- ed76162 feat: refactor three-tier analysis implementation in Week 5 notebook

## 🎯 Session Checklist

Before starting work:
- [ ] Reviewed this initialization (88% complete)
- [ ] Understand three-tier methodology
- [ ] Clear on enhanced logging requirement
- [ ] Know the no-temp-files rule
- [ ] Checked current focus: Unknown

## 💡 Remember

This project aims to inform World Bank policy for humanitarian interventions in Yemen. 
Every line of code should be production-ready, tested, and purposeful.

---
*Generated automatically by scripts/utilities/generate_claude_commands.py*
*Last updated: 2025-05-28 22:44:40*
