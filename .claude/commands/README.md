# Claude Commands Directory

This directory contains Claude Code slash commands for the Yemen Market Integration project.

## Available Commands

These commands are available in Claude Code by typing `/` followed by the command name:

### Core Commands
- `/project:init` - Initialize context for a new session
- `/project:refresh` - Refresh project context with latest state
- `/project:status` - Generate comprehensive status report
- `/project:quick-context` - Get a quick project overview
- `/project:test-status` - Check test coverage and status

### Generated Files
- `project_context.md` - Auto-generated unified context (DO NOT EDIT)

## Usage in Claude Code

1. Start Claude Code in the project directory:
   ```bash
   cd /Users/<USER>/Documents/GitHub/yemen-market-integration
   claude
   ```

2. Use slash commands:
   ```
   /project:init        # Start a new session
   /project:refresh     # Update context anytime
   /project:status      # Get detailed status
   ```

## Automation

The commands automatically:
- Extract current progress from `reports/progress/README.md`
- Count test files and coverage
- Check model implementation status
- Get recent git commits
- Update all context files

### Manual Update
```bash
make update-claude-commands
```

### Automatic Updates
- Post-merge git hook runs updates after `git pull`
- Commands run the update script when invoked

## Best Practices

1. **Start sessions with**: `/project:init`
2. **Refresh periodically with**: `/project:refresh`
3. **Check test status with**: `/project:test-status`
4. **Never manually edit** `project_context.md`

## Adding New Commands

Create new `.md` files in this directory. They become available as slash commands:
- `frontend/component.md` → `/project:frontend:component`
- `analysis.md` → `/project:analysis`

Use `$ARGUMENTS` for parameterized commands.