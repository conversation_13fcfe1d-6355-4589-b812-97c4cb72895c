Initialize my context for the Yemen Market Integration project by:

1. Running the automated context update:
```bash
make update-claude-commands
```

2. Reading the generated context from .claude/commands/project_context.md

3. Summarizing the current state including:
   - Project purpose and three-tier methodology
   - Current progress and implementation status
   - Critical development rules (enhanced logging, no temp files, complete implementations)
   - Active sprint focus and immediate next steps
   - Key commands and import patterns

4. Checking for any uncommitted changes or failing tests

5. Reminding me of the core project philosophy: This aims to inform World Bank policy for humanitarian interventions in Yemen through rigorous econometric analysis.