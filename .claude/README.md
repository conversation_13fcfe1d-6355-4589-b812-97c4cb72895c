# .claude Directory Structure

This directory contains Claude Code configuration and context for the Yemen Market Integration project.

## Directory Contents

### `/commands/`
Custom slash commands for common project tasks:
- `analyze-panel.md` - Analyze three-tier panel methodology
- `debug-data.md` - Debug panel data issues
- `generate-report.md` - Generate analysis reports
- `run-tier.md` - Run specific tiers of analysis

Usage: Type `/project:command-name` in Claude Code

### `/prompts/`
Reusable prompt templates for complex workflows:
- `initial_project_prompt.md` - Original project requirements
- `eda_phase_prompt.md` - EDA implementation guide
- `week5_testing_prompt.md` - Model testing procedures
- `panel_data_investigation_prompt.md` - Panel structure investigation

### `/models/`
Model-specific guides and documentation:
- `yemen-panel-guide.md` - Quick implementation guide for three-tier approach
- `claude-models-guide.md` - Claude-specific model guidance
- `diagnostic_testing_framework.md` - Testing framework details

### `/tasks/`
Current sprint planning:
- `week_5_6_sprint.md` - Active sprint tasks

### Root Files
- `ACTIVE_CONTEXT.md` - Current development context and immediate tasks
- `project_memory.md` - Technical memory and key decisions
- `methodology_notes.md` - Methodology implementation notes

### `/archive/`
Historical documents moved here to reduce clutter while preserving history.

## Best Practices

1. **Commands**: Keep commands focused and parameterized with `$ARGUMENTS`
2. **Prompts**: Use for complex, multi-step workflows
3. **Context**: Update ACTIVE_CONTEXT.md at the end of each session
4. **Archive**: Move completed work here monthly

## See Also
- Project context: `/CLAUDE.md`
- Methodology: `/METHODOLOGY.md`
- Progress tracking: `/reports/progress/README.md`