# Yemen Market Integration Analysis - Technical Memory

## Project Context
This project analyzes market integration in conflict-affected Yemen using threshold cointegration and spatial econometric techniques. We focus on how dual exchange rates and territorial fragmentation affect commodity price transmission.

## Key Data Sources
- WFP Yemen food prices (includes exchange rates per market)
- ACAPS areas of control (bi-weekly updates)
- HDX administrative boundaries
- ACLED conflict events (aggregated to market-month level)

## Core Methodological Approach (Three-Tier)
1. **Tier 1**: Pooled panel regression with multi-way fixed effects
2. **Tier 2**: Commodity-specific threshold VECMs
3. **Tier 3**: Factor analysis for validation

## Critical Implementation Details
- Markets mapped to control zones via spatial joins
- Exchange rate differentials calculated between zones
- Monthly frequency (not weekly) due to data constraints
- Bootstrap inference for all policy simulations
- Panel structure: market × commodity × time (3D)
- Entity definition: market-commodity pairs for Tier 1

## Technical Architecture Decisions

### Panel Data Structure Solution
- **Issue**: Multi-dimensional panel (market × commodity × time) incompatible with standard packages
- **Solution**: Three-tier methodology
  - Tier 1: Pool all data with market-commodity pairs as entities
  - Tier 2: Extract 2D panels by commodity
  - Tier 3: Wide matrix for factor analysis
- **Implementation**: linearmodels.PanelOLS for Tier 1, statsmodels for Tier 2

### Enhanced Logging System
- Structured JSON logging with context
- Timer contexts for performance tracking
- Progress bars for long operations
- Data shape logging after transformations
- Module-level context binding

### Data Processing Pipeline
- HDXClient with caching and rate limiting
- WFPProcessor with pcode standardization
- ACAPSProcessor handling nested ZIP structure
- SpatialJoiner for control zone mapping
- PanelBuilder for integrated datasets

## Key Technical Findings
- ACAPS data has nested ZIP with separate shapefiles (DFA.shp, IRG.shp, etc.)
- WFP exchange rates extracted from commodity names
- Maximum exchange rate differential: ~50% between zones
- Structural breaks at 2016, 2017, 2018 (Central Bank events)
- Panel coverage: 88.4% after smart filtering

## Important Configuration
- Repository uses src layout for better imports
- Testing with pytest, >90% coverage target
- NumPy style docstrings
- Black formatting (88 char limit)
- Conventional commits

## Common Commands
```bash
make install        # Set up environment
make test          # Run test suite
make lint          # Check code style
make format        # Format with black
make week5-models  # Run econometric models
```

## Known Limitations
- No fuel price data available from WFP
- Limited Central Bank data
- Conflict data is aggregated (not event-level)
- ACAPS temporal coverage limited to 2024

## MCP Servers Configuration
- filesystem (default) - Code development
- git - Version control
- github - Issue tracking
- fetch - API data collection

## Key Files
- `.claude/prompts/initial_project_prompt.md` - Original requirements
- `.claude/prompts/run_all_tests_phase_1_2_3.md` - Test execution guide
- `CLAUDE.md` - Development rules
- `METHODOLOGY.md` - Technical approach
- `docs/models/yemen_panel_methodology.md` - Three-tier implementation

## Development Sessions

### 2025-01-28: Comprehensive Test Creation
- **Completed**: Created comprehensive tests for all three phases of three-tier methodology
  - Phase 2 (Tier 2): 4 new test files (1,382 lines total)
  - Core components: 4 new test files (1,518 lines total)
  - Migration utilities: 1 new test file (370 lines)
- **Replaced**: Simple placeholder tests with full implementations
- **Removed**: 8 legacy test files from old dual-track approach
- **Key Decision**: Tests define the contract - implement exactly what they expect
- **Next Step**: Use test-driven development to implement all missing functionality