# Quick Start Guide: Week 5-6 Modeling Phase

## 🚀 Getting Started

You are implementing Week 5-6 of the Yemen Market Integration project. The EDA phase is complete with these key findings:

- **Conflict threshold**: 50 events/month (where market integration breaks down)
- **Price correlation**: 0.73 within zones, 0.31 between zones
- **Exchange rate differential**: Maximum 89% between zones
- **Spatial autocorrelation**: Moran's I = 0.42 (p < 0.001)

## 📂 Where to Work

```bash
cd /Users/<USER>/Documents/GitHub/yemen-market-integration

# Activate environment
conda activate yemen-market

# Key directories:
src/yemen_market/models/track1_complex/  # Complex models
src/yemen_market/models/track2_simple/   # Simple models
notebooks/04_models/                     # Model notebooks
```

## 🎯 Week 5 Daily Tasks

### Monday: Setup & Foundation
```python
# Morning: Create Track 1 Bayesian TVP-VECM
# File: src/yemen_market/models/track1_complex/tvp_vecm.py

# Afternoon: Create Track 2 Simple Threshold VECM  
# File: src/yemen_market/models/track2_simple/threshold_vecm.py

# Test both on small data subset
```

### Tuesday: Core Implementation
```python
# Complete both model implementations
# Run on Wheat data within Houthi zone only
# Generate first comparison table
```

### Wednesday: Threshold Testing
```python
# Implement Hansen threshold tests
# Validate 50-event threshold
# Test multiple thresholds for Track 1
```

### Thursday: Spatial Components
```python
# Add spatial weights to both tracks
# Test for spatial autocorrelation
# Run full diagnostic battery
```

### Friday: Results & Comparison
```python
# Create comprehensive results tables
# Run model comparison framework
# Generate diagnostic plots
```

## 💻 Key Code Templates

### Track 1: Complex Model
```python
from src.yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM

# Load data
panel_data = pd.read_parquet('data/processed/panels/integrated_panel.parquet')

# Filter to Houthi zone, Wheat only
houthi_wheat = panel_data[
    (panel_data['control_zone'] == 'Houthi') & 
    (panel_data['commodity'] == 'Wheat')
]

# Initialize model
model = BayesianTVPVECM(n_coint=1, n_lags=2)

# Fit model
model.fit(houthi_wheat)

# Run diagnostics
diagnostics = model.run_diagnostics()
```

### Track 2: Simple Model
```python
from src.yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM

# Initialize with 50-event threshold
model = SimpleThresholdVECM(threshold=50, n_lags=2)

# Fit model
model.fit(houthi_wheat, commodity='Wheat')

# Test threshold significance
threshold_test = model.test_threshold_significance()
```

### Model Comparison
```python
from src.yemen_market.models.model_comparison import run_model_comparison

# Compare both tracks
models = {
    'Track1_Complex': track1_model,
    'Track2_Simple': track2_model
}

comparison = run_model_comparison(models)
comparison.create_comparison_table()
```

## 🧪 Required Tests

### For Every Model:
1. **Unit root tests** (must pass)
2. **Cointegration tests** (must pass)
3. **Threshold significance** (core finding)
4. **Residual diagnostics** (serial correlation, heteroskedasticity)
5. **Spatial autocorrelation** (Moran's I on residuals)

### Test Implementation:
```python
from src.yemen_market.diagnostics.test_battery import run_all_tests

# Run comprehensive diagnostics
test_results = run_all_tests(model, data)

# Check for failures
if test_results['serial_correlation']['p_value'] < 0.05:
    print("WARNING: Serial correlation detected")
```

## 📊 Expected Outputs

By Friday, you should have:
1. **Both model tracks implemented and working**
2. **Diagnostic test results showing model adequacy**
3. **Threshold confirmed at ~50 events**
4. **Comparison table showing consistent results**
5. **Initial policy implications documented**

## ⚠️ Common Issues & Solutions

### PyMC Convergence Problems
```python
# Increase target_accept
model = BayesianTVPVECM(
    n_samples=4000,  # More samples
    n_chains=4,      # More chains
    target_accept=0.95  # Higher acceptance
)
```

### Insufficient Data in Regimes
```python
# Check regime sizes
low_regime_size = (conflict <= 50).sum()
high_regime_size = (conflict > 50).sum()

if min(low_regime_size, high_regime_size) < 50:
    # Adjust threshold or use different approach
```

### Spatial Weight Matrix Issues
```python
# Add small diagonal perturbation
W = W + np.eye(W.shape[0]) * 0.001
W = W / W.sum(axis=1, keepdims=True)  # Row-standardize
```

## 📝 Progress Tracking

Update daily in `.claude/models/week5_progress.md`:
```markdown
## Day X Progress
- [ ] Morning task completed
- [ ] Afternoon task completed
- [ ] Tests passing
- [ ] Results documented

Key findings:
- 
- 

Issues encountered:
- 
```

## 🔗 Resources

- [PyMC Documentation](https://www.pymc.io/)
- [statsmodels VECM](https://www.statsmodels.org/stable/generated/statsmodels.tsa.vector_ar.vecm.VECM.html)
- Hansen (1999) threshold testing paper
- `.claude/models/diagnostic_testing_framework.md` - Full test specifications

## 💡 Remember

1. **Start simple**: Get Track 2 working first
2. **Test frequently**: Run diagnostics after each change
3. **Document everything**: Keep notes on what works/fails
4. **Compare often**: Ensure both tracks give similar findings

The goal is to have **two independent approaches that validate each other**.

Good luck! The 50-event threshold is your key finding to confirm.