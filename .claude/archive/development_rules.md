# Development Rules for Yemen Market Integration

## 🚨 MANDATORY RULES - NO EXCEPTIONS 🚨

### Rule 1: Clean Codebase (Zero Tolerance for Clutter)

**BEFORE creating any file, ask yourself:**
1. Will this file be used in production? → If NO, don't create it
2. Is this a temporary test? → Use Jupyter notebook or delete after
3. Am I exploring an idea? → Use `examples/` folder
4. Is this replacing existing functionality? → Delete the old file FIRST

**File Creation Checklist:**
```
□ Has a clear, permanent purpose
□ Will be referenced by other modules
□ Belongs in its designated directory
□ Not duplicating existing functionality
□ Will be needed for final deliverables
```

**Cleanup Protocol:**
- After EVERY work session: Review created files
- Delete anything marked `temp_`, `test_`, `old_`, `backup_`
- If unsure about a file's value → It probably should be deleted

### Rule 2: Complete Implementation (No Shortcuts)

**NEVER simplify tasks because they're complex:**
```python
# ❌ WRONG: Processing subset because "it's faster"
for market in markets[:5]:  # NO! This is not representative
    process_market(market)

# ✅ CORRECT: Process everything with progress tracking
with progress("Processing all markets", total=len(markets)) as update:
    for market in markets:  # ALL markets, no exceptions
        process_market(market)
        update(1)
```

**When facing intensive operations:**
1. Add progress bars to show it's working
2. Log intermediate results
3. Save checkpoints if needed
4. But NEVER skip data or steps

**Examples of unacceptable shortcuts:**
- Sampling data instead of processing all
- Hardcoding values instead of calculating
- Skipping validation "because it takes time"
- Using simple models when complex ones are specified
- Processing only recent data when historical is required

### Rule 3: Enhanced Logging is Mandatory

**Import at the top of EVERY module:**
```python
from yemen_market.utils.logging import (
    info, debug, warning, error,
    timer, progress, log_data_shape,
    log_metric, bind
)

# First line after imports
bind(module=__name__)
```

**Required logging patterns:**

1. **Every function that processes data:**
```python
def process_data(df: pd.DataFrame) -> pd.DataFrame:
    with timer("process_data"):
        info(f"Processing {len(df)} rows")
        # ... processing ...
        log_data_shape("processed", result)
        return result
```

2. **Every loop over 10 items:**
```python
with progress("Processing items", total=len(items)) as update:
    for i, item in enumerate(items):
        debug(f"Processing item {i}")
        # ... work ...
        update(1)
```

3. **Every data transformation:**
```python
df_before_shape = df.shape
df = transformation(df)
info(f"Transformation complete: {df_before_shape} → {df.shape}")
log_data_shape("after_transformation", df)
```

### Rule 4: No Basic Print/Logging

**BANNED in production code:**
```python
print("Debug message")              # ❌ NEVER
logging.info("Starting process")    # ❌ NEVER
logger.debug("Value: " + str(x))    # ❌ NEVER
```

**ONLY use enhanced logging:**
```python
info("Starting process")            # ✅
debug(f"Value: {x}")               # ✅
error("Failed", error=str(e))      # ✅
```

### Rule 5: Git Hygiene

**Before EVERY commit:**
1. Check for temporary files: `git status`
2. Remove any files that shouldn't be permanent
3. Ensure no `print()` statements in code
4. Verify imports use enhanced logging

**Commit message must indicate cleanup:**
```bash
git commit -m "feat: add spatial analysis module

- Implemented SpatialAnalyzer class
- Added comprehensive unit tests
- Cleaned up temporary test files
- Uses enhanced logging throughout"
```

## Enforcement

**These rules are checked by:**
1. Code review before merging
2. Pre-commit hooks (when configured)
3. Unit tests that verify logging usage
4. Regular codebase audits

**Violations will require:**
1. Immediate cleanup
2. Rewriting code to comply
3. Additional review before acceptance

## Quick Reference Card

```
EVERY FILE:
✓ Permanent purpose
✓ Correct directory
✓ No duplication
✓ Enhanced logging
✗ No temp files
✗ No print()
✗ No shortcuts

EVERY LOOP:
✓ Process all items
✓ Progress bar if >10
✓ Log results
✗ No sampling
✗ No early exit

EVERY FUNCTION:
✓ Timer for slow ops
✓ Log data shapes
✓ Bind context
✗ No basic logging
✗ No silent failures
```

Remember: A clean, complete implementation is better than a quick, partial one. The enhanced logging system makes debugging easier, not harder - use it!
