# Yemen Panel Methodology Documentation Update Plan

## Overview
This document outlines the comprehensive documentation updates needed to reflect the Yemen Panel Methodology extracted from the PDF. The methodology addresses the fundamental challenge of analyzing 3D panel data (market × commodity × time) in a conflict-affected context.

## Core Methodology Summary

### Primary Approach: Pooled Panel Regression
- **Method**: Multi-way fixed effects with market, commodity, and time FE
- **Rationale**: Leverages full 3D variation while maintaining identification
- **Implementation**: LinearModels' PanelOLS with entity_effects=True, time_effects=True
- **Key Innovation**: Commodity FE capture heterogeneous responses to conflict

### Secondary Approach: Commodity-Specific Threshold VECMs
- **Method**: Separate 2D panels (market × time) for each commodity
- **Rationale**: Allows commodity-specific dynamics and thresholds
- **Implementation**: Statsmodels VECM with threshold estimation
- **Key Innovation**: Different conflict thresholds by commodity type

### Supporting Approach: Factor Analysis
- **Method**: Extract common factors from all commodities
- **Rationale**: Dimension reduction while preserving variation
- **Implementation**: PCA/Dynamic Factor Models
- **Key Innovation**: Validate pooled results against factor-based analysis

## Documentation Files to Update

### 1. `.claude/methodology_notes.md`
**Current State**: Contains old model hierarchy with Bayesian TVP-VECM as primary
**Updates Needed**:
- Replace model hierarchy with new three-tier approach
- Add section on 3D panel handling strategies
- Update threshold estimation to reflect commodity-specific approach
- Add factor analysis methodology
- Include formal notation for pooled panel specification

### 2. `.claude/ACTIVE_CONTEXT.md`
**Current State**: Shows panel structure issue as unresolved
**Updates Needed**:
- Mark panel structure as RESOLVED
- Update current working section with new methodology
- Add implementation status for each approach
- Update immediate next actions

### 3. `.claude/models/claude-models-guide.md`
**New File Needed**: Comprehensive guide to the three-tier methodology
**Content**:
- Formal econometric specifications
- Implementation details for each approach
- Code examples for data preparation
- Comparison of approaches

### 4. `.claude/prompts/panel_data_investigation_prompt.md`
**Current State**: Asks for solution to panel problem
**Updates Needed**:
- Add "RESOLVED" header with solution summary
- Include link to full methodology document
- Keep original for historical reference

### 5. `docs/models/worldbank_methodology_summary.md`
**Current State**: Focuses on threshold VECM approach
**Updates Needed**:
- Add new section on 3D panel methodology
- Update model hierarchy section
- Include pooled panel regression details
- Add factor analysis approach

### 6. `docs/models/panel_methodology.md`
**New File Needed**: Detailed technical documentation
**Content**:
- Mathematical notation for all three approaches
- Data transformation procedures
- Standard error corrections
- Software implementation details

### 7. `docs/guides/modeling_guide.md`
**Current State**: May not exist or be outdated
**Updates Needed**:
- Complete rewrite with new methodology
- Step-by-step implementation guide
- Code snippets for each approach
- Troubleshooting section

### 8. `.claude/tasks/week_5_6_sprint.md`
**Current State**: References old dual-track approach
**Updates Needed**:
- Update to reflect three-tier methodology
- Revise checklist items
- Update success metrics
- Adjust timeline if needed

## Key Technical Details to Document

### 1. Data Transformation
```python
# For pooled panel
long_data = panel_data.set_index(['market_id', 'commodity_id', 'date'])

# For commodity-specific
wheat_panel = panel_data[panel_data.commodity == 'Wheat'].pivot(
    index='date', columns='market_id', values='price_usd'
)

# For factor analysis
commodity_matrix = panel_data.pivot_table(
    index=['market_id', 'date'], 
    columns='commodity_id', 
    values='price_usd'
)
```

### 2. Model Specifications

#### Pooled Panel
```
P_imt = α_i + γ_m + δ_t + β₁Conflict_it + β₂(Conflict_it × Commodity_m) + ε_imt
```

#### Commodity-Specific VECM
```
ΔP_it^m = α_i^m + λ^m(P_{i,t-1}^m - βP_{j,t-1}^m) + Γ^m(L)ΔP_{t-1}^m + ε_it^m
```

#### Factor Model
```
P_imt = Λ_m F_it + e_imt
```

### 3. Estimation Strategy
1. **Pooled**: Multi-way clustered SEs at market and time levels
2. **Commodity**: HAC standard errors for each commodity
3. **Factor**: Bootstrap confidence intervals

### 4. Robustness Checks
- Driscoll-Kraay standard errors for pooled model
- Alternative factor extraction methods
- Sensitivity to commodity selection
- Temporal stability tests

## Implementation Priority

### Phase 1: Core Documentation (Immediate)
1. Update `.claude/ACTIVE_CONTEXT.md` with resolution
2. Create `docs/models/panel_methodology.md`
3. Update `.claude/methodology_notes.md`

### Phase 2: Technical Guides (This Week)
1. Create `.claude/models/yemen-panel-guide.md`
2. Update `docs/guides/modeling_guide.md`
3. Create implementation notebooks

### Phase 3: Integration (Next Week)
1. Update all model documentation
2. Revise sprint plans
3. Create validation scripts

## Validation Checklist
- [ ] All documentation internally consistent
- [ ] Code examples run without errors
- [ ] Mathematical notation properly formatted
- [ ] Clear decision tree for method selection
- [ ] Troubleshooting guide included
- [ ] References to academic literature

## Key Messages to Emphasize
1. **Problem Solved**: We have a clear, academically sound solution
2. **Three Complementary Approaches**: Not competing, but validating
3. **Publication Ready**: Methods meet World Bank standards
4. **Practical Implementation**: Clear code examples provided
5. **Flexible Framework**: Can adapt to different research questions

## Next Steps
1. Review and approve this plan
2. Execute Phase 1 updates immediately
3. Create tracking issue for remaining updates
4. Assign documentation tasks
5. Set review checkpoints

---
**Created**: 2025-01-28
**Status**: Ready for implementation
**Priority**: HIGH - Blocks model implementation