## File Organization
- **scripts/**: ONLY 5 essential pipeline scripts
- **src/**: Production library code only
- **tests/**: All test files
- **examples/**: Demo and exploratory code
- **NO temporary files anywhere in production directories**

## Performance Standards
- **Use timers** for operations >1 second
- **Use progress bars** for loops >10 iterations
- **Process all data** - no sampling for speed
- Log performance metrics

## Documentation Standards
- NumPy-style docstrings (see template)
- Include complexity warnings for slow operations
- Document why enhanced logging is used
- Explain any data filtering (should be minimal)# Coding Standards for Yemen Market Integration Project

## 🚨 MANDATORY: Read .claude/development_rules.md FIRST! 🚨

This document outlines coding standards. For strict development rules about file cleanup, complete implementation, and logging requirements, see `.claude/development_rules.md`.

## Python Style Guide
- Follow PEP 8 with 88-character line limit (Black formatter)
- Use type hints for all function parameters and return values
- NumPy-style docstrings for all public functions

## Naming Conventions
- **Modules/Packages**: `snake_case` (e.g., `threshold_vecm.py`)
- **Classes**: `PascalCase` (e.g., `ThresholdVECM`)
- **Functions**: `snake_case` (e.g., `calculate_exchange_differential`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `DEFAULT_THRESHOLD`)

## Import Order
1. Standard library imports
2. Third-party imports
3. **Enhanced logging imports** (ALWAYS include these)
4. Local imports
(Separated by blank lines)

```python
# Standard library
import json
from pathlib import Path

# Third-party
import pandas as pd
import numpy as np

# Enhanced logging (MANDATORY)
from yemen_market.utils.logging import (
    info, debug, warning, error,
    timer, progress, log_data_shape,
    bind
)

# Local imports
from yemen_market.models.base import BaseModel

# First line after imports
bind(module=__name__)
```

## Docstring Template
```python
def function_name(param1: type1, param2: type2) -> return_type:
    """Brief description.
    
    Longer description if needed.
    
    Parameters
    ----------
    param1 : type1
        Description of param1
    param2 : type2
        Description of param2
        
    Returns
    -------
    return_type
        Description of return value
        
    Examples
    --------
    >>> function_name(x, y)
    expected_output
    """
```

## Error Handling
- Use specific exceptions (not bare `except:`)
- **Log all errors with enhanced logging**
- Provide informative error messages
- Never silently catch exceptions

```python
try:
    result = risky_operation()
except SpecificError as e:
    error("Operation failed", 
          operation="risky_operation",
          error_type=type(e).__name__,
          details=str(e))
    raise  # Re-raise after logging
```

## Testing Requirements
- All public functions must have unit tests
- Use pytest fixtures for shared test data
- Aim for >90% code coverage
- **Tests must verify enhanced logging is used**
- No temporary test files in version control
