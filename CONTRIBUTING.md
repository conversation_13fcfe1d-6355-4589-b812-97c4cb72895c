# Contributing to Yemen Market Integration

Thank you for your interest in contributing to the Yemen Market Integration project! This guide explains how to contribute effectively while maintaining our high standards.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Development Workflow](#development-workflow)
3. [Code Standards](#code-standards)
4. [Documentation Requirements](#documentation-requirements)
5. [Testing Guidelines](#testing-guidelines)
6. [Pull Request Process](#pull-request-process)

## Getting Started

### Prerequisites

- Python 3.10+
- Git
- Conda or virtual environment
- Claude Code (optional but recommended)

### Setup

```bash
# Clone the repository
git clone https://github.com/worldbank/yemen-market-integration.git
cd yemen-market-integration

# Create environment
conda env create -f environment.yml
conda activate yemen-market

# Install in development mode
pip install -e .

# Run tests to verify setup
pytest tests/
```

## Development Workflow

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Follow the Three-Tier Methodology

All contributions should align with our three-tier approach:
- **Tier 1**: Pooled panel regression (primary analysis)
- **Tier 2**: Commodity-specific models (supporting evidence)
- **Tier 3**: Factor analysis (validation)

### 3. Use Enhanced Logging

**MANDATORY**: All code must use our enhanced logging system:

```python
from yemen_market.utils.logging import (
    info, timer, progress, log_data_shape, bind
)

# Set module context
bind(module=__name__)

# Time operations
with timer("operation_name"):
    result = process_data()

# Show progress
with progress("Processing", total=len(items)) as update:
    for item in items:
        process(item)
        update(1)

# Log data shapes
log_data_shape("dataframe_name", df)
```

**NEVER** use `print()` or basic `logging`.

### 4. Keep the Codebase Clean

- **NO** temporary test files
- **NO** exploratory scripts in production directories
- **NO** incomplete implementations
- Use `examples/` for demos
- Delete failed attempts immediately

## Code Standards

### Style Guide

- **Formatter**: Black (88 char limit)
- **Linting**: flake8
- **Type hints**: Required for all functions
- **Docstrings**: NumPy style

```python
def calculate_integration_metric(
    prices: pd.DataFrame,
    conflict: pd.Series,
    threshold: float = 50.0
) -> Dict[str, float]:
    """
    Calculate market integration metrics.
    
    Parameters
    ----------
    prices : pd.DataFrame
        Price data with markets as columns
    conflict : pd.Series
        Conflict intensity time series
    threshold : float, optional
        Conflict threshold for regime switching
        
    Returns
    -------
    Dict[str, float]
        Integration metrics by regime
    """
```

### Commit Messages

Use conventional commits:
- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation only
- `test:` Test additions/changes
- `refactor:` Code restructuring
- `chore:` Maintenance tasks

Example: `feat: add Driscoll-Kraay standard errors to Tier 1`

## Documentation Requirements

### Where to Document

| Content Type | Location |
|-------------|----------|
| API changes | `docs/api/` |
| New methods | `docs/models/` |
| User guides | `docs/guides/` |
| Progress | `reports/progress/README.md` |

### Documentation Standards

1. **Update existing docs** rather than creating new files
2. **Link to methodology** in `docs/models/yemen_panel_methodology.md`
3. **Include examples** for new features
4. **Test documentation** build before submitting

## Testing Guidelines

### Test Coverage

- Minimum 90% coverage for new code
- Test both success and failure cases
- Include edge cases

### Running Tests

```bash
# All tests
pytest

# Specific module
pytest tests/unit/test_panel_builder.py

# With coverage
pytest --cov=yemen_market --cov-report=html
```

### Writing Tests

```python
def test_three_tier_integration():
    """Test three-tier analysis integration."""
    # Arrange
    data = create_test_panel_data()
    
    # Act
    results = run_three_tier_analysis(data)
    
    # Assert
    assert results['tier1'] is not None
    assert results['tier2']['wheat']['threshold'] > 0
    assert results['tier3']['variance_explained'] > 0.5
```

## Pull Request Process

### Before Submitting

1. **Run all tests**: `pytest`
2. **Check code style**: `black .` and `flake8`
3. **Update documentation** if needed
4. **Add to CHANGELOG.md** if applicable
5. **Verify no temporary files** are included

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement

## Testing
- [ ] All tests pass
- [ ] Added new tests
- [ ] Coverage maintained >90%

## Documentation
- [ ] Updated relevant docs
- [ ] Added examples if needed

## Checklist
- [ ] Used enhanced logging
- [ ] No temporary files
- [ ] Follows three-tier methodology
- [ ] Conventional commit messages
```

### Review Process

1. Automated tests must pass
2. Code review by maintainer
3. Documentation review if applicable
4. Merge after approval

## Getting Help

### Resources

- **Methodology**: See `METHODOLOGY.md`
- **Development rules**: See `CLAUDE.md`
- **API docs**: See `docs/api/`
- **Examples**: See `examples/`

### Questions?

- Open an issue for bugs/features
- Use discussions for questions
- Check existing issues first

## Recognition

Contributors will be acknowledged in:
- `AUTHORS.md`
- Release notes
- Academic publications (if applicable)

Thank you for contributing to research that helps understand market dynamics in conflict-affected regions!