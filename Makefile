.PHONY: help install test lint format clean download-data process-data pipeline analysis report all week5-models quick-test check-deps

# Detect if we're using venv or conda
PYTHON := $(if $(VIRTUAL_ENV),python,conda run -n yemen-market python)
PIP := $(if $(VIRTUAL_ENV),pip,conda run -n yemen-market pip)
PYTEST := $(if $(VIRTUAL_ENV),pytest,conda run -n yemen-market pytest)
RUFF := $(if $(VIRTUAL_ENV),ruff,conda run -n yemen-market ruff)
BLACK := $(if $(VIRTUAL_ENV),black,conda run -n yemen-market black)
MYPY := $(if $(VIRTUAL_ENV),mypy,conda run -n yemen-market mypy)

help:
	@echo "Yemen Market Integration Analysis - Available Commands:"
	@echo "  make install       Install dependencies and package"
	@echo "  make test          Run test suite"
	@echo "  make lint          Check code style"
	@echo "  make format        Format code with black"
	@echo "  make download-data Download initial datasets from HDX"
	@echo "  make process-data  Run complete data processing pipeline"
	@echo "  make pipeline      Run full data pipeline (download + process)"
	@echo "  make analysis      Run econometric analysis (future)"
	@echo "  make report        Generate final report (future)"
	@echo "  make week5-models  Run Week 5 dual-track models ⭐ NEW"
	@echo "  make quick-test    Test models with synthetic data ⭐ NEW"
	@echo "  make check-deps    Check dependencies for models ⭐ NEW"
	@echo "  make update-claude-commands Update Claude context files ⭐ NEW"
	@echo "  make clean         Remove generated files"

install:
	@if [ -z "$(VIRTUAL_ENV)" ]; then \
		echo "Using conda environment..."; \
		conda env create -f environment.yml; \
		conda run -n yemen-market pip install -e ".[dev]"; \
		conda run -n yemen-market pre-commit install; \
	else \
		echo "Using virtual environment..."; \
		pip install -e ".[dev]"; \
		pre-commit install; \
	fi

test:
	$(PYTEST) tests/ -v --cov=yemen_market

test-coverage:
	$(PYTEST) tests/ -v --cov=yemen_market --cov-report=html
	@echo "Coverage report generated in htmlcov/index.html"

lint:
	$(RUFF) check src/ tests/
	$(MYPY) src/

format:
	$(BLACK) src/ tests/ scripts/
	$(RUFF) check src/ tests/ --fix

download-data:
	$(PYTHON) scripts/download_data.py

process-wfp:
	$(PYTHON) scripts/process_wfp_data.py

process-acaps:
	$(PYTHON) scripts/process_acaps_data.py

process-acled:
	$(PYTHON) scripts/process_acled_data.py

spatial-joins:
	$(PYTHON) scripts/run_spatial_joins.py

build-panels:
	$(PYTHON) scripts/build_panel_datasets.py

process-data: process-wfp process-acaps process-acled spatial-joins build-panels
	@echo "Data processing pipeline complete!"

pipeline: download-data process-data
	@echo "Full data pipeline complete!"

# Future implementation
analysis:
	@echo "Econometric analysis not yet implemented"
	@echo "Run: $(PYTHON) scripts/run_analysis.py"

# Future implementation
report:
	@echo "Report generation not yet implemented"
	@echo "Run: $(PYTHON) scripts/generate_report.py"

clean:
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache/ .coverage htmlcov/
	rm -rf data/interim/* data/processed/*
	rm -rf reports/figures/* reports/tables/*

clean-cache:
	rm -rf data/raw/*/cache_info.json
	@echo "Cache metadata cleared (data files retained)"

all: install pipeline
	@echo "Installation and data pipeline complete!"

# Week 5 Sprint Implementation
week5-models:
	@echo "Running Week 5 dual-track econometric models..."
	$(PYTHON) scripts/analysis/run_week5_models.py

quick-test:
	@echo "Running quick model test with synthetic data..."
	$(PYTHON) scripts/test_models_quick.py

check-deps:
	@echo "Checking dependencies for Week 5 models..."
	$(PYTHON) scripts/check_dependencies.py

run-pipeline:
	@echo "Running complete data pipeline..."
	$(PYTHON) scripts/run_data_pipeline.py

# Claude context management
update-claude-commands:
	@echo "Updating Claude command context..."
	$(PYTHON) scripts/utilities/generate_claude_commands.py